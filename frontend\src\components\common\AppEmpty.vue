<template>
  <div class="app-empty">
    <div class="empty-content">
      <div class="empty-icon">
        <el-icon :size="iconSize" :color="iconColor">
          <component :is="icon" />
        </el-icon>
      </div>
      <div class="empty-text">
        <div class="empty-title">{{ title }}</div>
        <div class="empty-description" v-if="description">{{ description }}</div>
      </div>
      <div class="empty-actions" v-if="$slots.actions">
        <slot name="actions" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Box, DocumentDelete, Search, Folder, Collection } from '@element-plus/icons-vue'

interface Props {
  type?: 'default' | 'search' | 'folder' | 'bookmark' | 'trash'
  title?: string
  description?: string
  iconSize?: number
  iconColor?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  iconSize: 80,
  iconColor: '#c0c4cc'
})

const iconMap = {
  default: Box,
  search: Search,
  folder: Folder,
  bookmark: Collection,
  trash: DocumentDelete
}

const titleMap = {
  default: '暂无数据',
  search: '未找到相关内容',
  folder: '暂无文件夹',
  bookmark: '暂无书签',
  trash: '回收站为空'
}

const descriptionMap = {
  default: '当前没有任何数据',
  search: '尝试调整搜索条件',
  folder: '还没有创建任何文件夹',
  bookmark: '还没有添加任何书签',
  trash: '回收站中没有任何项目'
}

const icon = computed(() => iconMap[props.type])
const title = computed(() => props.title || titleMap[props.type])
const description = computed(() => props.description || descriptionMap[props.type])
</script>

<style scoped>
.app-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  padding: 40px 20px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  margin-bottom: 20px;
}

.empty-title {
  font-size: 16px;
  color: #909399;
  margin-bottom: 8px;
  font-weight: 500;
}

.empty-description {
  font-size: 14px;
  color: #c0c4cc;
  line-height: 1.5;
  margin-bottom: 20px;
}

.empty-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>
