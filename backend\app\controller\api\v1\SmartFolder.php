<?php

namespace app\controller\api\v1;

use app\BaseController;
use app\service\SmartFolderService;
use app\validate\SmartFolderValidate;
use app\exception\BusinessException;
use think\Response;

/**
 * 智能文件夹控制器
 *
 * 提供智能文件夹的CRUD操作，基于条件自动筛选书签
 */
class SmartFolder extends BaseController
{
    /**
     * 智能文件夹服务
     * @var SmartFolderService
     */
    protected $smartFolderService;

    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->smartFolderService = new SmartFolderService();
    }

    /**
     * 获取智能文件夹列表
     *
     * 获取当前用户的智能文件夹列表，包含每个文件夹匹配的书签数量
     *
     * @route GET /api/v1/smart-folders
     * @middleware auth
     * @param string $search 搜索关键词，可选
     * @param string $order_by 排序字段：sort_order|name|created_at，默认sort_order
     * @param string $order_dir 排序方向：asc|desc，默认asc
     * @return Response JSON响应，包含智能文件夹列表
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/smart-folders?search=技术&order_by=name&order_dir=asc
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取智能文件夹列表成功",
     *   "data": [
     *     {
     *       "id": 1,
     *       "name": "技术文章",
     *       "icon": "🔧",
     *       "conditions": [...],
     *       "bookmark_count": 25,
     *       "sort_order": 1,
     *       "created_at": "2024-06-30 12:00:00"
     *     }
     *   ]
     * }
     */
    public function index(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $params = $this->request->get();
            $smartFolders = $this->smartFolderService->getSmartFolderList($userId, $params);

            return $this->success($smartFolders, '获取智能文件夹列表成功');
        } catch (\Exception $e) {
            return $this->error('获取智能文件夹列表失败：' . $e->getMessage());
        }
    }

    /**
     * 创建智能文件夹
     *
     * 创建新的智能文件夹，基于指定条件自动筛选书签
     *
     * @route POST /api/v1/smart-folders
     * @middleware auth
     * @param string $name 文件夹名称，必填，1-100个字符
     * @param string $icon 图标，可选，emoji或图标代码
     * @param array $conditions 搜索条件，必填，最多10个条件
     * @return Response JSON响应，包含创建的智能文件夹信息
     * @throws BusinessException 参数验证失败或名称重复
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/smart-folders
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "name": "技术文章",
     *   "icon": "🔧",
     *   "conditions": [
     *     {
     *       "field": "tags",
     *       "operator": "contains",
     *       "value": "技术"
     *     },
     *     {
     *       "field": "is_star",
     *       "operator": "equals",
     *       "value": true
     *     }
     *   ]
     * }
     * Response: {
     *   "code": 201,
     *   "message": "创建智能文件夹成功",
     *   "data": {
     *     "id": 1,
     *     "name": "技术文章",
     *     "icon": "🔧",
     *     "conditions": [...],
     *     "sort_order": 1,
     *     "created_at": "2024-06-30 12:00:00"
     *   }
     * }
     */
    public function create(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            // 参数验证
            $data = $this->request->post();
            $this->validate($data, SmartFolderValidate::class . '.create');

            // 创建智能文件夹
            $smartFolder = $this->smartFolderService->createSmartFolder($userId, $data);

            return $this->created($smartFolder->toApiArray(), '创建智能文件夹成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('创建智能文件夹失败：' . $e->getMessage());
        }
    }

    /**
     * 获取智能文件夹内容
     *
     * 获取智能文件夹中匹配条件的书签列表，支持分页和额外搜索
     *
     * @route GET /api/v1/smart-folders/{id}/bookmarks
     * @middleware auth
     * @param int $id 智能文件夹ID，路径参数
     * @param string $search 额外搜索关键词，可选
     * @param int $page 页码，默认1
     * @param int $limit 每页数量，默认20，最大100
     * @param string $order_by 排序字段：created_at|title|visit_count，默认created_at
     * @param string $order_dir 排序方向：asc|desc，默认desc
     * @return Response JSON响应，包含智能文件夹信息和书签列表
     * @throws BusinessException 智能文件夹不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/smart-folders/1/bookmarks?page=1&limit=20&search=GitHub
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取智能文件夹内容成功",
     *   "data": {
     *     "smart_folder": {
     *       "id": 1,
     *       "name": "技术文章",
     *       "conditions": [...]
     *     },
     *     "list": [...],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 25,
     *       "last_page": 2
     *     }
     *   }
     * }
     */
    public function bookmarks(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $smartFolderId = (int)$this->request->param('id');
            $params = $this->request->get();

            $result = $this->smartFolderService->getSmartFolderBookmarks($userId, $smartFolderId, $params);

            return $this->paginate($result['items'], $result['meta'], '获取智能文件夹内容成功', $result['smart_folder']);

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('获取智能文件夹内容失败：' . $e->getMessage());
        }
    }

    /**
     * 更新智能文件夹
     *
     * 更新智能文件夹的名称、图标或搜索条件
     *
     * @route PUT /api/v1/smart-folders/{id}
     * @middleware auth
     * @param int $id 智能文件夹ID，路径参数
     * @param string $name 文件夹名称，可选，1-100个字符
     * @param string $icon 图标，可选，emoji或图标代码
     * @param array $conditions 搜索条件，可选，最多10个条件
     * @return Response JSON响应，包含更新后的智能文件夹信息
     * @throws BusinessException 智能文件夹不存在、参数验证失败或名称重复
     * @throws \Exception 系统异常
     * @example
     * PUT /api/v1/smart-folders/1
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "name": "技术资源",
     *   "icon": "📚",
     *   "conditions": [
     *     {
     *       "field": "tags",
     *       "operator": "contains",
     *       "value": "技术"
     *     }
     *   ]
     * }
     * Response: {
     *   "code": 200,
     *   "message": "更新智能文件夹成功",
     *   "data": {
     *     "id": 1,
     *     "name": "技术资源",
     *     "icon": "📚",
     *     "conditions": [...],
     *     "updated_at": "2024-06-30 12:30:00"
     *   }
     * }
     */
    public function update(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $smartFolderId = (int)$this->request->param('id');
            $data = $this->request->put();

            // 参数验证
            if (!empty($data)) {
                $this->validate($data, SmartFolderValidate::class . '.update');
            }

            // 更新智能文件夹
            $smartFolder = $this->smartFolderService->updateSmartFolder($userId, $smartFolderId, $data);

            return $this->success($smartFolder->toApiArray(), '更新智能文件夹成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('更新智能文件夹失败：' . $e->getMessage());
        }
    }

    /**
     * 删除智能文件夹
     *
     * 删除指定的智能文件夹，不会影响实际的书签数据
     *
     * @route DELETE /api/v1/smart-folders/{id}
     * @middleware auth
     * @param int $id 智能文件夹ID，路径参数
     * @return Response JSON响应，确认删除成功
     * @throws BusinessException 智能文件夹不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * DELETE /api/v1/smart-folders/1
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "删除智能文件夹成功",
     *   "data": null
     * }
     */
    public function delete(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $smartFolderId = (int)$this->request->param('id');

            // 删除智能文件夹
            $this->smartFolderService->deleteSmartFolder($userId, $smartFolderId);

            return $this->success(null, '删除智能文件夹成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('删除智能文件夹失败：' . $e->getMessage());
        }
    }
}
