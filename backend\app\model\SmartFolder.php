<?php

namespace app\model;

use think\Model;

/**
 * 智能文件夹模型
 */
class SmartFolder extends Model
{
    // 表名
    protected $name = 'smart_folders';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'user_id' => 'integer',
        'sort_order' => 'integer',
        'conditions' => 'json',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // JSON字段
    protected $json = ['conditions'];

    /**
     * 关联用户
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 检查智能文件夹名称是否存在
     * @param string $name 名称
     * @param int $userId 用户ID
     * @param int|null $excludeId 排除的ID
     * @return bool
     */
    public static function nameExists(string $name, int $userId, ?int $excludeId = null): bool
    {
        $query = self::where('name', $name)->where('user_id', $userId);
        
        if ($excludeId) {
            $query->where('id', '<>', $excludeId);
        }
        
        return $query->count() > 0;
    }

    /**
     * 获取下一个排序值
     * @param int $userId 用户ID
     * @return int
     */
    public static function getNextSortOrder(int $userId): int
    {
        $maxOrder = self::where('user_id', $userId)->max('sort_order');
        return ($maxOrder ?? 0) + 1;
    }

    /**
     * 转换为API数组格式
     * @param bool $withBookmarks 是否包含书签数量
     * @return array
     */
    public function toApiArray(bool $withBookmarks = false): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'icon' => $this->icon,
            'conditions' => $this->conditions,
            'sort_order' => $this->sort_order,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];

        if ($withBookmarks) {
            $data['bookmark_count'] = $this->getBookmarkCount();
        }

        return $data;
    }

    /**
     * 获取匹配的书签数量
     * @return int
     */
    public function getBookmarkCount(): int
    {
        return $this->getMatchingBookmarks()->count();
    }

    /**
     * 获取匹配的书签查询构建器
     * @return \think\db\Query
     */
    public function getMatchingBookmarks()
    {
        $query = Bookmark::where('user_id', $this->user_id);
        
        if (empty($this->conditions)) {
            return $query->where('1', '0'); // 返回空结果
        }

        foreach ($this->conditions as $condition) {
            $this->applyCondition($query, $condition);
        }

        return $query;
    }

    /**
     * 应用单个条件到查询
     * @param \think\db\Query $query
     * @param array $condition
     */
    private function applyCondition($query, array $condition)
    {
        $field = $condition['field'] ?? '';
        $operator = $condition['operator'] ?? 'contains';
        $value = $condition['value'] ?? '';

        switch ($field) {
            case 'title':
                if ($operator === 'contains') {
                    $query->where('title', 'like', '%' . $value . '%');
                } elseif ($operator === 'equals') {
                    $query->where('title', $value);
                }
                break;
                
            case 'url':
                if ($operator === 'contains') {
                    $query->where('url', 'like', '%' . $value . '%');
                } elseif ($operator === 'equals') {
                    $query->where('url', $value);
                }
                break;
                
            case 'description':
                if ($operator === 'contains') {
                    $query->where('description', 'like', '%' . $value . '%');
                }
                break;
                
            case 'folder_id':
                if ($operator === 'equals') {
                    $query->where('folder_id', $value);
                }
                break;
                
            case 'is_star':
                if ($operator === 'equals') {
                    $query->where('is_star', $value ? 1 : 0);
                }
                break;
                
            case 'tags':
                if ($operator === 'contains') {
                    $query->whereExists(function($subQuery) use ($value) {
                        $subQuery->table('bookmark_tags bt')
                            ->join('tags t', 'bt.tag_id', '=', 't.id')
                            ->where('bt.bookmark_id', 'exp', 'bookmarks.id')
                            ->where('t.name', 'like', '%' . $value . '%');
                    });
                }
                break;
                
            case 'created_at':
                if ($operator === 'after') {
                    $query->where('created_at', '>=', $value);
                } elseif ($operator === 'before') {
                    $query->where('created_at', '<=', $value);
                }
                break;
        }
    }

    /**
     * 验证条件格式
     * @param array $conditions
     * @return bool
     */
    public static function validateConditions(array $conditions): bool
    {
        if (count($conditions) > 10) {
            return false; // 最多10个条件
        }

        $allowedFields = ['title', 'url', 'description', 'folder_id', 'is_star', 'tags', 'created_at'];
        $allowedOperators = ['contains', 'equals', 'after', 'before'];

        foreach ($conditions as $condition) {
            if (!isset($condition['field']) || !isset($condition['operator']) || !isset($condition['value'])) {
                return false;
            }

            if (!in_array($condition['field'], $allowedFields)) {
                return false;
            }

            if (!in_array($condition['operator'], $allowedOperators)) {
                return false;
            }
        }

        return true;
    }
}
