<?php

namespace app\service;

use app\model\Bookmark;
use app\model\Folder;
use app\exception\BusinessException;
use think\facade\Db;

/**
 * 回收站服务类
 */
class TrashService
{
    /**
     * 获取回收站列表
     * @param int $userId 用户ID
     * @param array $params 查询参数
     * @return array 回收站列表
     */
    public function getTrashList(int $userId, array $params = []): array
    {
        $type = $params['type'] ?? 'all';
        $page = max(1, (int)($params['page'] ?? 1));
        $limit = max(1, min(100, (int)($params['limit'] ?? 20)));
        $search = $params['search'] ?? '';
        $orderBy = $params['order_by'] ?? 'deleted_at';
        $orderDir = $params['order_dir'] ?? 'desc';

        $items = [];
        $total = 0;

        if ($type === 'all' || $type === 'bookmark') {
            // 获取已删除的书签（使用软删除）
            $bookmarkQuery = Bookmark::onlyTrashed()
                ->where('user_id', $userId)
                ->with(['folder']);

            if ($search) {
                $bookmarkQuery->where(function($q) use ($search) {
                    $q->where('title', 'like', '%' . $search . '%')
                      ->whereOr('url', 'like', '%' . $search . '%');
                });
            }

            $bookmarks = $bookmarkQuery->select();
            foreach ($bookmarks as $bookmark) {
                $items[] = [
                    'id' => $bookmark->id,
                    'type' => 'bookmark',
                    'title' => $bookmark->title,
                    'url' => $bookmark->url,
                    'description' => $bookmark->description,
                    'folder_name' => $bookmark->folder ? $bookmark->folder->name : '',
                    'deleted_at' => $bookmark->delete_time ? date('Y-m-d H:i:s', $bookmark->delete_time) : null,
                    'auto_delete_at' => $bookmark->delete_time ? date('Y-m-d H:i:s', $bookmark->delete_time + 30 * 24 * 3600) : null
                ];
            }
        }

        if ($type === 'all' || $type === 'folder') {
            // 获取已删除的文件夹（使用软删除）
            $folderQuery = Folder::onlyTrashed()
                ->where('user_id', $userId);

            if ($search) {
                $folderQuery->where('name', 'like', '%' . $search . '%');
            }

            $folders = $folderQuery->select();
            foreach ($folders as $folder) {
                $items[] = [
                    'id' => $folder->id,
                    'type' => 'folder',
                    'title' => $folder->name,
                    'url' => null,
                    'description' => '文件夹',
                    'folder_name' => $folder->parent ? $folder->parent->name : '',
                    'deleted_at' => $folder->delete_time ? date('Y-m-d H:i:s', $folder->delete_time) : null,
                    'auto_delete_at' => $folder->delete_time ? date('Y-m-d H:i:s', $folder->delete_time + 30 * 24 * 3600) : null
                ];
            }
        }

        // 排序
        usort($items, function($a, $b) use ($orderBy, $orderDir) {
            $aValue = $a[$orderBy] ?? '';
            $bValue = $b[$orderBy] ?? '';
            
            if ($orderDir === 'desc') {
                return $bValue <=> $aValue;
            } else {
                return $aValue <=> $bValue;
            }
        });

        // 分页
        $total = count($items);
        $offset = ($page - 1) * $limit;
        $items = array_slice($items, $offset, $limit);

        return [
            'items' => $items,
            'meta' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]
        ];
    }

    /**
     * 恢复项目
     * @param int $userId 用户ID
     * @param int $itemId 项目ID
     * @param string $type 项目类型
     * @return array 恢复结果
     * @throws BusinessException
     */
    public function restoreItem(int $userId, int $itemId, string $type): array
    {
        if ($type === 'bookmark') {
            return $this->restoreBookmark($userId, $itemId);
        } elseif ($type === 'folder') {
            return $this->restoreFolder($userId, $itemId);
        } else {
            throw new BusinessException('不支持的项目类型', 30007);
        }
    }

    /**
     * 恢复书签
     * @param int $userId 用户ID
     * @param int $bookmarkId 书签ID
     * @return array 恢复结果
     * @throws BusinessException
     */
    private function restoreBookmark(int $userId, int $bookmarkId): array
    {
        $bookmark = Bookmark::onlyTrashed()
            ->where('id', $bookmarkId)
            ->where('user_id', $userId)
            ->find();

        if (!$bookmark) {
            throw new BusinessException('书签不存在或已被永久删除', 30008);
        }

        // 检查原文件夹是否还存在
        if ($bookmark->folder_id) {
            $folder = Folder::find($bookmark->folder_id);
            if (!$folder) {
                // 如果原文件夹不存在，恢复到根目录
                $bookmark->folder_id = null;
            }
        }

        // 恢复书签
        if (!$bookmark->restore()) {
            throw new BusinessException('恢复书签失败', 30009);
        }

        return [
            'id' => $bookmark->id,
            'type' => 'bookmark',
            'title' => $bookmark->title,
            'restored_to' => $bookmark->folder ? $bookmark->folder->name : '根目录',
            'restored_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * 恢复文件夹
     * @param int $userId 用户ID
     * @param int $folderId 文件夹ID
     * @return array 恢复结果
     * @throws BusinessException
     */
    private function restoreFolder(int $userId, int $folderId): array
    {
        $folder = Folder::onlyTrashed()
            ->where('id', $folderId)
            ->where('user_id', $userId)
            ->find();

        if (!$folder) {
            throw new BusinessException('文件夹不存在或已被永久删除', 30008);
        }

        // 检查父文件夹是否还存在
        if ($folder->parent_id) {
            $parentFolder = Folder::find($folder->parent_id);
            if (!$parentFolder) {
                // 如果父文件夹不存在，恢复到根目录
                $folder->parent_id = null;
            }
        }

        // 恢复文件夹
        if (!$folder->restore()) {
            throw new BusinessException('恢复文件夹失败', 30009);
        }

        return [
            'id' => $folder->id,
            'type' => 'folder',
            'title' => $folder->name,
            'restored_to' => $folder->parent ? $folder->parent->name : '根目录',
            'restored_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * 永久删除项目
     * @param int $userId 用户ID
     * @param int $itemId 项目ID
     * @param string $type 项目类型
     * @return bool
     * @throws BusinessException
     */
    public function destroyItem(int $userId, int $itemId, string $type): bool
    {
        if ($type === 'bookmark') {
            $item = Bookmark::onlyTrashed()
                ->where('id', $itemId)
                ->where('user_id', $userId)
                ->find();
        } elseif ($type === 'folder') {
            $item = Folder::onlyTrashed()
                ->where('id', $itemId)
                ->where('user_id', $userId)
                ->find();
        } else {
            throw new BusinessException('不支持的项目类型', 30007);
        }

        if (!$item) {
            throw new BusinessException('项目不存在或已被永久删除', 30008);
        }

        // 永久删除
        return $item->force()->delete();
    }

    /**
     * 清空回收站
     * @param int $userId 用户ID
     * @param string $type 项目类型
     * @return array 清空结果
     * @throws BusinessException
     */
    public function clearTrash(int $userId, string $type = 'all'): array
    {
        $deletedBookmarks = 0;
        $deletedFolders = 0;

        Db::startTrans();
        try {
            if ($type === 'all' || $type === 'bookmark') {
                // 永久删除所有已删除的书签
                $bookmarks = Bookmark::onlyTrashed()
                    ->where('user_id', $userId)
                    ->select();
                
                foreach ($bookmarks as $bookmark) {
                    $bookmark->force()->delete();
                    $deletedBookmarks++;
                }
            }

            if ($type === 'all' || $type === 'folder') {
                // 永久删除所有已删除的文件夹
                $folders = Folder::onlyTrashed()
                    ->where('user_id', $userId)
                    ->select();
                
                foreach ($folders as $folder) {
                    $folder->force()->delete();
                    $deletedFolders++;
                }
            }

            Db::commit();

            return [
                'deleted_bookmarks' => $deletedBookmarks,
                'deleted_folders' => $deletedFolders,
                'total_deleted' => $deletedBookmarks + $deletedFolders
            ];

        } catch (\Exception $e) {
            Db::rollback();
            throw new BusinessException('清空回收站失败：' . $e->getMessage());
        }
    }

    /**
     * 自动清理过期的回收站项目
     * @param int $days 保留天数，默认30天
     * @return array 清理结果
     */
    public function autoCleanExpired(int $days = 30): array
    {
        $expireTime = time() - $days * 24 * 3600;
        
        $deletedBookmarks = Bookmark::onlyTrashed()
            ->where('delete_time', '<', $expireTime)
            ->count();
            
        $deletedFolders = Folder::onlyTrashed()
            ->where('delete_time', '<', $expireTime)
            ->count();

        // 执行清理
        Bookmark::onlyTrashed()
            ->where('delete_time', '<', $expireTime)
            ->force()
            ->delete();
            
        Folder::onlyTrashed()
            ->where('delete_time', '<', $expireTime)
            ->force()
            ->delete();

        return [
            'deleted_bookmarks' => $deletedBookmarks,
            'deleted_folders' => $deletedFolders,
            'total_deleted' => $deletedBookmarks + $deletedFolders
        ];
    }
}
