# Web端书签管理系统 - 项目架构文档

## 1. 系统概述

Web端书签管理系统是一个基于B/S架构的个人知识与信息导航平台，采用前后端分离的架构模式，提供高效的书签管理、智能分类和数据洞察功能。

## 2. 技术栈选型

### 2.1 前端技术栈
- **核心框架**: Vue 3.3+ (Composition API)
- **构建工具**: Vite 4.0+
- **路由管理**: Vue Router 4.0+
- **状态管理**: Pinia 2.1+
- **UI组件库**: Element Plus 2.4+
- **HTTP客户端**: Axios 1.5+
- **样式处理**: SASS/SCSS + Tailwind CSS 3.3+
- **图标库**: Iconify + Lucide Icons
- **拖拽库**: Sortable.js / Vue.Draggable.Next
- **图表库**: ECharts 5.4+
- **富文本**: TipTap 2.0+ (用于笔记功能)

### 2.2 后端技术栈
- **核心框架**: PHP 8.1+ / ThinkPHP 8.0
- **Web服务器**: Nginx 1.20+
- **数据库**: MySQL 8.0+
- **缓存服务**: Redis 7.0+
- **消息队列**: ThinkPHP Queue (基于Redis驱动)
- **进程管理**: Supervisor
- **日志处理**: Monolog
- **API文档**: Swagger/OpenAPI 3.0

### 2.3 开发工具与环境
- **代码版本管理**: Git + GitHub/GitLab
- **开发环境**: Docker + Docker Compose
- **代码规范**: PHP-CS-Fixer + ESLint + Prettier
- **测试框架**: PHPUnit + Vitest
- **CI/CD**: GitHub Actions / GitLab CI

### 2.4 第三方服务
- **AI服务**: 
  - 主选方案：通义千问 API (阿里云)
  - 备选方案：文心一言 API (百度)
- **邮件服务**: 阿里云邮件推送 / SendCloud
- **对象存储**: 本地存储 (v1.0) / 阿里云OSS (未来版本)
- **监控服务**: 自建日志系统 + Prometheus (可选)

## 3. 系统架构设计

### 3.1 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                          用户浏览器                               │
└────────────────────────────┬────────────────────────────────────┘
                             │ HTTPS
┌────────────────────────────┴────────────────────────────────────┐
│                         Nginx 反向代理                            │
│                    (静态资源 + API 路由分发)                       │
└──────────┬─────────────────────────────────┬────────────────────┘
           │ 静态资源                         │ API 请求
┌──────────┴──────────┐          ┌───────────┴────────────────────┐
│   Vue 3 SPA 应用    │          │    ThinkPHP 8.0 API 服务       │
│  (dist 静态文件)     │          │   ┌─────────────────────┐      │
└────────────────────┘          │   │   路由中间件层       │      │
                                │   ├─────────────────────┤      │
                                │   │   控制器层          │      │
                                │   ├─────────────────────┤      │
                                │   │   服务层            │      │
                                │   ├─────────────────────┤      │
                                │   │   模型层            │      │
                                │   └──────────┬──────────┘      │
                                └──────────────┼─────────────────┘
                                               │
                ┌──────────────────────────────┼──────────────────┐
                │                              │                  │
     ┌──────────┴──────────┐       ┌──────────┴──────────┐      │
     │     MySQL 8.0       │       │     Redis 7.0       │      │
     │   (主数据存储)       │       │  (缓存/队列/会话)    │      │
     └────────────────────┘       └──────────────────┘      │
                                                                 │
     ┌───────────────────────────────────────────────────────┐  │
     │              消息队列处理器 (Supervisor)                │  │
     │         ┌──────────────┐ ┌──────────────┐            │  │
     │         │ AI解析任务    │ │ 死链检测任务  │            │──┘
     │         └──────────────┘ └──────────────┘            │
     └───────────────────────────────────────────────────────┘
```

### 3.2 分层架构设计

#### 3.2.1 前端架构分层

```
src/
├── api/              # API 接口层
│   ├── modules/      # 按模块组织的接口
│   └── request.js    # Axios 封装与拦截器
├── assets/           # 静态资源
├── components/       # 通用组件
│   ├── common/       # 基础组件
│   └── business/     # 业务组件
├── composables/      # 组合式函数
├── layouts/          # 布局组件
├── router/           # 路由配置
├── stores/           # Pinia 状态管理
├── utils/            # 工具函数
├── views/            # 页面组件
└── App.vue           # 根组件
```

#### 3.2.2 后端架构分层

```
app/
├── controller/       # 控制器层 (处理HTTP请求)
│   ├── api/          # API 控制器
│   └── BaseController.php
├── model/            # 模型层 (数据实体)
├── service/          # 服务层 (业务逻辑)
├── validate/         # 验证器
├── middleware/       # 中间件
│   ├── Auth.php      # JWT认证
│   └── Cors.php      # 跨域处理
├── job/              # 队列任务
│   ├── AiParseJob.php
│   └── LinkCheckJob.php
├── common/           # 公共模块
│   ├── exception/    # 自定义异常
│   └── trait/        # 复用特性
└── command/          # 命令行工具
```

### 3.3 核心模块设计

#### 3.3.1 认证授权模块

- **认证方式**: JWT (JSON Web Token)
- **Token 策略**: 
  - Access Token: 有效期 2 小时
  - Refresh Token: 有效期 30 天
- **存储方式**: 
  - Access Token: localStorage
  - Refresh Token: httpOnly Cookie
- **刷新机制**: 自动无感刷新

#### 3.3.2 数据缓存策略

- **缓存层级**:
  1. 浏览器缓存 (localStorage/sessionStorage)
  2. HTTP 缓存 (ETag/Last-Modified)
  3. Redis 缓存 (热点数据)
  4. MySQL 查询缓存

- **缓存内容**:
  - 用户会话信息: Redis, TTL 2小时
  - 书签列表: Redis, TTL 10分钟
  - 标签云数据: Redis, TTL 1小时
  - 统计数据: Redis, TTL 1天

#### 3.3.3 消息队列设计

- **队列驱动**: Redis (ThinkPHP Queue)
- **任务类型**:
  - AI解析任务 (ai_parse)
  - 死链检测任务 (link_check)
  - 报告生成任务 (report_generate)
  - 批量导入任务 (batch_import)

- **任务配置**:
  ```php
  // 无优先级区分，按先进先出原则处理
  'default' => [
      'connection' => 'redis',
      'queue' => 'default',
      'retry_after' => 90,
      'block_for' => null,
  ]
  ```

#### 3.3.4 文件存储设计

- **存储位置**: 本地服务器 `/storage` 目录
- **目录结构**:
  ```
  storage/
  ├── avatars/      # 用户头像
  ├── icons/        # 自定义图标
  ├── exports/      # 导出文件
  └── temp/         # 临时文件
  ```
- **访问方式**: 通过 Nginx 配置静态资源访问
- **文件限制**: 
  - 头像: 最大 2MB, 格式 jpg/png/webp
  - 图标: 最大 500KB, 格式 svg/png/ico

## 4. 安全架构设计

### 4.1 应用安全
- **HTTPS**: 全站强制 HTTPS，HSTS 头部
- **XSS 防护**: CSP 头部 + 输入过滤 + 输出转义
- **CSRF 防护**: Token 验证
- **SQL 注入**: 参数化查询 + ORM
- **文件上传**: 类型白名单 + 文件头检测

### 4.2 认证安全
- **密码存储**: bcrypt 加密 (cost=12)
- **登录限制**: 5次失败锁定15分钟
- **会话管理**: 单设备登录 / 异地登录提醒

### 4.3 数据安全
- **数据隔离**: 租户级别完全隔离
- **敏感数据**: AES-256 加密存储
- **备份策略**: 每日增量备份 + 每周全量备份

## 5. 性能架构设计

### 5.1 前端性能优化
- **代码分割**: 路由级别懒加载
- **资源优化**: 图片懒加载 + WebP 格式
- **打包优化**: Tree Shaking + Gzip 压缩
- **缓存策略**: Service Worker + HTTP 缓存
- **虚拟滚动**: 大数据列表渲染优化

### 5.2 后端性能优化
- **数据库优化**:
  - 合理索引设计
  - 读写分离 (未来版本)
  - 查询优化与慢查询监控
- **接口优化**:
  - 数据分页
  - 字段按需返回
  - 批量接口支持
- **并发处理**:
  - 连接池管理
  - 异步任务队列

### 5.3 性能指标
- **响应时间**: 
  - API 平均响应 < 200ms
  - 页面加载 < 3s
- **并发能力**: 
  - 支持 1000+ 并发用户
  - 支持 10000+ 书签/用户
- **可用性**: 99.9% SLA

## 6. 部署架构设计

### 6.1 开发环境
- Docker Compose 一键部署
- 包含所有依赖服务
- 热重载支持

### 6.2 生产环境
```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Nginx     │────▶│  PHP-FPM    │────▶│   MySQL     │
│  (反向代理)  │     │  (应用服务)  │     │  (主数据库)  │
└─────────────┘     └─────────────┘     └─────────────┘
                            │
                            ▼
                    ┌─────────────┐     ┌─────────────┐
                    │   Redis     │     │ Supervisor  │
                    │ (缓存/队列)  │────▶│  (队列处理)  │
                    └─────────────┘     └─────────────┘
```

### 6.3 监控与日志
- **应用日志**: Monolog → 本地文件
- **访问日志**: Nginx → 本地文件
- **错误监控**: 自定义异常处理器
- **性能监控**: 自建 APM (可选)

## 7. 扩展性设计

### 7.1 水平扩展能力
- 无状态应用设计
- Session 存储在 Redis
- 支持负载均衡部署

### 7.2 功能扩展点
- 插件化架构预留
- Webhook 支持
- 第三方集成接口

### 7.3 数据扩展性
- 分表策略预留 (按用户ID)
- 归档机制设计
- 多租户架构支持

## 8. 开发规范

### 8.1 代码规范
- **PHP**: PSR-12 编码规范
- **JavaScript**: Airbnb 规范
- **Git**: Conventional Commits

### 8.2 API 规范
- RESTful 设计原则
- 统一响应格式
- 版本化管理

### 8.3 文档规范
- 代码注释规范
- API 文档自动生成
- 部署文档维护

## 9. 项目里程碑

- **Phase 1**: 基础架构搭建 (1周)
- **Phase 2**: 核心功能开发 (3周)
- **Phase 3**: AI功能集成 (1周)
- **Phase 4**: 性能优化与测试 (1周)
- **Phase 5**: 部署与上线 (3天) 