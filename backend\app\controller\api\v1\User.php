<?php

namespace app\controller\api\v1;

use app\BaseController;
use app\model\User as UserModel;
use app\model\UserPreference;
use app\service\AuthService;
use app\validate\AuthValidate;
use app\validate\UserValidate;
use app\exception\BusinessException;
use think\Response;

/**
 * 用户管理控制器
 *
 * 提供用户信息管理、密码修改、偏好设置、设备管理等功能
 */
class User extends BaseController
{
    /**
     * 认证服务
     * @var AuthService
     */
    protected $authService;

    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->authService = new AuthService();
    }

    /**
     * 获取用户信息
     *
     * 获取当前登录用户的详细信息，包含个人资料和统计数据
     *
     * @route GET /api/v1/user/profile
     * @middleware auth
     * @return Response JSON响应，包含用户详细信息
     * @throws BusinessException 用户不存在或未登录
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/user/profile
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取用户信息成功",
     *   "data": {
     *     "id": 1,
     *     "username": "admin",
     *     "email": "<EMAIL>",
     *     "nickname": "管理员",
     *     "avatar": "https://example.com/avatar.jpg",
     *     "status": 1,
     *     "last_login_at": "2024-06-30 12:00:00",
     *     "created_at": "2024-06-30 12:00:00"
     *   }
     * }
     */
    public function profile(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $user = UserModel::find($userId);
            if (!$user) {
                throw BusinessException::userNotFound();
            }

            return $this->success($user->toApiArray(true), '获取用户信息成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('获取用户信息失败：' . $e->getMessage());
        }
    }

    /**
     * 更新用户信息
     *
     * 更新当前登录用户的个人信息，支持部分字段更新
     *
     * @route PUT /api/v1/user/profile
     * @middleware auth
     * @param string $email 邮箱地址，可选，必须是有效的邮箱格式且未被使用
     * @param string $username 用户名，可选，3-20个字符且未被使用
     * @param string $nickname 昵称，可选，1-50个字符
     * @param string $avatar 头像URL，可选
     * @return Response JSON响应，包含更新后的用户信息
     * @throws BusinessException 参数验证失败、用户不存在或邮箱/用户名已被使用
     * @throws \Exception 系统异常
     * @example
     * PUT /api/v1/user/profile
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "nickname": "新昵称",
     *   "avatar": "https://example.com/new-avatar.jpg"
     * }
     * Response: {
     *   "code": 200,
     *   "message": "更新用户信息成功",
     *   "data": {
     *     "id": 1,
     *     "nickname": "新昵称",
     *     "avatar": "https://example.com/new-avatar.jpg",
     *     "updated_at": "2024-06-30 12:30:00"
     *   }
     * }
     */
    public function updateProfile(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            // 参数验证
            $data = $this->request->put();
            $this->validate($data, UserValidate::class . '.update');

            $user = UserModel::find($userId);
            if (!$user) {
                throw BusinessException::userNotFound();
            }

            // 检查邮箱是否被其他用户使用
            if (isset($data['email']) && $data['email'] !== $user->email) {
                if (UserModel::emailExists($data['email'], $userId)) {
                    throw BusinessException::userEmailExists();
                }
            }

            // 检查用户名是否被其他用户使用
            if (isset($data['username']) && $data['username'] !== $user->username) {
                if (UserModel::usernameExists($data['username'], $userId)) {
                    throw BusinessException::userUsernameExists();
                }
            }

            // 更新用户信息
            $allowedFields = ['email', 'username', 'avatar', 'nickname'];
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $user->$field = $data[$field];
                }
            }

            if (!$user->save()) {
                throw new BusinessException('更新用户信息失败');
            }

            return $this->success($user->toApiArray(), '更新用户信息成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('更新用户信息失败：' . $e->getMessage());
        }
    }

    /**
     * 修改密码
     *
     * 修改当前登录用户的密码，支持登出其他设备选项
     *
     * @route POST /api/v1/user/change-password
     * @middleware auth
     * @param string $old_password 原密码，必填
     * @param string $new_password 新密码，必填，6-20个字符
     * @param string $confirm_password 确认新密码，必填，必须与新密码一致
     * @param bool $logout_other_devices 是否登出其他设备，可选，默认true
     * @return Response JSON响应，确认密码修改成功
     * @throws BusinessException 参数验证失败、原密码错误或用户不存在
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/user/change-password
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "old_password": "123456",
     *   "new_password": "newpassword123",
     *   "confirm_password": "newpassword123",
     *   "logout_other_devices": true
     * }
     * Response: {
     *   "code": 200,
     *   "message": "密码修改成功",
     *   "data": null
     * }
     */
    public function changePassword(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            // 参数验证
            $data = $this->request->post();
            $this->validate($data, AuthValidate::class . '.change_password');

            // 执行密码修改
            $logoutOtherDevices = $data['logout_other_devices'] ?? true;
            $this->authService->changePassword(
                $userId,
                $data['old_password'],
                $data['new_password'],
                $logoutOtherDevices
            );

            return $this->success(null, '密码修改成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('密码修改失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户的活跃设备列表
     *
     * 获取当前用户所有活跃登录设备的信息
     *
     * @route GET /api/v1/user/devices
     * @middleware auth
     * @return Response JSON响应，包含设备列表
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/user/devices
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取设备列表成功",
     *   "data": [
     *     {
     *       "token_id": 1,
     *       "device_info": "Chrome 91.0 on Windows 10",
     *       "ip_address": "*************",
     *       "last_used_at": "2024-06-30 12:00:00",
     *       "is_current": true
     *     }
     *   ]
     * }
     */
    public function devices(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $devices = $this->authService->getUserDevices($userId);

            return $this->success($devices, '获取设备列表成功');

        } catch (\Exception $e) {
            return $this->error('获取设备列表失败：' . $e->getMessage());
        }
    }

    /**
     * 撤销指定设备的登录
     *
     * 撤销指定设备的登录状态，使其令牌失效
     *
     * @route POST /api/v1/user/revoke-device
     * @middleware auth
     * @param int $token_id 设备令牌ID，必填
     * @return Response JSON响应，确认撤销成功
     * @throws BusinessException 参数验证失败或设备不存在
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/user/revoke-device
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "token_id": 2
     * }
     * Response: {
     *   "code": 200,
     *   "message": "设备登录已撤销",
     *   "data": null
     * }
     */
    public function revokeDevice(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $data = $this->request->post();
            $tokenId = (int)($data['token_id'] ?? 0);

            if (!$tokenId) {
                return $this->error('设备令牌ID不能为空');
            }

            $this->authService->revokeDevice($userId, $tokenId);

            return $this->success(null, '设备登录已撤销');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('撤销设备登录失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户偏好设置
     *
     * 获取当前登录用户的个性化偏好设置
     *
     * @route GET /api/v1/user/preferences
     * @middleware auth
     * @return Response JSON响应，包含用户偏好设置
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/user/preferences
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取用户偏好设置成功",
     *   "data": {
     *     "theme": "light",
     *     "language": "zh-CN",
     *     "items_per_page": 20,
     *     "default_view": "list",
     *     "auto_backup": true,
     *     "email_notifications": true
     *   }
     * }
     */
    public function preferences(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            // 获取用户偏好设置，如果不存在则创建默认设置
            $preference = UserPreference::getOrCreateByUserId($userId);

            return $this->success($preference->toApiArray(), '获取用户偏好设置成功');

        } catch (\Exception $e) {
            return $this->error('获取用户偏好设置失败：' . $e->getMessage());
        }
    }

    /**
     * 更新用户偏好设置
     *
     * 更新当前登录用户的个性化偏好设置
     *
     * @route PUT /api/v1/user/preferences
     * @middleware auth
     * @param string $theme 主题设置，可选：light|dark|auto
     * @param string $language 语言设置，可选，如：zh-CN、en-US
     * @param int $items_per_page 每页显示数量，可选，10-100之间
     * @param string $default_view 默认视图，可选：list|grid|card
     * @param bool $auto_backup 自动备份，可选
     * @param bool $email_notifications 邮件通知，可选
     * @return Response JSON响应，确认更新成功
     * @throws \Exception 系统异常
     * @example
     * PUT /api/v1/user/preferences
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "theme": "dark",
     *   "items_per_page": 30,
     *   "default_view": "grid",
     *   "email_notifications": false
     * }
     * Response: {
     *   "code": 200,
     *   "message": "更新用户偏好设置成功",
     *   "data": null
     * }
     */
    public function updatePreferences(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            // 获取请求数据
            $data = $this->request->put();

            // 参数验证
            $this->validate($data, UserValidate::class . '.preferences');

            // 更新用户偏好设置
            $result = UserPreference::updateByUserId($userId, $data);

            if (!$result) {
                throw new BusinessException('更新用户偏好设置失败');
            }

            // 获取更新后的偏好设置
            $preference = UserPreference::findByUserId($userId);

            return $this->success($preference->toApiArray(), '更新用户偏好设置成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('更新用户偏好设置失败：' . $e->getMessage());
        }
    }
}
