/**
 * JWT Token 管理工具
 */

import jwtDecode from 'jwt-decode'

interface JwtPayload {
  exp: number
  iat: number
  sub: string
  [key: string]: any
}

export class TokenManager {
  private static readonly TOKEN_KEY = 'token'
  private static readonly REFRESH_TOKEN_KEY = 'refreshToken'
  private static readonly TOKEN_EXPIRE_THRESHOLD = 5 * 60 * 1000 // 5分钟

  /**
   * 设置Token
   */
  static setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token)
  }

  /**
   * 获取Token
   */
  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY)
  }

  /**
   * 设置刷新Token
   */
  static setRefreshToken(refreshToken: string): void {
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken)
  }

  /**
   * 获取刷新Token
   */
  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY)
  }

  /**
   * 清除所有Token
   */
  static clearTokens(): void {
    localStorage.removeItem(this.TOKEN_KEY)
    localStorage.removeItem(this.REFRESH_TOKEN_KEY)
  }

  /**
   * 解析JWT Token
   */
  static decodeToken(token: string): JwtPayload | null {
    try {
      return jwtDecode<JwtPayload>(token)
    } catch (error) {
      console.error('Token解析失败:', error)
      return null
    }
  }

  /**
   * 检查Token是否有效
   */
  static isTokenValid(token: string): boolean {
    const payload = this.decodeToken(token)
    if (!payload) {
      return false
    }

    const now = Date.now() / 1000
    return payload.exp > now
  }

  /**
   * 检查Token是否即将过期
   */
  static isTokenExpiringSoon(token: string): boolean {
    const payload = this.decodeToken(token)
    if (!payload) {
      return true
    }

    const now = Date.now()
    const expireTime = payload.exp * 1000
    return (expireTime - now) < this.TOKEN_EXPIRE_THRESHOLD
  }

  /**
   * 获取Token过期时间
   */
  static getTokenExpireTime(token: string): Date | null {
    const payload = this.decodeToken(token)
    if (!payload) {
      return null
    }

    return new Date(payload.exp * 1000)
  }

  /**
   * 获取Token剩余有效时间（毫秒）
   */
  static getTokenRemainingTime(token: string): number {
    const payload = this.decodeToken(token)
    if (!payload) {
      return 0
    }

    const now = Date.now()
    const expireTime = payload.exp * 1000
    return Math.max(0, expireTime - now)
  }

  /**
   * 从Token中获取用户ID
   */
  static getUserIdFromToken(token: string): string | null {
    const payload = this.decodeToken(token)
    return payload?.sub || null
  }

  /**
   * 检查当前Token状态
   */
  static checkTokenStatus(): {
    hasToken: boolean
    isValid: boolean
    isExpiringSoon: boolean
    remainingTime: number
  } {
    const token = this.getToken()
    
    if (!token) {
      return {
        hasToken: false,
        isValid: false,
        isExpiringSoon: true,
        remainingTime: 0
      }
    }

    const isValid = this.isTokenValid(token)
    const isExpiringSoon = this.isTokenExpiringSoon(token)
    const remainingTime = this.getTokenRemainingTime(token)

    return {
      hasToken: true,
      isValid,
      isExpiringSoon,
      remainingTime
    }
  }

  /**
   * 自动刷新Token的定时器
   */
  static startAutoRefresh(refreshCallback: () => Promise<boolean>): () => void {
    const checkInterval = 60 * 1000 // 每分钟检查一次

    const intervalId = setInterval(async () => {
      const status = this.checkTokenStatus()
      
      if (status.hasToken && status.isValid && status.isExpiringSoon) {
        console.log('Token即将过期，尝试自动刷新...')
        try {
          const success = await refreshCallback()
          if (success) {
            console.log('Token自动刷新成功')
          } else {
            console.log('Token自动刷新失败')
          }
        } catch (error) {
          console.error('Token自动刷新出错:', error)
        }
      }
    }, checkInterval)

    // 返回清理函数
    return () => {
      clearInterval(intervalId)
    }
  }
}

// 导出默认实例
export default TokenManager
