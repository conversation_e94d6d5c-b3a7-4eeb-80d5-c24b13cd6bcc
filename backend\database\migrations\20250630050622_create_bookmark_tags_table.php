<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateBookmarkTagsTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $table = $this->table('bookmark_tags', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => '书签标签关联表'
        ]);

        $table->addColumn('id', 'biginteger', [
            'identity' => true,
            'signed' => false,
            'comment' => '关联ID'
        ])
        ->addColumn('bookmark_id', 'biginteger', [
            'signed' => false,
            'null' => false,
            'comment' => '书签ID'
        ])
        ->addColumn('tag_id', 'biginteger', [
            'signed' => false,
            'null' => false,
            'comment' => '标签ID'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => false,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addIndex(['bookmark_id', 'tag_id'], ['unique' => true, 'name' => 'uk_bookmark_tag'])
        ->addIndex(['tag_id'], ['name' => 'idx_tag_id'])
        ->addForeignKey('bookmark_id', 'bookmarks', 'id', [
            'delete' => 'CASCADE',
            'constraint' => 'fk_bookmark_tags_bookmarks'
        ])
        ->addForeignKey('tag_id', 'tags', 'id', [
            'delete' => 'CASCADE',
            'constraint' => 'fk_bookmark_tags_tags'
        ])
        ->create();
    }
}
