<?php

namespace app\validate;

use think\Validate;

class FolderValidate extends Validate
{
    protected $rule = [
        'name' => 'require|max:100',
        'parent_id' => 'integer',
        'icon' => 'max:50',
        'color' => 'regex:/^#[0-9A-Fa-f]{6}$/',
    ];

    protected $message = [
        'name.require' => '文件夹名称不能为空',
        'name.max' => '文件夹名称不能超过100个字符',
        'parent_id.integer' => '父文件夹ID必须是整数',
        'icon.max' => '图标代码不能超过50个字符',
        'color.regex' => '颜色格式不正确，请使用#开头的6位16进制颜色代码',
    ];

    protected $scene = [
        'create' => ['name', 'parent_id', 'icon', 'color'],
        'update' => ['name', 'icon', 'color'],
    ];
}
