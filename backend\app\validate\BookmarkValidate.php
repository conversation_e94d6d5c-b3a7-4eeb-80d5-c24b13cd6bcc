<?php

namespace app\validate;

use think\Validate;

class BookmarkValidate extends Validate
{
    protected $rule = [
        'title' => 'require|max:255',
        'url' => 'require|url|max:2000',
        'description' => 'max:1000',
        'favicon' => 'url|max:500',
        'folder_id' => 'integer',
        'is_star' => 'in:0,1',
        'tags' => 'array',
    ];

    protected $message = [
        'title.require' => '标题不能为空',
        'title.max' => '标题不能超过255个字符',
        'url.require' => 'URL不能为空',
        'url.url' => 'URL格式不正确',
        'url.max' => 'URL不能超过2000个字符',
        'description.max' => '描述不能超过1000个字符',
        'favicon.url' => '网站图标必须是有效的URL',
        'favicon.max' => '网站图标URL不能超过500个字符',
        'folder_id.integer' => '文件夹ID必须是整数',
        'is_star.in' => '星标状态只能是0或1',
        'tags.array' => '标签必须是数组格式',

        // 更新时的错误消息
        'update_title.max' => '标题不能超过255个字符',
        'update_url.url' => 'URL格式不正确',
        'update_url.max' => 'URL不能超过2000个字符',
        'update_description.max' => '描述不能超过1000个字符',
        'update_favicon.url' => '网站图标URL格式不正确',
        'update_favicon.max' => '网站图标URL不能超过500个字符',
        'update_folder_id.integer' => '文件夹ID必须是整数',
        'update_is_star.in' => '星标状态只能是0或1',
        'update_tags.array' => '标签必须是数组格式',
    ];

    protected $scene = [
        'create' => ['title', 'url', 'description', 'favicon', 'folder_id', 'is_star', 'tags'],
        'update' => ['title', 'description', 'favicon', 'folder_id', 'is_star', 'tags'], // 更新时不要求URL和title
    ];

    /**
     * 更新场景的验证规则
     */
    public function sceneUpdate()
    {
        return $this->only(['title', 'description', 'favicon', 'folder_id', 'is_star', 'tags'])
                    ->remove('title', 'require') // 移除title的必需验证
                    ->remove('url', 'require'); // 移除url的必需验证
    }
}
