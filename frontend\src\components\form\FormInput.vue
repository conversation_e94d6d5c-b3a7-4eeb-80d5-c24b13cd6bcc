<template>
  <el-form-item 
    :label="label" 
    :prop="prop"
    :required="required"
    :error="errorMessage"
    :label-width="labelWidth"
  >
    <el-input
      v-model="inputValue"
      :type="type"
      :placeholder="placeholder"
      :disabled="disabled"
      :readonly="readonly"
      :clearable="clearable"
      :show-password="showPassword"
      :prefix-icon="prefixIcon"
      :suffix-icon="suffixIcon"
      :size="size"
      :maxlength="maxlength"
      :minlength="minlength"
      :show-word-limit="showWordLimit"
      :rows="rows"
      :autosize="autosize"
      :resize="resize"
      @blur="handleBlur"
      @focus="handleFocus"
      @change="handleChange"
      @input="handleInput"
      @clear="handleClear"
    >
      <template #prepend v-if="$slots.prepend">
        <slot name="prepend" />
      </template>
      <template #append v-if="$slots.append">
        <slot name="append" />
      </template>
    </el-input>
  </el-form-item>
</template>

<script setup lang="ts">
import type { ComponentSize } from 'element-plus'

interface Props {
  modelValue: string | number
  label?: string
  prop?: string
  type?: string
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  clearable?: boolean
  showPassword?: boolean
  prefixIcon?: string
  suffixIcon?: string
  size?: ComponentSize
  maxlength?: number
  minlength?: number
  showWordLimit?: boolean
  rows?: number
  autosize?: boolean | object
  resize?: string
  required?: boolean
  labelWidth?: string
  validator?: (value: any) => string | null
}

interface Emits {
  (e: 'update:modelValue', value: string | number): void
  (e: 'blur', event: FocusEvent): void
  (e: 'focus', event: FocusEvent): void
  (e: 'change', value: string | number): void
  (e: 'input', value: string | number): void
  (e: 'clear'): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  clearable: true,
  showPassword: false,
  disabled: false,
  readonly: false,
  required: false,
  showWordLimit: false,
  autosize: false
})

const emit = defineEmits<Emits>()

const errorMessage = ref<string>('')

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const validateValue = (value: any) => {
  if (props.validator) {
    const error = props.validator(value)
    errorMessage.value = error || ''
    return !error
  }
  return true
}

const handleBlur = (event: FocusEvent) => {
  validateValue(inputValue.value)
  emit('blur', event)
}

const handleFocus = (event: FocusEvent) => {
  errorMessage.value = ''
  emit('focus', event)
}

const handleChange = (value: string | number) => {
  emit('change', value)
}

const handleInput = (value: string | number) => {
  emit('input', value)
}

const handleClear = () => {
  errorMessage.value = ''
  emit('clear')
}

// 暴露验证方法
defineExpose({
  validate: () => validateValue(inputValue.value),
  clearValidation: () => {
    errorMessage.value = ''
  }
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
