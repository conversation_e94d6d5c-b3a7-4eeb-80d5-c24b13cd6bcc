<?php

namespace app\service;

use app\model\Bookmark;
use app\model\Folder;
use app\model\Tag;
use app\model\BookmarkTag;
use think\facade\Db;

/**
 * 统计服务类
 */
class StatsService
{
    /**
     * 获取概览统计
     * @param int $userId 用户ID
     * @param int $days 统计天数
     * @return array 概览统计数据
     */
    public function getOverviewStats(int $userId, int $days = 30): array
    {
        $startDate = date('Y-m-d H:i:s', time() - $days * 24 * 3600);

        // 基础统计
        $totalBookmarks = Bookmark::where('user_id', $userId)->count();
        $totalFolders = Folder::where('user_id', $userId)->count();
        $totalTags = Tag::where('user_id', $userId)->count();
        $starBookmarks = Bookmark::where('user_id', $userId)->where('is_star', 1)->count();

        // 最近添加的书签
        $recentAdded = Bookmark::where('user_id', $userId)
            ->where('created_at', '>=', $startDate)
            ->count();

        // 最近访问的书签（模拟数据）
        $recentVisited = (int)($totalBookmarks * 0.3);

        // 增长率（模拟计算）
        $previousPeriodStart = date('Y-m-d H:i:s', time() - $days * 2 * 24 * 3600);
        $previousAdded = Bookmark::where('user_id', $userId)
            ->where('created_at', '>=', $previousPeriodStart)
            ->where('created_at', '<', $startDate)
            ->count();
        
        $growthRate = $previousAdded > 0 ? (($recentAdded - $previousAdded) / $previousAdded) * 100 : 0;

        // 最常用标签
        $mostUsedTags = Tag::where('user_id', $userId)
            ->where('usage_count', '>', 0)
            ->order('usage_count', 'desc')
            ->limit(5)
            ->field('name,usage_count')
            ->select()
            ->toArray();

        // 文件夹分布
        $folderDistribution = Folder::where('user_id', $userId)
            ->field('name,id')
            ->select()
            ->map(function($folder) {
                $bookmarkCount = Bookmark::where('folder_id', $folder->id)->count();
                return [
                    'name' => $folder->name,
                    'bookmark_count' => $bookmarkCount,
                    'percentage' => 0 // 后续计算
                ];
            })
            ->toArray();

        // 计算文件夹分布百分比
        if ($totalBookmarks > 0) {
            foreach ($folderDistribution as &$folder) {
                $folder['percentage'] = round(($folder['bookmark_count'] / $totalBookmarks) * 100, 1);
            }
        }

        // 每日活动（模拟数据）
        $dailyActivity = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', time() - $i * 24 * 3600);
            $dailyActivity[] = [
                'date' => $date,
                'added' => rand(0, 5),
                'visited' => rand(5, 20)
            ];
        }

        return [
            'total_bookmarks' => $totalBookmarks,
            'total_folders' => $totalFolders,
            'total_tags' => $totalTags,
            'star_bookmarks' => $starBookmarks,
            'recent_added' => $recentAdded,
            'recent_visited' => $recentVisited,
            'growth_rate' => round($growthRate, 1),
            'most_used_tags' => $mostUsedTags,
            'folder_distribution' => $folderDistribution,
            'daily_activity' => $dailyActivity
        ];
    }

    /**
     * 获取标签统计
     * @param int $userId 用户ID
     * @param array $params 查询参数
     * @return array 标签统计数据
     */
    public function getTagStats(int $userId, array $params = []): array
    {
        $limit = min((int)($params['limit'] ?? 20), 100);
        $sort = $params['sort'] ?? 'usage';
        $order = $params['order'] ?? 'desc';

        // 排序字段映射
        $sortMap = [
            'usage' => 'usage_count',
            'name' => 'name',
            'created' => 'created_at'
        ];
        $sortField = $sortMap[$sort] ?? 'usage_count';

        // 获取标签列表
        $tags = Tag::where('user_id', $userId)
            ->order($sortField, $order)
            ->limit($limit)
            ->select();

        $totalTags = Tag::where('user_id', $userId)->count();
        $activeTags = Tag::where('user_id', $userId)->where('usage_count', '>', 0)->count();
        $unusedTags = $totalTags - $activeTags;
        $avgUsage = Tag::where('user_id', $userId)->avg('usage_count') ?: 0;

        $totalUsage = Tag::where('user_id', $userId)->sum('usage_count');

        $tagList = $tags->map(function($tag) use ($totalUsage) {
            return [
                'id' => $tag->id,
                'name' => $tag->name,
                'usage_count' => $tag->usage_count,
                'percentage' => $totalUsage > 0 ? round(($tag->usage_count / $totalUsage) * 100, 1) : 0,
                'recent_usage' => rand(0, 5), // 模拟最近使用次数
                'created_at' => $tag->created_at ? (is_string($tag->created_at) ? $tag->created_at : $tag->created_at->format('Y-m-d H:i:s')) : null
            ];
        })->toArray();

        return [
            'total_tags' => $totalTags,
            'active_tags' => $activeTags,
            'unused_tags' => $unusedTags,
            'avg_usage' => round($avgUsage, 1),
            'tags' => $tagList
        ];
    }

    /**
     * 获取访问统计
     * @param int $userId 用户ID
     * @param array $params 查询参数
     * @return array 访问统计数据
     */
    public function getVisitStats(int $userId, array $params = []): array
    {
        $days = min((int)($params['days'] ?? 30), 365);
        $type = $params['type'] ?? 'daily';
        $limit = min((int)($params['limit'] ?? 20), 100);

        // 模拟访问统计数据
        $totalBookmarks = Bookmark::where('user_id', $userId)->count();
        $totalVisits = $totalBookmarks * rand(2, 8); // 模拟总访问次数
        $uniqueBookmarks = (int)($totalBookmarks * 0.7); // 模拟被访问过的书签数
        $avgDailyVisits = round($totalVisits / $days, 1);

        // 最常访问的书签
        $mostVisited = Bookmark::where('user_id', $userId)
            ->with('tags')
            ->limit($limit)
            ->select()
            ->map(function($bookmark) {
                return [
                    'id' => $bookmark->id,
                    'title' => $bookmark->title,
                    'url' => $bookmark->url,
                    'visit_count' => rand(5, 50),
                    'last_visit' => date('Y-m-d H:i:s', time() - rand(0, 7 * 24 * 3600)),
                    'tags' => $bookmark->tags ? $bookmark->tags->column('name') : []
                ];
            })
            ->toArray();

        // 访问趋势
        $visitTrends = [];
        $interval = $type === 'daily' ? 1 : ($type === 'weekly' ? 7 : 30);
        $periods = (int)ceil($days / $interval);

        for ($i = $periods - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', time() - $i * $interval * 24 * 3600);
            $visitTrends[] = [
                'date' => $date,
                'visits' => rand(10, 100),
                'unique_visitors' => rand(5, 30)
            ];
        }

        // 时间分布（按小时）
        $timeDistribution = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $timeDistribution[] = [
                'hour' => $hour,
                'visits' => rand(0, 50),
                'percentage' => rand(1, 8)
            ];
        }

        return [
            'total_visits' => $totalVisits,
            'unique_bookmarks' => $uniqueBookmarks,
            'avg_daily_visits' => $avgDailyVisits,
            'most_visited' => $mostVisited,
            'visit_trends' => $visitTrends,
            'time_distribution' => $timeDistribution
        ];
    }

    /**
     * 生成报告
     * @param array $params 报告参数
     * @return array 报告信息
     */
    public function generateReport(array $params): array
    {
        $userId = $params['user_id'];
        $type = $params['type'] ?? 'summary';
        $days = $params['days'] ?? 30;
        $format = $params['format'] ?? 'json';
        $sections = $params['sections'] ?? ['overview', 'tags', 'visits'];

        $reportId = 'report_' . date('Ymd_His');
        $reportData = [];

        // 根据选择的部分生成报告数据
        if (in_array('overview', $sections)) {
            $reportData['overview'] = $this->getOverviewStats($userId, $days);
        }

        if (in_array('tags', $sections)) {
            $reportData['tags'] = $this->getTagStats($userId, ['limit' => 50]);
        }

        if (in_array('visits', $sections)) {
            $reportData['visits'] = $this->getVisitStats($userId, ['days' => $days]);
        }

        // 生成报告文件（这里简化处理）
        $fileName = $reportId . '.' . $format;
        $downloadUrl = '/api/v1/stats/download/' . $reportId;

        return [
            'report_id' => $reportId,
            'type' => $type,
            'format' => $format,
            'generated_at' => date('Y-m-d H:i:s'),
            'download_url' => $downloadUrl,
            'expires_at' => date('Y-m-d H:i:s', time() + 24 * 3600), // 24小时后过期
            'summary' => [
                'total_bookmarks' => Bookmark::where('user_id', $userId)->count(),
                'report_period' => date('Y-m-d', time() - $days * 24 * 3600) . ' to ' . date('Y-m-d'),
                'sections_included' => count($sections)
            ],
            'data' => $format === 'json' ? $reportData : null
        ];
    }
}
