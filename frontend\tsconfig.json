{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "auto-imports.d.ts", "components.d.ts", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["node"], "lib": ["ES2015", "DOM", "DOM.Iterable"], "target": "ES2015", "module": "ESNext", "moduleResolution": "bundler", "allowJs": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true}}