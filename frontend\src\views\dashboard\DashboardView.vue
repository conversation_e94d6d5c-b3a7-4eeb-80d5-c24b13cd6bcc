<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表板</h1>
      <p>欢迎使用书签管理系统</p>
    </div>
    
    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="32" color="#409eff"><Collection /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">1,234</div>
            <div class="stat-label">总书签数</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="32" color="#67c23a"><Folder /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">56</div>
            <div class="stat-label">文件夹数</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="32" color="#e6a23c"><PriceTag /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">89</div>
            <div class="stat-label">标签数</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="32" color="#f56c6c"><Star /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">123</div>
            <div class="stat-label">收藏书签</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <div class="dashboard-content">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card title="最近添加的书签">
            <div class="recent-bookmarks">
              <div class="bookmark-item" v-for="i in 5" :key="i">
                <div class="bookmark-info">
                  <div class="bookmark-title">示例书签 {{ i }}</div>
                  <div class="bookmark-url">https://example.com</div>
                </div>
                <div class="bookmark-time">2小时前</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card title="热门标签">
            <div class="tag-cloud">
              <el-tag
                v-for="tag in popularTags"
                :key="tag.name"
                :size="tag.size"
                class="tag-item"
              >
                {{ tag.name }}
              </el-tag>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Collection, Folder, PriceTag, Star } from '@element-plus/icons-vue'

const popularTags = ref([
  { name: '前端开发', size: 'large' },
  { name: 'Vue.js', size: 'default' },
  { name: 'JavaScript', size: 'large' },
  { name: 'CSS', size: 'small' },
  { name: 'TypeScript', size: 'default' },
  { name: 'Node.js', size: 'small' },
  { name: 'React', size: 'default' },
  { name: 'Python', size: 'small' }
])
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.dashboard-header p {
  margin: 0;
  color: #909399;
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.recent-bookmarks {
  space-y: 12px;
}

.bookmark-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.bookmark-item:last-child {
  border-bottom: none;
}

.bookmark-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.bookmark-url {
  font-size: 12px;
  color: #909399;
}

.bookmark-time {
  font-size: 12px;
  color: #c0c4cc;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  cursor: pointer;
}
</style>
