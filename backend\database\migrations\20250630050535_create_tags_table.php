<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateTagsTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $table = $this->table('tags', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => '标签表'
        ]);

        $table->addColumn('id', 'biginteger', [
            'identity' => true,
            'signed' => false,
            'comment' => '标签ID'
        ])
        ->addColumn('user_id', 'biginteger', [
            'signed' => false,
            'null' => false,
            'comment' => '用户ID'
        ])
        ->addColumn('name', 'string', [
            'limit' => 50,
            'null' => false,
            'comment' => '标签名称'
        ])
        ->addColumn('color', 'string', [
            'limit' => 7,
            'null' => true,
            'default' => null,
            'comment' => '标签颜色（#开头的16进制）'
        ])
        ->addColumn('usage_count', 'integer', [
            'signed' => false,
            'null' => false,
            'default' => 0,
            'comment' => '使用次数'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => false,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addColumn('updated_at', 'timestamp', [
            'null' => false,
            'default' => 'CURRENT_TIMESTAMP',
            'update' => 'CURRENT_TIMESTAMP',
            'comment' => '更新时间'
        ])
        ->addIndex(['user_id', 'name'], ['unique' => true, 'name' => 'uk_user_tag'])
        ->addIndex(['usage_count'], ['name' => 'idx_usage_count'])
        ->addForeignKey('user_id', 'users', 'id', [
            'delete' => 'CASCADE',
            'constraint' => 'fk_tags_users'
        ])
        ->create();
    }
}
