<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateUserPreferencesTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     */
    public function change()
    {
        $table = $this->table('user_preferences', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => '用户偏好设置表'
        ]);

        $table->addColumn('id', 'biginteger', [
            'identity' => true,
            'signed' => false,
            'comment' => '偏好设置ID'
        ])
        ->addColumn('user_id', 'biginteger', [
            'signed' => false,
            'null' => false,
            'comment' => '用户ID'
        ])
        ->addColumn('theme', 'string', [
            'limit' => 20,
            'null' => false,
            'default' => 'light',
            'comment' => '主题设置：light-浅色，dark-深色'
        ])
        ->addColumn('language', 'string', [
            'limit' => 10,
            'null' => false,
            'default' => 'zh-CN',
            'comment' => '语言设置：zh-CN-中文，en-US-英文'
        ])
        ->addColumn('items_per_page', 'integer', [
            'null' => false,
            'default' => 20,
            'comment' => '每页显示数量，范围10-100'
        ])
        ->addColumn('default_view', 'string', [
            'limit' => 20,
            'null' => false,
            'default' => 'list',
            'comment' => '默认视图：list-列表，grid-网格，card-卡片'
        ])
        ->addColumn('auto_backup', 'boolean', [
            'null' => false,
            'default' => true,
            'comment' => '自动备份：0-关闭，1-开启'
        ])
        ->addColumn('email_notifications', 'boolean', [
            'null' => false,
            'default' => true,
            'comment' => '邮件通知：0-关闭，1-开启'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => false,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addColumn('updated_at', 'timestamp', [
            'null' => false,
            'default' => 'CURRENT_TIMESTAMP',
            'update' => 'CURRENT_TIMESTAMP',
            'comment' => '更新时间'
        ])
        ->addIndex(['user_id'], ['unique' => true, 'name' => 'uk_user_id'])
        ->addForeignKey('user_id', 'users', 'id', [
            'delete' => 'CASCADE',
            'constraint' => 'fk_user_preferences_users'
        ])
        ->create();
    }
}
