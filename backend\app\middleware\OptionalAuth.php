<?php

namespace app\middleware;

use app\common\JwtAuth;
use think\Request;
use Closure;

/**
 * 可选JWT认证中间件
 * 如果提供了Token则验证，没有提供则跳过
 */
class OptionalAuth
{
    /**
     * 处理请求
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 获取Authorization头部
        $authorization = $request->header('Authorization');
        
        if (!empty($authorization)) {
            // 提取Token
            $token = JwtAuth::extractTokenFromHeader($authorization);
            
            if (!empty($token)) {
                // 验证Token
                $payload = JwtAuth::validateAccessToken($token);
                
                if ($payload && isset($payload['user_id']) && is_numeric($payload['user_id'])) {
                    // Token有效，设置用户信息
                    $request->userId = (int)$payload['user_id'];
                    $request->tokenPayload = $payload;
                }
            }
        }

        return $next($request);
    }
}
