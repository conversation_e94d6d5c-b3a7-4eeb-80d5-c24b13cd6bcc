<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑书签' : '添加书签'"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="formData.title"
          placeholder="请输入书签标题"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="网址" prop="url">
        <el-input
          v-model="formData.url"
          placeholder="请输入完整的网址，如：https://example.com"
          @blur="handleUrlBlur"
        >
          <template #prepend>
            <el-button :icon="Link" @click="fetchUrlInfo" :loading="fetchingInfo">
              获取信息
            </el-button>
          </template>
        </el-input>
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入书签描述（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="文件夹" prop="folder_id">
        <el-tree-select
          v-model="formData.folder_id"
          :data="folderOptions"
          :props="{ label: 'name', value: 'id', children: 'children' }"
          placeholder="选择文件夹（可选）"
          clearable
          check-strictly
          :render-after-expand="false"
        />
      </el-form-item>
      
      <el-form-item label="标签" prop="tags">
        <FormTagInput
          v-model="formData.tags"
          placeholder="输入标签后按回车添加"
          :max-tags="10"
          :suggestions="tagSuggestions"
          @input="handleTagInput"
        />
      </el-form-item>
      
      <el-form-item label="设置">
        <el-checkbox v-model="formData.is_star">收藏此书签</el-checkbox>
        <el-checkbox v-model="formData.is_private" style="margin-left: 16px;">
          私有书签
        </el-checkbox>
      </el-form-item>
      
      <!-- 预览区域 -->
      <el-form-item v-if="urlPreview.title" label="预览">
        <div class="url-preview">
          <div class="preview-favicon">
            <img 
              :src="urlPreview.favicon" 
              :alt="urlPreview.title"
              @error="handleFaviconError"
              v-if="showFavicon"
            />
            <el-icon v-else><Link /></el-icon>
          </div>
          <div class="preview-content">
            <div class="preview-title">{{ urlPreview.title }}</div>
            <div class="preview-description">{{ urlPreview.description }}</div>
            <div class="preview-url">{{ urlPreview.url }}</div>
          </div>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Link } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { Bookmark, CreateBookmarkRequest, Folder, Tag } from '@/types/api'
import { useBookmarkStore } from '@/stores/bookmark'
import { useFolderStore } from '@/stores/folder'
import { validate } from '@/utils/validate'
import FormTagInput from '@/components/form/FormTagInput.vue'

interface Props {
  modelValue: boolean
  bookmark?: Bookmark
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success', bookmark: Bookmark): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const bookmarkStore = useBookmarkStore()
const folderStore = useFolderStore()

const formRef = ref<FormInstance>()
const submitting = ref(false)
const fetchingInfo = ref(false)
const showFavicon = ref(true)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.bookmark)

// 表单数据
const formData = reactive<CreateBookmarkRequest & { is_private: boolean }>({
  title: '',
  url: '',
  description: '',
  folder_id: undefined,
  tags: [],
  is_star: false,
  is_private: false
})

// URL预览信息
const urlPreview = reactive({
  title: '',
  description: '',
  url: '',
  favicon: ''
})

// 标签建议
const tagSuggestions = ref<string[]>([])

// 文件夹选项
const folderOptions = computed(() => folderStore.folderTree)

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入书签标题', trigger: 'blur' },
    { max: 200, message: '标题长度不能超过200个字符', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入网址', trigger: 'blur' },
    { validator: (rule, value, callback) => {
      const error = validate.url(value)
      callback(error ? new Error(error) : undefined)
    }, trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过500个字符', trigger: 'blur' }
  ]
}

// 处理URL失焦
const handleUrlBlur = () => {
  if (formData.url && !formData.title) {
    fetchUrlInfo()
  }
}

// 获取URL信息
const fetchUrlInfo = async () => {
  if (!formData.url) return
  
  try {
    fetchingInfo.value = true
    
    // 调用后端API获取URL信息
    const response = await fetch('/api/v1/bookmarks/parse', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        url: formData.url
      })
    })
    const data = await response.json()

    if (data.code === 200 || data.code === 0) {
      const urlInfo = data.data
      urlPreview.title = urlInfo.title || ''
      urlPreview.description = urlInfo.summary || ''
      urlPreview.url = formData.url
      urlPreview.favicon = '' // AI解析不返回favicon，可以后续添加

      // 如果标题为空，自动填充
      if (!formData.title) {
        formData.title = urlInfo.title || ''
      }

      // 如果描述为空，自动填充
      if (!formData.description) {
        formData.description = urlInfo.summary || ''
      }

      // 如果有标签，自动填充
      if (urlInfo.tags && Array.isArray(urlInfo.tags) && urlInfo.tags.length > 0) {
        if (!formData.tags || formData.tags.length === 0) {
          formData.tags = [...urlInfo.tags]
        }
      }
    }
  } catch (error) {
    console.error('获取URL信息失败:', error)
  } finally {
    fetchingInfo.value = false
  }
}

// 处理标签输入
const handleTagInput = (query: string) => {
  if (query) {
    // 模拟标签建议
    tagSuggestions.value = [
      '前端开发',
      'Vue.js',
      'JavaScript',
      'TypeScript',
      '工具',
      '文档',
      '教程',
      '资源'
    ].filter(tag => tag.toLowerCase().includes(query.toLowerCase()))
  } else {
    tagSuggestions.value = []
  }
}

// 处理图标错误
const handleFaviconError = () => {
  showFavicon.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const submitData = {
      title: formData.title,
      url: formData.url,
      description: formData.description,
      folder_id: formData.folder_id,
      tags: formData.tags,
      is_star: formData.is_star
    }
    
    let result: Bookmark
    if (isEdit.value && props.bookmark) {
      result = await bookmarkStore.updateBookmark(props.bookmark.id, submitData)
    } else {
      result = await bookmarkStore.createBookmark(submitData)
    }
    
    ElMessage.success(isEdit.value ? '书签更新成功' : '书签添加成功')
    emit('success', result)
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    title: '',
    url: '',
    description: '',
    folder_id: null,
    tags: [],
    is_star: false,
    is_private: false
  })
  
  Object.assign(urlPreview, {
    title: '',
    description: '',
    url: '',
    favicon: ''
  })
  
  showFavicon.value = true
  formRef.value?.clearValidate()
}

// 初始化表单数据
const initFormData = () => {
  if (props.bookmark) {
    Object.assign(formData, {
      title: props.bookmark.title,
      url: props.bookmark.url,
      description: props.bookmark.description || '',
      folder_id: props.bookmark.folder_id,
      tags: props.bookmark.tags?.map(tag => tag.name) || [],
      is_star: props.bookmark.is_star,
      is_private: false // 假设有私有字段
    })
    
    Object.assign(urlPreview, {
      title: props.bookmark.title,
      description: props.bookmark.description || '',
      url: props.bookmark.url,
      favicon: `https://www.google.com/s2/favicons?domain=${new URL(props.bookmark.url).hostname}`
    })
  }
}

// 监听对话框显示
watch(visible, (show) => {
  if (show) {
    nextTick(() => {
      initFormData()
      // 获取文件夹列表
      if (folderOptions.value.length === 0) {
        folderStore.fetchFolders()
      }
    })
  } else {
    resetForm()
  }
})
</script>

<style scoped>
.url-preview {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.preview-favicon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-favicon img {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.preview-content {
  flex: 1;
  min-width: 0;
}

.preview-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.preview-description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.preview-url {
  font-size: 12px;
  color: #909399;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
