<?php

namespace app\service;

use app\exception\BusinessException;
use think\facade\Log;

/**
 * Moonshot AI服务类
 * 
 * 提供与Moonshot AI API的集成功能，用于网页内容解析
 */
class MoonshotService
{
    /**
     * Moonshot AI API配置
     */
    private const API_BASE_URL = 'https://api.moonshot.cn/v1';
    private const MODEL = 'moonshot-v1-8k';
    private const MAX_TOKENS = 2000;
    private const TEMPERATURE = 0.3;
    
    /**
     * API Key
     * @var string
     */
    private string $apiKey;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 从配置文件或环境变量获取API Key
        $this->apiKey = config('moonshot.api_key', 'sk-RwkkQfFQwX3J3EOlVyqrRHTkoSAWYD6wSVmqWUUIVjTIZ08G');
        
        if (empty($this->apiKey)) {
            throw new BusinessException('Moonshot AI API Key未配置');
        }
    }
    
    /**
     * 解析网页内容
     * 
     * @param string $url 要解析的网址
     * @return array 解析结果 ['title' => string, 'summary' => string, 'tags' => array]
     * @throws BusinessException
     */
    public function parseWebContent(string $url): array
    {
        try {
            // 验证URL格式
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                throw new BusinessException('无效的URL格式');
            }
            
            // 获取网页内容
            $webContent = $this->fetchWebContent($url);
            
            // 调用Moonshot AI API进行解析
            $aiResponse = $this->callMoonshotAPI($url, $webContent);
            
            // 解析AI响应
            $parsedResult = $this->parseAIResponse($aiResponse);
            
            Log::info('Moonshot AI解析成功', [
                'url' => $url,
                'title' => $parsedResult['title'] ?? '',
                'tags_count' => count($parsedResult['tags'] ?? [])
            ]);
            
            return $parsedResult;
            
        } catch (\Exception $e) {
            Log::error('Moonshot AI解析失败', [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            if ($e instanceof BusinessException) {
                throw $e;
            }
            
            throw new BusinessException('AI解析失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取网页内容
     *
     * @param string $url
     * @return string
     * @throws BusinessException
     */
    private function fetchWebContent(string $url): string
    {
        $maxRetries = 3;
        $retryDelay = 2; // 秒

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            $ch = curl_init();

            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 5,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_HTTPHEADER => [
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding: gzip, deflate',
                    'Cache-Control: no-cache',
                ],
                CURLOPT_ENCODING => '', // 自动处理gzip
            ]);

            $content = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);

            curl_close($ch);

            // 如果成功，直接返回
            if (!$error && $httpCode === 200 && !empty($content)) {
                // 提取文本内容，移除HTML标签
                $textContent = $this->extractTextFromHtml($content);

                // 限制内容长度，避免API调用过大
                if (strlen($textContent) > 8000) {
                    $textContent = substr($textContent, 0, 8000) . '...';
                }

                return $textContent;
            }

            // 记录重试日志
            Log::warning("网页获取失败，第{$attempt}次尝试", [
                'url' => $url,
                'http_code' => $httpCode,
                'error' => $error,
                'attempt' => $attempt
            ]);

            // 如果不是最后一次尝试，等待后重试
            if ($attempt < $maxRetries) {
                sleep($retryDelay);
                $retryDelay *= 2; // 指数退避
            }
        }

        // 所有重试都失败了
        if ($error) {
            throw new BusinessException('网页获取失败：' . $error);
        }

        if ($httpCode !== 200) {
            throw new BusinessException("网页访问失败，HTTP状态码：{$httpCode}");
        }

        throw new BusinessException('网页内容为空或无法获取');
    }
    
    /**
     * 从HTML中提取文本内容
     * 
     * @param string $html
     * @return string
     */
    private function extractTextFromHtml(string $html): string
    {
        // 移除script和style标签及其内容
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);
        
        // 移除HTML标签
        $text = strip_tags($html);
        
        // 清理空白字符
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);
        
        return $text;
    }
    
    /**
     * 调用Moonshot AI API
     * 
     * @param string $url
     * @param string $content
     * @return array
     * @throws BusinessException
     */
    private function callMoonshotAPI(string $url, string $content): array
    {
        $prompt = $this->buildPrompt($url, $content);
        
        $requestData = [
            'model' => self::MODEL,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => self::MAX_TOKENS,
            'temperature' => self::TEMPERATURE,
        ];
        
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => self::API_BASE_URL . '/chat/completions',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($requestData),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey,
            ],
            CURLOPT_TIMEOUT => 60,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            throw new BusinessException('Moonshot AI API调用失败：' . $error);
        }
        
        $responseData = json_decode($response, true);
        
        if ($httpCode !== 200) {
            $errorMessage = $responseData['error']['message'] ?? "HTTP错误：{$httpCode}";
            throw new BusinessException('Moonshot AI API错误：' . $errorMessage);
        }
        
        if (!isset($responseData['choices'][0]['message']['content'])) {
            throw new BusinessException('Moonshot AI API响应格式错误');
        }
        
        return $responseData;
    }
    
    /**
     * 构建AI提示词
     * 
     * @param string $url
     * @param string $content
     * @return string
     */
    private function buildPrompt(string $url, string $content): string
    {
        return "请分析以下网址和内容，并以JSON对象格式返回结果。JSON对象应包含三个键：'title'（字符串，作为网页标题），'summary'（字符串，一段1000字内的网页总结，使用中文），和 'tags'（一个包含3-5个相关关键词的字符串数组）。\n\n" .
               "网址：{$url}\n\n" .
               "网页内容：\n{$content}\n\n" .
               "请确保返回的是有效的JSON格式，不要包含任何其他文本。";
    }
    
    /**
     * 解析AI响应
     * 
     * @param array $aiResponse
     * @return array
     * @throws BusinessException
     */
    private function parseAIResponse(array $aiResponse): array
    {
        $content = $aiResponse['choices'][0]['message']['content'];
        
        // 尝试解析JSON
        $jsonData = json_decode($content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            // 如果直接解析失败，尝试提取JSON部分
            if (preg_match('/\{.*\}/s', $content, $matches)) {
                $jsonData = json_decode($matches[0], true);
            }
        }
        
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($jsonData)) {
            throw new BusinessException('AI响应解析失败，返回内容不是有效的JSON格式');
        }
        
        // 验证必需字段
        if (!isset($jsonData['title']) || !isset($jsonData['summary']) || !isset($jsonData['tags'])) {
            throw new BusinessException('AI响应缺少必需字段（title、summary、tags）');
        }
        
        // 数据清理和验证
        $result = [
            'title' => trim($jsonData['title']),
            'summary' => trim($jsonData['summary']),
            'tags' => array_filter(array_map('trim', (array)$jsonData['tags']))
        ];
        
        // 验证数据有效性
        if (empty($result['title'])) {
            throw new BusinessException('AI解析的标题为空');
        }
        
        if (empty($result['summary'])) {
            throw new BusinessException('AI解析的摘要为空');
        }
        
        if (empty($result['tags'])) {
            $result['tags'] = ['网页', '内容'];
        }
        
        // 限制标签数量
        if (count($result['tags']) > 10) {
            $result['tags'] = array_slice($result['tags'], 0, 10);
        }
        
        return $result;
    }
}
