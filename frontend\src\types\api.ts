// API相关类型定义

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

export interface PaginatedResponse<T = any> {
  code: number
  message: string
  data: {
    list: T[]
    current_page: number
    per_page: number
    total: number
    last_page: number
  }
  timestamp?: number
}

export interface ApiError {
  code: number
  message: string
  errors?: Record<string, string[]>
}

// 书签相关类型
export interface Bookmark {
  id: number
  title: string
  url: string
  description?: string
  favicon?: string
  folder_id?: number
  tags: Tag[]
  is_star: boolean
  visit_count: number
  created_at: string
  updated_at: string
}

export interface CreateBookmarkRequest {
  title: string
  url: string
  description?: string
  folder_id?: number
  tags?: string[]
  is_star?: boolean
}

// 文件夹相关类型
export interface Folder {
  id: number
  name: string
  parent_id?: number
  description?: string
  bookmark_count: number
  children?: Folder[]
  created_at: string
  updated_at: string
}

export interface CreateFolderRequest {
  name: string
  parent_id?: number
  description?: string
}

// 标签相关类型
export interface Tag {
  id: number
  name: string
  color?: string
  bookmark_count: number
  created_at: string
  updated_at: string
}

export interface CreateTagRequest {
  name: string
  color?: string
}

// 搜索相关类型
export interface SearchParams {
  q?: string
  folder_id?: number
  tag_ids?: number[]
  is_star?: boolean
  date_from?: string
  date_to?: string
  page?: number
  per_page?: number
}

// 统计相关类型
export interface StatsOverview {
  total_bookmarks: number
  total_folders: number
  total_tags: number
  star_bookmarks: number
  recent_added: number
  recent_visited: number
}

// 智能文件夹相关类型
export interface SmartFolder {
  id: number
  name: string
  description?: string
  rules: SmartFolderRule[]
  bookmarkCount: number
  created_at: string
  updated_at: string
}

export interface SmartFolderRule {
  id: number
  field: 'title' | 'url' | 'description' | 'tags' | 'folder'
  operator: 'contains' | 'not_contains' | 'equals' | 'not_equals' | 'starts_with' | 'ends_with'
  value: string
  logic?: 'and' | 'or'
}

export interface CreateSmartFolderRequest {
  name: string
  description?: string
  rules: Omit<SmartFolderRule, 'id'>[]
}

// 导入导出相关类型
export interface ImportResult {
  success: number
  failed: number
  skipped: number
  errors?: string[]
}

export interface ExportResult {
  filename: string
  size: number
  format: string
}

// 回收站相关类型
export interface TrashItem {
  id: number
  type: 'bookmark' | 'folder'
  original_data: Bookmark | Folder
  deleted_at: string
  expires_at: string
}
