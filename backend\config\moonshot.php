<?php

return [
    // Moonshot AI API配置
    'api_key' => env('MOONSHOT_API_KEY', 'sk-RwkkQfFQwX3J3EOlVyqrRHTkoSAWYD6wSVmqWUUIVjTIZ08G'),
    
    // API基础URL
    'api_base_url' => 'https://api.moonshot.cn/v1',
    
    // 默认模型
    'model' => 'moonshot-v1-8k',
    
    // 最大token数
    'max_tokens' => 2000,
    
    // 温度参数（0-1，控制输出的随机性）
    'temperature' => 0.3,
    
    // 请求超时时间（秒）
    'timeout' => 60,
    
    // 网页内容获取超时时间（秒）
    'web_timeout' => 30,
    
    // 最大网页内容长度（字符）
    'max_content_length' => 8000,
    
    // 最大标签数量
    'max_tags' => 10,
];
