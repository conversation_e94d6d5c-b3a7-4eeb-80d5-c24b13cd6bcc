import { defineStore } from 'pinia'
import type { User, LoginForm, LoginResponse } from '@/types/user'
import { authApi } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const user = ref<User | null>(null)
  const isLoggedIn = computed(() => !!token.value)

  // 初始化
  const initAuth = () => {
    const savedToken = localStorage.getItem('token')
    const savedRefreshToken = localStorage.getItem('refreshToken')
    const savedUser = localStorage.getItem('user')

    if (savedToken) {
      token.value = savedToken
    }
    if (savedRefreshToken) {
      refreshToken.value = savedRefreshToken
    }
    if (savedUser) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
      }
    }
  }

  // 设置认证信息
  const setAuth = (authData: LoginResponse) => {
    token.value = authData.access_token
    refreshToken.value = authData.refresh_token
    user.value = authData.user

    // 保存到本地存储
    localStorage.setItem('token', authData.access_token)
    localStorage.setItem('refreshToken', authData.refresh_token)
    localStorage.setItem('user', JSON.stringify(authData.user))
  }

  // 清除认证信息
  const clearAuth = () => {
    token.value = ''
    refreshToken.value = ''
    user.value = null

    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('user')
  }

  // 登录
  const login = async (loginForm: LoginForm): Promise<void> => {
    try {
      const response = await authApi.login(loginForm)
      setAuth(response.data)
    } catch (error) {
      console.error('登录错误:', error)
      throw error
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出错误:', error)
    } finally {
      clearAuth()
    }
  }

  // 刷新Token
  const refreshAccessToken = async (): Promise<boolean> => {
    try {
      if (!refreshToken.value) {
        return false
      }

      const response = await authApi.refreshToken(refreshToken.value)

      token.value = response.data.access_token
      localStorage.setItem('token', response.data.access_token)

      return true
    } catch (error) {
      console.error('刷新Token错误:', error)
      return false
    }
  }

  // 更新用户信息
  const updateUser = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  return {
    // 状态
    token: readonly(token),
    refreshToken: readonly(refreshToken),
    user: readonly(user),
    isLoggedIn,
    
    // 方法
    initAuth,
    setAuth,
    clearAuth,
    login,
    logout,
    refreshAccessToken,
    updateUser
  }
})
