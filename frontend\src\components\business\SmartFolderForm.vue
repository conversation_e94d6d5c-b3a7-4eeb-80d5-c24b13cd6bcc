<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑智能文件夹' : '创建智能文件夹'"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="文件夹名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入智能文件夹名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入文件夹描述（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="筛选规则" prop="rules">
        <div class="rules-container">
          <div
            v-for="(rule, index) in formData.rules"
            :key="index"
            class="rule-item"
          >
            <div class="rule-row">
              <el-select
                v-model="rule.field"
                placeholder="选择字段"
                style="width: 120px"
              >
                <el-option label="标题" value="title" />
                <el-option label="网址" value="url" />
                <el-option label="描述" value="description" />
                <el-option label="标签" value="tags" />
                <el-option label="文件夹" value="folder" />
              </el-select>
              
              <el-select
                v-model="rule.operator"
                placeholder="选择条件"
                style="width: 120px; margin-left: 10px"
              >
                <el-option label="包含" value="contains" />
                <el-option label="不包含" value="not_contains" />
                <el-option label="等于" value="equals" />
                <el-option label="不等于" value="not_equals" />
                <el-option label="开头是" value="starts_with" />
                <el-option label="结尾是" value="ends_with" />
              </el-select>
              
              <el-input
                v-model="rule.value"
                placeholder="请输入值"
                style="width: 200px; margin-left: 10px"
              />
              
              <el-select
                v-if="index < formData.rules.length - 1"
                v-model="rule.logic"
                placeholder="逻辑"
                style="width: 80px; margin-left: 10px"
              >
                <el-option label="且" value="and" />
                <el-option label="或" value="or" />
              </el-select>
              
              <el-button
                type="danger"
                :icon="Delete"
                circle
                size="small"
                style="margin-left: 10px"
                @click="removeRule(index)"
                :disabled="formData.rules.length === 1"
              />
            </div>
          </div>
          
          <el-button
            type="primary"
            :icon="Plus"
            @click="addRule"
            style="margin-top: 10px"
          >
            添加规则
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import type { SmartFolder, SmartFolderRule, CreateSmartFolderRequest } from '@/types/api'
import { useSmartFolderStore } from '@/stores/smartFolder'

// Props
interface Props {
  modelValue: boolean
  smartFolder?: SmartFolder | null
}

const props = withDefaults(defineProps<Props>(), {
  smartFolder: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': [smartFolder: SmartFolder]
}>()

// Store
const smartFolderStore = useSmartFolderStore()

// Refs
const formRef = ref<FormInstance>()
const submitting = ref(false)

// Computed
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.smartFolder)

// Form data
const formData = reactive<CreateSmartFolderRequest>({
  name: '',
  description: '',
  rules: [
    {
      field: 'title',
      operator: 'contains',
      value: '',
      logic: 'and'
    }
  ]
})

// Form rules
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入文件夹名称', trigger: 'blur' },
    { min: 1, max: 100, message: '名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  rules: [
    {
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('至少需要一个筛选规则'))
          return
        }
        
        for (let i = 0; i < value.length; i++) {
          const ruleItem = value[i]
          if (!ruleItem.field || !ruleItem.operator || !ruleItem.value.trim()) {
            callback(new Error(`第 ${i + 1} 个规则不完整`))
            return
          }
        }
        
        callback()
      },
      trigger: 'blur'
    }
  ]
}

// Methods
const addRule = () => {
  formData.rules.push({
    field: 'title',
    operator: 'contains',
    value: '',
    logic: 'and'
  })
}

const removeRule = (index: number) => {
  if (formData.rules.length > 1) {
    formData.rules.splice(index, 1)
  }
}

const resetForm = () => {
  formData.name = ''
  formData.description = ''
  formData.rules = [
    {
      field: 'title',
      operator: 'contains',
      value: '',
      logic: 'and'
    }
  ]
  formRef.value?.clearValidate()
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 清理最后一个规则的 logic 字段
    const rules = formData.rules.map((rule, index) => {
      const cleanRule = { ...rule }
      if (index === formData.rules.length - 1) {
        delete cleanRule.logic
      }
      return cleanRule
    })
    
    const submitData = {
      ...formData,
      rules
    }
    
    let result: SmartFolder
    
    if (isEdit.value && props.smartFolder) {
      result = await smartFolderStore.updateSmartFolder(props.smartFolder.id, submitData)
      ElMessage.success('智能文件夹更新成功')
    } else {
      result = await smartFolderStore.createSmartFolder(submitData)
      ElMessage.success('智能文件夹创建成功')
    }
    
    emit('success', result)
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// Watch for edit mode
watch(
  () => props.smartFolder,
  (smartFolder) => {
    if (smartFolder && visible.value) {
      formData.name = smartFolder.name
      formData.description = smartFolder.description || ''
      formData.rules = smartFolder.rules.map(rule => ({ ...rule }))
    }
  },
  { immediate: true }
)

// Watch for dialog visibility
watch(visible, (newVisible) => {
  if (newVisible && props.smartFolder) {
    formData.name = props.smartFolder.name
    formData.description = props.smartFolder.description || ''
    formData.rules = props.smartFolder.rules.map(rule => ({ ...rule }))
  } else if (!newVisible) {
    resetForm()
  }
})
</script>

<style scoped>
.rules-container {
  width: 100%;
}

.rule-item {
  margin-bottom: 10px;
}

.rule-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.dialog-footer {
  text-align: right;
}

@media (max-width: 768px) {
  .rule-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .rule-row .el-select,
  .rule-row .el-input {
    width: 100% !important;
    margin-left: 0 !important;
  }
}
</style>
