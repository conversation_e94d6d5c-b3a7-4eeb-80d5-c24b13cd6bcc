<?php

namespace app\controller\api\v1;

use app\BaseController;
use app\service\ExportService;
use app\validate\ExportValidate;
use app\exception\BusinessException;
use think\Response;

/**
 * 导出控制器
 *
 * 提供书签导出功能，支持多种格式的书签文件导出
 */
class Export extends BaseController
{
    /**
     * 导出服务
     * @var ExportService
     */
    protected $exportService;

    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->exportService = new ExportService();
    }

    /**
     * 导出书签
     *
     * 导出用户的书签数据为指定格式的文件
     *
     * @route POST /api/v1/export
     * @middleware auth
     * @param string $format 导出格式：html|json|csv，默认html
     * @param array $folder_ids 要导出的文件夹ID列表，可选，空表示导出所有
     * @param bool $include_folders 是否包含文件夹结构，默认true
     * @param bool $include_tags 是否包含标签信息，默认true
     * @param string $encoding 文件编码：utf-8|gbk，默认utf-8
     * @return Response 文件下载响应或JSON响应包含下载链接
     * @throws BusinessException 参数验证失败或导出失败
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/export
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "format": "html",
     *   "folder_ids": [1, 2, 3],
     *   "include_folders": true,
     *   "include_tags": true,
     *   "encoding": "utf-8"
     * }
     * Response: {
     *   "code": 200,
     *   "message": "导出书签成功",
     *   "data": {
     *     "job_id": "export_20240630_123456",
     *     "download_url": "/api/v1/export/download/export_20240630_123456",
     *     "file_name": "bookmarks_20240630.html",
     *     "file_size": 1024000,
     *     "total_bookmarks": 150,
     *     "expires_at": "2024-06-30 18:00:00"
     *   }
     * }
     */
    public function export(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            // 获取参数
            $params = $this->request->post();
            $params['user_id'] = $userId;

            // 参数验证
            $this->validate($params, ExportValidate::class . '.export');

            // 执行导出
            $result = $this->exportService->exportBookmarks($params);

            return $this->success($result, '导出书签成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('导出书签失败：' . $e->getMessage());
        }
    }
}
