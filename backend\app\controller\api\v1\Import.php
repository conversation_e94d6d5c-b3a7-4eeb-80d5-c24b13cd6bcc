<?php

namespace app\controller\api\v1;

use app\BaseController;
use app\service\ImportService;
use app\validate\ImportValidate;
use app\exception\BusinessException;
use think\Response;

/**
 * 导入控制器
 *
 * 提供书签导入功能，支持多种格式的书签文件导入
 */
class Import extends BaseController
{
    /**
     * 导入服务
     * @var ImportService
     */
    protected $importService;

    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->importService = new ImportService();
    }

    /**
     * 导入书签
     *
     * 支持导入浏览器书签文件（HTML格式）或JSON格式的书签数据
     *
     * @route POST /api/v1/import
     * @middleware auth
     * @param file $file 书签文件，支持HTML、JSON格式，最大10MB
     * @param int $folder_id 目标文件夹ID，可选，null表示导入到根目录
     * @param bool $merge_duplicates 是否合并重复书签，默认true
     * @param bool $create_folders 是否创建文件夹结构，默认true
     * @return Response JSON响应，包含导入结果和统计信息
     * @throws BusinessException 文件格式不支持或解析失败
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/import
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Content-Type: multipart/form-data
     *
     * file: bookmarks.html
     * folder_id: 1
     * merge_duplicates: true
     * create_folders: true
     *
     * Response: {
     *   "code": 200,
     *   "message": "导入书签成功",
     *   "data": {
     *     "job_id": "import_20240630_123456",
     *     "total_count": 150,
     *     "imported_count": 145,
     *     "skipped_count": 5,
     *     "folders_created": 8,
     *     "tags_created": 12,
     *     "status": "completed",
     *     "errors": []
     *   }
     * }
     */
    public function import(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            // 获取上传的文件
            $file = $this->request->file('file');
            if (!$file) {
                return $this->error('请选择要导入的文件', 40001);
            }

            // 获取其他参数
            $params = $this->request->post();
            $params['user_id'] = $userId;

            // 参数验证
            $this->validate($params, ImportValidate::class . '.import');

            // 执行导入
            $result = $this->importService->importBookmarks($file, $params);

            return $this->success($result, '导入书签成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('导入书签失败：' . $e->getMessage());
        }
    }
}
