// 用户相关类型定义

export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  created_at: string
  updated_at: string
}

export interface LoginForm {
  account: string
  password: string
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  password_confirmation: string
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  expires_in: number
  user: User
}

export interface UserProfile {
  id: number
  username: string
  email: string
  avatar?: string
  nickname?: string
  bio?: string
  created_at: string
  updated_at: string
}
