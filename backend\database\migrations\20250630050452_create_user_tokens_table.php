<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateUserTokensTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $table = $this->table('user_tokens', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => '用户认证令牌表'
        ]);

        $table->addColumn('id', 'biginteger', [
            'identity' => true,
            'signed' => false,
            'comment' => '令牌ID'
        ])
        ->addColumn('user_id', 'biginteger', [
            'signed' => false,
            'null' => false,
            'comment' => '用户ID'
        ])
        ->addColumn('token_type', 'string', [
            'limit' => 20,
            'null' => false,
            'default' => 'refresh',
            'comment' => '令牌类型：refresh'
        ])
        ->addColumn('token', 'string', [
            'limit' => 500,
            'null' => false,
            'comment' => '令牌值'
        ])
        ->addColumn('expires_at', 'timestamp', [
            'null' => false,
            'comment' => '过期时间'
        ])
        ->addColumn('device_info', 'string', [
            'limit' => 255,
            'null' => true,
            'default' => null,
            'comment' => '设备信息'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => false,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addIndex(['user_id'], ['name' => 'idx_user_id'])
        ->addIndex(['token'], ['name' => 'idx_token'])
        ->addIndex(['expires_at'], ['name' => 'idx_expires_at'])
        ->addForeignKey('user_id', 'users', 'id', [
            'delete' => 'CASCADE',
            'constraint' => 'fk_user_tokens_users'
        ])
        ->create();
    }
}
