# 📚 Web端书签管理系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![PHP Version](https://img.shields.io/badge/PHP-8.1%2B-blue.svg)](https://php.net)
[![ThinkPHP](https://img.shields.io/badge/ThinkPHP-8.0-green.svg)](https://www.thinkphp.cn)
[![API Version](https://img.shields.io/badge/API-v1.0-orange.svg)](https://vscode.qidian.cc/api/v1)

一个功能强大、界面优雅的个人书签管理系统，基于ThinkPHP 8.0开发，提供完整的RESTful API接口，支持AI智能解析和数据洞察，让书签管理变得高效而愉悦。

## 🌟 核心特性

### 📚 智能管理
- **无限层级文件夹**：支持拖拽操作，轻松组织书签结构
- **多维度标签系统**：扁平化标签设计，支持批量操作和合并
- **智能文件夹**：基于条件动态筛选，自动归类书签
- **回收站保护**：软删除机制，支持恢复和永久删除

### 🤖 AI赋能
- **内容智能解析**：自动生成摘要，提取关键词和标题
- **智能分类**：识别网页类型（文章/视频/代码/新闻）
- **标签推荐**：基于内容智能推荐相关标签
- **死链检测**：自动检测和标记失效链接

### 🔍 高效搜索
- **全文检索**：覆盖标题、URL、描述、标签、AI摘要
- **高级筛选**：支持多条件组合查询
- **搜索历史**：记录和管理搜索历史

### 📊 数据洞察
- **可视化统计**：标签云、访问排行、收藏趋势
- **智能报告**：生成详细的使用统计报告
- **导入导出**：支持HTML、JSON、CSV格式

## 🛠 技术栈

### 后端 (已完成)
- **PHP 8.1+** - 现代PHP语言特性
- **ThinkPHP 8.0** - 高性能PHP框架
- **MySQL 8.0+** - 关系型数据库
- **Redis 7.0+** - 缓存与会话存储
- **JWT** - JSON Web Token认证
- **OpenAPI 3.0.3** - API文档标准

### 前端 (规划中)
- **Vue 3** + Composition API - 渐进式JavaScript框架
- **Vite 4** - 下一代前端构建工具
- **Element Plus** - 基于Vue 3的组件库
- **Pinia** - 新一代状态管理
- **Tailwind CSS** - 原子化CSS框架

### 基础设施
- **Nginx** - 高性能Web服务器
- **Supervisor** - 进程管理
- **Docker** - 容器化部署

## 🚀 快速开始

### 环境要求
- PHP >= 8.1
- MySQL >= 8.0
- Redis >= 7.0 (可选)
- Composer >= 2.0
- Node.js >= 16.0 (前端开发)

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/yourusername/bookmark-manager.git
cd bookmark-manager
```

2. **后端安装**
```bash
# 进入后端目录
cd backend

# 安装ThinkPHP框架
composer create-project topthink/think .

# 安装项目依赖
composer install

# 复制环境配置
cp .env.example .env

# 编辑.env文件配置数据库连接信息
# 配置数据库连接、Redis连接等

# 创建数据库
mysql -u root -p -e "CREATE DATABASE bookmark_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行数据库迁移
php think migrate:run

# 启动开发服务器
php think run
```

3. **访问系统**
- **API地址**: https://vscode.qidian.cc/api/v1
- **API文档**: [OpenAPI 3.0.3 文档](backend/docs/api.yaml)
- **ThinkPHP欢迎页**: https://vscode.qidian.cc
- **开发环境**: http://localhost:8000

4. **API测试**

**🎯 推荐使用可视化测试页面**：
- 打开浏览器访问项目根目录下的 `test_api.html` 文件
- 提供完整的图形化API测试界面，支持：
  - ✅ 系统状态检测和服务器连接测试
  - ✅ 用户注册和登录（支持自动生成测试数据）
  - ✅ AI解析URL功能测试
  - ✅ 批量URL测试和完整测试套件
  - ✅ 友好的错误提示和结果展示
  - ✅ 自动Token管理和持久化存储

**命令行测试**：
```bash
# 测试系统配置接口
curl http://localhost:8000/api/v1/system/status

# 用户注册
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"test","email":"<EMAIL>","password":"123456","password_confirm":"123456"}'

# AI解析URL (需要先登录获取token)
curl -X POST http://localhost:8000/api/v1/bookmarks/parse \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{"url":"https://www.github.com"}'
```

**详细测试指南**：查看 `API_TEST_GUIDE.md` 获取完整的API测试说明文档。

## 📖 项目文档

### 📋 需求文档
- [产品需求文档 (PRD)](Docs/1.0Web端书签管理系统.md)
- [用户故事](Docs/1.1用户故事.md)

### 🏗️ 技术文档
- [项目架构文档](Docs/1.2项目架构文档.md)
- [数据库设计文档](Docs/1.3.2数据库设计文档.md)
- [数据字典](Docs/1.4数据字典.md)
- [数据流设计文档](Docs/1.3.1数据流设计文档.md)
- [统一错误码规范](Docs/1.6统一错误码规范.md)

### 🔌 API文档
- **[OpenAPI 3.0.3 规范文档](backend/docs/api.yaml)** - 完整的API接口定义
- **接口总数**: 57个API接口，覆盖11个功能模块
- **在线测试**: 支持Postman、Swagger UI等工具导入
- **认证方式**: JWT Bearer Token

#### API模块列表
- 🔐 **用户认证** (4个接口): 注册、登录、刷新令牌、登出
- 👤 **用户管理** (7个接口): 个人信息、密码修改、设备管理、偏好设置
- 🗂️ **文件夹管理** (7个接口): CRUD操作、移动、排序
- 🏷️ **标签管理** (6个接口): CRUD操作、合并、标签云
- 📚 **书签管理** (12个接口): CRUD操作、批量操作、AI解析、死链检测
- 🔍 **搜索功能** (2个接口): 全文搜索、高级筛选
- 🤖 **智能文件夹** (5个接口): 动态筛选文件夹管理
- 📥 **导入导出** (3个接口): 文件导入导出、任务状态查询
- 📊 **统计分析** (4个接口): 概览统计、标签统计、访问统计、报告生成
- 🗑️ **回收站** (5个接口): 回收站管理、恢复、永久删除
- ⚙️ **系统管理** (2个接口): 系统配置、状态监控

## 🏗 项目结构

```
bookmark-manager/
├── backend/                    # 后端ThinkPHP项目
│   ├── app/                   # 应用目录
│   │   ├── controller/        # 控制器层
│   │   │   └── api/v1/       # API v1控制器
│   │   ├── model/            # 数据模型层
│   │   ├── service/          # 业务服务层
│   │   ├── middleware/       # 中间件
│   │   ├── validate/         # 验证器
│   │   └── common.php        # 公共函数
│   ├── config/               # 配置文件
│   ├── database/             # 数据库迁移文件
│   ├── docs/                 # API文档
│   │   └── api.yaml         # OpenAPI 3.0.3规范文档
│   ├── public/               # Web入口目录
│   ├── route/                # 路由配置
│   │   └── app.php          # API路由定义
│   ├── runtime/              # 运行时目录
│   ├── vendor/               # Composer依赖
│   ├── .env.example          # 环境配置示例
│   └── composer.json         # PHP依赖配置
├── frontend/                  # 前端项目 (规划中)
│   ├── src/                  # Vue.js源代码
│   ├── public/               # 静态资源
│   └── dist/                 # 构建输出
├── Docs/                     # 项目文档
│   ├── 1.0Web端书签管理系统.md
│   ├── 1.2项目架构文档.md
│   ├── 1.3.2数据库设计文档.md
│   └── ...                   # 其他设计文档
├── docker/                   # Docker配置 (规划中)
├── tests/                    # 测试目录 (规划中)
└── README.md                 # 项目说明
```

## 🔧 开发指南

### 本地开发

1. **启动后端服务**
```bash
cd backend
php think run
# 或者使用内置服务器
php -S localhost:8000 -t public
```

2. **API测试**
```bash
# 导入API文档到Postman
# 文件路径: backend/docs/api.yaml

# 或使用curl测试
curl -X GET https://vscode.qidian.cc/api/v1/system/config
```

3. **数据库操作**
```bash
# 创建迁移文件
php think make:migration CreateUsersTable

# 运行迁移
php think migrate:run

# 回滚迁移
php think migrate:rollback
```

### 代码规范

- **PHP代码**: 遵循 PSR-12 标准
- **API设计**: 遵循 RESTful 规范
- **文档规范**: 使用 OpenAPI 3.0.3 标准
- **Git提交**: 遵循 Conventional Commits 规范
- **注释规范**: 使用 PHPDoc 标准注释

### API开发规范

```php
/**
 * @route GET /api/v1/bookmarks
 * @middleware auth
 * @param int page 页码
 * @param int per_page 每页数量
 * @return array 书签列表
 * @throws 401 未授权访问
 * @throws 422 参数验证失败
 */
public function index()
{
    // 控制器实现
}
```

## 🚢 部署指南

### 生产环境部署

1. **Nginx配置**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/bookmark-manager/backend/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

2. **环境配置**
```bash
# 复制生产环境配置
cp .env.example .env

# 编辑生产环境配置
# 设置数据库连接、Redis连接、JWT密钥等
```

3. **权限设置**
```bash
# 设置目录权限
chmod -R 755 backend/
chmod -R 777 backend/runtime/
```

### Docker部署 (规划中)

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

## 🤝 贡献指南

我们欢迎所有形式的贡献，包括但不限于：

- 报告Bug
- 提交功能建议
- 改进文档
- 提交代码

请查看 [贡献指南](CONTRIBUTING.md) 了解更多信息。

## 📄 开源协议

本项目采用 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

感谢所有为本项目做出贡献的开发者！

特别感谢以下开源项目：
- Vue.js
- ThinkPHP
- Element Plus
- 以及所有其他依赖的开源项目

## 🎯 开发里程碑

### Phase 1: 基础架构 ✅ (已完成)
- [x] 项目架构设计
- [x] 数据库设计
- [x] API接口设计
- [x] OpenAPI文档生成
- [x] 开发环境搭建

### Phase 2: 后端开发 � (进行中)
- [ ] 用户认证模块实现
- [ ] 文件夹管理模块实现
- [ ] 标签管理模块实现
- [ ] 书签管理模块实现
- [ ] 搜索功能实现

### Phase 3: 前端开发 📅 (计划中)
- [ ] Vue.js项目搭建
- [ ] 用户界面设计
- [ ] 组件开发
- [ ] 状态管理
- [ ] 前后端联调

### Phase 4: 高级功能 📅 (计划中)
- [ ] AI智能解析
- [ ] 数据统计分析
- [ ] 导入导出功能
- [ ] 性能优化

## �📞 联系我们

- **项目主页**: [https://github.com/yourusername/bookmark-manager](https://github.com/yourusername/bookmark-manager)
- **Issue反馈**: [https://github.com/yourusername/bookmark-manager/issues](https://github.com/yourusername/bookmark-manager/issues)
- **API文档**: [OpenAPI 3.0.3 规范](backend/docs/api.yaml)
- **开发环境**: https://vscode.qidian.cc

## 📈 项目状态

### ✅ 已完成 (v1.0.0 - 2024-06-30)

#### 🏗️ **基础架构**
- ✅ ThinkPHP 8.0 框架搭建
- ✅ MVC+Service 架构设计
- ✅ 数据库设计与迁移文件
- ✅ 统一错误处理机制
- ✅ JWT认证中间件
- ✅ 环境配置管理

#### 📚 **完整文档体系**
- ✅ 产品需求文档 (PRD)
- ✅ 数据库设计文档
- ✅ API接口设计文档
- ✅ **OpenAPI 3.0.3 规范文档** (57个接口)
- ✅ 统一错误码规范
- ✅ 项目架构文档

#### 🔌 **API接口设计**
- ✅ **11个功能模块**，**57个API接口**
- ✅ 完整的RESTful API设计
- ✅ JWT认证体系
- ✅ 统一响应格式
- ✅ 详细的PHPDoc注释
- ✅ 支持Postman/Swagger导入

### 🚧 开发中 (v1.1.0)
- 🔄 API接口实现 (控制器、服务层、模型层)
- 🔄 数据库迁移文件编写
- 🔄 单元测试编写
- 🔄 API接口联调测试

### 📋 待开发 (v2.0.0)
- 📅 前端Vue.js界面开发
- 📅 AI智能解析功能
- 📅 实时通知系统
- 📅 Docker容器化部署
- 📅 性能优化与缓存策略

## 🚀 快速体验

### API接口测试

1. **导入API文档到Postman**
   - 下载文件：`backend/docs/api.yaml`
   - 在Postman中选择 Import → File → 选择api.yaml文件
   - 导入后可看到57个API接口，按11个模块分组

2. **在线API文档**
   - 访问 [Swagger Editor](https://editor.swagger.io/)
   - 将`api.yaml`内容粘贴到编辑器中
   - 即可查看完整的API文档和在线测试

3. **快速测试**
   ```bash
   # 获取系统配置
   curl https://vscode.qidian.cc/api/v1/system/config

   # 用户注册
   curl -X POST https://vscode.qidian.cc/api/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{"username":"test","email":"<EMAIL>","password":"123456"}'
   ```

## 📊 项目亮点

- 🎯 **完整的API设计**: 57个接口覆盖所有功能模块
- 📚 **标准化文档**: 符合OpenAPI 3.0.3规范
- 🏗️ **清晰的架构**: MVC+Service分层架构
- 🔐 **安全认证**: JWT Token认证机制
- 📝 **详细注释**: 完整的PHPDoc注释
- 🧪 **易于测试**: 支持Postman等工具导入测试

---

⭐ **如果这个项目对你有帮助，请给我们一个 Star！**

🔥 **项目特色**：这是一个完整的企业级书签管理系统，从需求分析到API设计，从数据库设计到接口文档，每一个环节都经过精心设计，是学习现代Web开发的绝佳案例！