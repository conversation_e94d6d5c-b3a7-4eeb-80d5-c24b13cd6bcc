import { http } from '@/utils/request'
import type { LoginForm, RegisterForm, LoginResponse, UserProfile } from '@/types/user'
import type { ApiResponse } from '@/types/api'

/**
 * 认证相关API
 */
export const authApi = {
  // 用户登录
  login(data: LoginForm): Promise<ApiResponse<LoginResponse>> {
    return http.post('/auth/login', {
      account: data.account,
      login_password: data.password
    })
  },

  // 用户注册
  register(data: RegisterForm): Promise<ApiResponse<LoginResponse>> {
    return http.post('/auth/register', data)
  },

  // 刷新Token
  refreshToken(refreshToken: string): Promise<ApiResponse<{ access_token: string; refresh_token: string; expires_in: number }>> {
    return http.post('/auth/refresh', {
      refresh_token: refreshToken
    })
  },

  // 用户登出
  logout(): Promise<ApiResponse<null>> {
    return http.post('/auth/logout')
  },

  // 获取用户信息
  getUserInfo(): Promise<ApiResponse<UserProfile>> {
    return http.get('/user/profile')
  },

  // 更新用户信息
  updateProfile(data: Partial<UserProfile>): Promise<ApiResponse<UserProfile>> {
    return http.put('/user/profile', data)
  },

  // 修改密码
  changePassword(data: { current_password: string; new_password: string; new_password_confirmation: string }): Promise<ApiResponse<null>> {
    return http.put('/user/password', data)
  },

  // 上传头像
  uploadAvatar(file: File): Promise<ApiResponse<{ avatar_url: string }>> {
    const formData = new FormData()
    formData.append('avatar', file)
    return http.upload('/user/avatar', formData)
  }
}
