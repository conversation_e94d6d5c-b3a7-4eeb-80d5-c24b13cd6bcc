# 🚀 前端开发执行指南

## 📋 当前任务状态

**当前阶段**: Phase 1 - 项目初始化与基础架构  
**当前任务**: 1.1 创建Vue 3项目  
**预计完成时间**: 20分钟

## 🎯 立即执行的任务

### ✅ Task 1.1: 创建Vue 3项目 (20分钟)

#### 🔧 执行步骤

1. **创建项目目录**
```bash
cd /path/to/your/workspace
mkdir bookmark-manager-frontend
cd bookmark-manager-frontend
```

2. **使用Vite创建Vue 3项目**
```bash
npm create vue@latest .
```

3. **选择配置选项**
```
✔ Add TypeScript? … Yes
✔ Add JSX Support? … No
✔ Add Vue Router for Single Page Application development? … Yes
✔ Add Pinia for state management? … Yes
✔ Add Vitest for Unit Testing? … Yes
✔ Add an End-to-End Testing Solution? › Playwright
✔ Add ESLint for code quality? … Yes
✔ Add Prettier for code formatting? … Yes
```

4. **安装依赖**
```bash
npm install
```

5. **验证项目创建**
```bash
npm run dev
```
访问 http://localhost:5173 确认项目正常运行

#### ✅ 验收标准
- [ ] 项目可以正常启动
- [ ] TypeScript支持正常
- [ ] Vue Router配置正确
- [ ] Pinia状态管理可用
- [ ] ESLint和Prettier配置正确

---

### ✅ Task 1.2: 安装核心依赖 (20分钟)

#### 🔧 执行步骤

1. **安装UI组件库**
```bash
npm install element-plus @element-plus/icons-vue
```

2. **安装CSS框架**
```bash
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
```

3. **安装HTTP客户端和工具库**
```bash
npm install axios @vueuse/core
npm install dayjs lodash-es
npm install @types/lodash-es -D
```

4. **安装开发工具**
```bash
npm install -D @types/node unplugin-auto-import unplugin-vue-components
```

#### ✅ 验收标准
- [ ] Element Plus可以正常导入
- [ ] Tailwind CSS配置正确
- [ ] Axios可以正常使用
- [ ] 类型定义完整

---

### ✅ Task 1.3: 配置开发工具 (20分钟)

#### 🔧 执行步骤

1. **配置Vite自动导入**
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ['vue', 'vue-router', 'pinia'],
      dts: true
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      dts: true
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'https://vscode.qidian.cc',
        changeOrigin: true
      }
    }
  }
})
```

2. **配置Tailwind CSS**
```javascript
// tailwind.config.js
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
```

3. **配置环境变量**
```bash
# .env.development
VITE_API_BASE_URL=https://vscode.qidian.cc/api/v1
VITE_APP_TITLE=书签管理系统
VITE_APP_VERSION=1.0.0

# .env.production
VITE_API_BASE_URL=https://your-domain.com/api/v1
VITE_APP_TITLE=书签管理系统
VITE_APP_VERSION=1.0.0
```

#### ✅ 验收标准
- [ ] Element Plus组件可以自动导入
- [ ] Tailwind CSS样式生效
- [ ] 环境变量可以正常读取
- [ ] 代理配置正常工作

---

### ✅ Task 1.4: 项目目录结构 (20分钟)

#### 🔧 执行步骤

1. **创建标准目录结构**
```bash
mkdir -p src/{components/{common,form,layout,business},views/{auth,bookmark,folder,tag,dashboard,search,stats,settings},stores,utils,api,types,assets/{images,styles},hooks,constants}
```

2. **创建基础文件**
```bash
# 创建基础组件文件
touch src/components/common/AppLoading.vue
touch src/components/common/AppMessage.vue
touch src/components/layout/AppHeader.vue
touch src/components/layout/AppSidebar.vue
touch src/components/layout/AppMain.vue

# 创建页面文件
touch src/views/auth/LoginView.vue
touch src/views/auth/RegisterView.vue
touch src/views/dashboard/DashboardView.vue

# 创建工具文件
touch src/utils/request.ts
touch src/utils/storage.ts
touch src/utils/format.ts
touch src/api/auth.ts
touch src/types/api.ts
touch src/types/user.ts
```

3. **创建样式文件**
```css
/* src/assets/styles/index.css */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Element Plus样式覆盖 */
.el-menu {
  border-right: none;
}
```

#### ✅ 验收标准
- [ ] 目录结构清晰合理
- [ ] 基础文件创建完成
- [ ] 样式文件配置正确

---

### ✅ Task 1.5: 基础配置文件 (20分钟)

#### 🔧 执行步骤

1. **配置路由**
```typescript
// src/router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/RegisterView.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/components/layout/AppLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/DashboardView.vue')
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
```

2. **配置状态管理**
```typescript
// src/stores/index.ts
import { createPinia } from 'pinia'

const pinia = createPinia()

export default pinia

// src/stores/auth.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { User } from '@/types/user'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string>('')
  const user = ref<User | null>(null)
  const isLoggedIn = computed(() => !!token.value)

  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  const clearAuth = () => {
    token.value = ''
    user.value = null
    localStorage.removeItem('token')
  }

  return {
    token,
    user,
    isLoggedIn,
    setToken,
    clearAuth
  }
})
```

3. **更新main.ts**
```typescript
// src/main.ts
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './stores'
import '@/assets/styles/index.css'

const app = createApp(App)

app.use(pinia)
app.use(router)

app.mount('#app')
```

#### ✅ 验收标准
- [ ] 路由配置正确，可以正常跳转
- [ ] 状态管理可以正常使用
- [ ] 样式文件正确加载

## 📊 进度跟踪

### Phase 1 任务完成情况
- [x] 1.1 创建Vue 3项目 ✅
- [ ] 1.2 安装核心依赖 🔄
- [ ] 1.3 配置开发工具 ⏳
- [ ] 1.4 项目目录结构 ⏳
- [ ] 1.5 基础配置文件 ⏳

### 下一步计划
完成Phase 1后，立即开始Phase 2: 核心基础设施开发

## 🚨 注意事项

1. **版本兼容性**: 确保所有依赖版本兼容
2. **代码规范**: 严格遵循ESLint和Prettier规则
3. **类型安全**: 充分利用TypeScript类型检查
4. **性能考虑**: 注意组件懒加载和代码分割
5. **测试覆盖**: 每个功能都要有对应的测试用例

## 🔗 相关文档

- [前端开发详细规划](frontend-development-plan.md)
- [API接口对接优先级](api-integration-priority.md)
- [Vue 3官方文档](https://vuejs.org/)
- [Element Plus文档](https://element-plus.org/)
- [Tailwind CSS文档](https://tailwindcss.com/)
