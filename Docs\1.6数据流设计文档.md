# Web端书签管理系统 - 数据流设计文档

## 1. 概述

本文档详细描述Web端书签管理系统中各类数据的流转过程，包括数据的创建、处理、存储、缓存和同步机制。通过清晰的数据流设计，确保系统的高性能、高可用和数据一致性。

## 2. 核心数据流程图

### 2.1 系统整体数据流

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   用户界面   │────▶│   API网关    │────▶│  业务逻辑层  │
│  (Vue 3)    │◀────│  (Nginx)    │◀────│ (ThinkPHP)  │
└─────────────┘     └─────────────┘     └──────┬──────┘
                                                │
                    ┌───────────────────────────┼───────────────┐
                    │                           │               │
              ┌─────▼─────┐           ┌─────────▼──────┐ ┌──────▼──────┐
              │   MySQL    │           │     Redis      │ │  消息队列    │
              │  (主存储)   │           │   (缓存层)     │ │ (异步任务)   │
              └───────────┘           └────────────────┘ └─────────────┘
```

### 2.2 数据分层架构

1. **表现层**: Vue 3 SPA，负责数据展示和用户交互
2. **接口层**: RESTful API，统一的数据交换格式
3. **业务层**: 业务逻辑处理，数据验证和转换
4. **数据访问层**: ORM映射，数据库操作抽象
5. **存储层**: MySQL持久化存储 + Redis缓存

## 3. 用户认证数据流

### 3.1 用户注册流程

```
用户输入 → 前端验证 → API请求 → 后端验证 → 密码加密 → 数据库存储 → 生成Token → 返回响应
```

**详细步骤**：
1. 用户填写注册表单（邮箱、用户名、密码）
2. 前端进行基础验证（格式、长度等）
3. 发送POST请求到 `/api/v1/auth/register`
4. 后端验证数据合法性：
   - 邮箱格式验证
   - 用户名唯一性检查
   - 密码强度验证
5. 使用bcrypt对密码进行加密（cost=12）
6. 将用户数据写入users表
7. 生成JWT Token（Access Token + Refresh Token）
8. 将Refresh Token存储到user_tokens表
9. 返回用户信息和Token

**数据转换**：
```php
// 原始数据
[
    'email' => '<EMAIL>',
    'username' => 'user123',
    'password' => 'plain_password'
]

// 存储数据
[
    'email' => '<EMAIL>',
    'username' => 'user123',
    'password' => '$2y$12$...' // bcrypt hash
]
```

### 3.2 用户登录流程

```
登录请求 → 账号验证 → 密码验证 → 生成Token → 缓存Session → 返回Token
```

**详细步骤**：
1. 用户输入账号（邮箱/用户名）和密码
2. 查询数据库验证账号存在性
3. 使用bcrypt验证密码
4. 生成新的JWT Token对
5. 更新用户最后登录时间和IP
6. 将用户信息缓存到Redis（TTL: 2小时）
7. 返回Token和用户信息

**Redis缓存结构**：
```json
// Key: user:session:{user_id}
{
    "id": 1,
    "email": "<EMAIL>",
    "username": "user123",
    "avatar": "/storage/avatars/1.jpg",
    "permissions": ["read", "write"],
    "login_at": "2024-01-20 10:00:00"
}
```

### 3.3 Token刷新流程

```
Refresh Token → 验证有效性 → 检查过期 → 生成新Access Token → 返回
```

**Token生命周期**：
- Access Token: 2小时
- Refresh Token: 30天
- 自动刷新: Access Token过期前15分钟

## 4. 书签数据流

### 4.1 创建书签流程

```
URL输入 → 前端预处理 → API提交 → 数据验证 → 网页信息抓取 → 数据存储 → 缓存更新 → 返回结果
```

**详细步骤**：
1. 用户输入或粘贴URL
2. 前端进行URL格式验证
3. 提交到 `/api/v1/bookmarks`
4. 后端验证：
   - URL格式合法性
   - 用户权限验证
   - 重复URL检查
5. 异步抓取网页信息：
   - 网页标题（`<title>`标签）
   - 描述（`<meta name="description">`）
   - Favicon图标
6. 数据存储：
   - 写入bookmarks表
   - 更新文件夹书签计数（如有）
   - 创建标签关联（如有）
7. 缓存处理：
   - 清除用户书签列表缓存
   - 更新统计数据缓存
8. 返回创建成功的书签数据

**数据处理流程**：
```javascript
// 前端提交数据
{
    url: "https://example.com/article",
    title: "自定义标题",  // 可选
    folder_id: 5,
    tag_ids: [1, 2, 3]
}

// 后端处理后存储
{
    id: 156,
    user_id: 1,
    url: "https://example.com/article",
    title: "文章标题 - Example",  // 自动抓取或用户自定义
    description: "这是一篇关于...",  // 自动抓取
    favicon: "https://example.com/favicon.ico",
    folder_id: 5,
    sort_order: 0,
    is_star: 0,
    visit_count: 0,
    created_at: "2024-01-20 10:30:00"
}
```

### 4.2 批量操作数据流

```
选择书签 → 批量操作请求 → 权限验证 → 事务处理 → 批量更新 → 缓存同步
```

**批量移动示例**：
1. 前端收集选中的书签ID数组
2. 发送批量操作请求
3. 后端开启数据库事务
4. 验证每个书签的所有权
5. 批量更新folder_id
6. 更新相关文件夹的统计
7. 提交事务
8. 批量清除相关缓存

**事务处理伪代码**：
```php
DB::transaction(function () {
    // 验证所有书签属于当前用户
    $bookmarks = Bookmark::whereIn('id', $ids)
        ->where('user_id', $userId)
        ->get();
    
    // 批量更新
    Bookmark::whereIn('id', $ids)->update([
        'folder_id' => $targetFolderId
    ]);
    
    // 更新文件夹统计
    Folder::find($targetFolderId)->increment('bookmark_count', count($ids));
});
```

### 4.3 书签访问统计流程

```
点击书签 → 记录访问 → 异步更新统计 → 跳转目标URL
```

**数据更新**：
1. 前端发送访问记录请求
2. 后端异步更新：
   - visit_count + 1
   - last_visit_at = now()
3. 更新Redis中的热门书签排行
4. 返回新的访问统计数据

## 5. AI解析数据流

### 5.1 AI解析任务流程

```
触发解析 → 创建任务 → 加入队列 → 异步处理 → 调用AI → 存储结果 → 通知完成
```

**详细流程**：
1. 用户点击"AI解析"按钮
2. 创建解析任务记录
3. 将任务加入消息队列
4. 队列消费者处理：
   - 获取网页内容
   - 清理HTML标签
   - 调用AI接口（通义千问）
   - 解析返回结果
5. 存储解析结果：
   - 更新ai_parse_results表
   - 提取的关键词自动创建标签
6. 更新书签的解析状态
7. 清除相关缓存

**AI接口调用示例**：
```php
// 请求AI接口
$response = $aiClient->analyze([
    'content' => $webContent,
    'tasks' => [
        'summary' => ['max_length' => 300],
        'keywords' => ['max_count' => 10],
        'category' => ['types' => ['article', 'video', 'code', 'news']]
    ]
]);

// 处理返回结果
[
    'summary' => '这篇文章介绍了Vue 3的新特性...',
    'keywords' => ['Vue3', 'Composition API', '性能优化'],
    'content_type' => 'article'
]
```

### 5.2 AI解析状态机

```
待解析(0) → 解析中(1) → 成功(2)
                  ↓
               失败(3) → 重试 → 解析中(1)
```

**重试机制**：
- 最大重试次数：3次
- 重试间隔：5分钟、15分钟、30分钟
- 失败原因记录：网络超时、AI服务异常、内容过长等

## 6. 搜索与筛选数据流

### 6.1 全文搜索流程

```
搜索输入 → 分词处理 → 构建查询 → 执行搜索 → 结果排序 → 高亮处理 → 返回结果
```

**搜索优化策略**：
1. **搜索范围**：
   - 标题（权重：10）
   - URL（权重：5）
   - 描述（权重：8）
   - 标签（权重：7）
   - AI摘要（权重：6）

2. **分词处理**：
   ```php
   // 支持中文分词
   $keywords = jieba_cut($query);
   
   // 支持拼音搜索
   $pinyin = pinyin_convert($query);
   ```

3. **搜索缓存**：
   - 热门搜索词缓存结果
   - 缓存时间：10分钟
   - 缓存key: `search:{user_id}:{query_hash}`

### 6.2 智能文件夹数据流

```
定义条件 → 保存配置 → 动态查询 → 实时过滤 → 返回结果
```

**条件解析示例**：
```json
{
    "conditions": [
        {
            "field": "created_at",
            "operator": "between",
            "value": ["2024-01-01", "2024-01-31"]
        },
        {
            "field": "tags",
            "operator": "includes",
            "value": [1, 5, 8]
        }
    ],
    "logic": "AND"
}
```

**动态SQL生成**：
```sql
SELECT * FROM bookmarks 
WHERE user_id = ? 
  AND created_at BETWEEN ? AND ?
  AND id IN (
    SELECT bookmark_id FROM bookmark_tags 
    WHERE tag_id IN (1, 5, 8)
  )
```

## 7. 导入导出数据流

### 7.1 批量导入流程

```
文件上传 → 格式解析 → 数据验证 → 去重处理 → 批量插入 → 结果统计
```

**导入处理步骤**：
1. **文件解析**：
   - 支持浏览器HTML格式
   - 解析DOM结构提取书签
   - 保持原有文件夹层级

2. **数据清洗**：
   ```php
   // 去重逻辑
   $existingUrls = Bookmark::where('user_id', $userId)
       ->pluck('url')->toArray();
   
   $newBookmarks = array_filter($bookmarks, function($item) use ($existingUrls) {
       return !in_array($item['url'], $existingUrls);
   });
   ```

3. **批量插入优化**：
   - 每批次处理100条
   - 使用insert批量插入
   - 显示实时进度

### 7.2 导出数据流

```
选择范围 → 查询数据 → 格式转换 → 生成文件 → 提供下载
```

**导出格式处理**：
1. **HTML格式**：
   - 符合浏览器书签标准
   - 保持文件夹结构
   - 包含创建时间

2. **JSON格式**：
   ```json
   {
       "version": "1.0",
       "exported_at": "2024-01-20",
       "bookmarks": [...],
       "folders": [...],
       "tags": [...]
   }
   ```

3. **CSV格式**：
   - 扁平化数据结构
   - 包含所有字段
   - Excel友好格式

## 8. 缓存策略与数据同步

### 8.1 缓存分层设计

1. **浏览器缓存**：
   - localStorage: 用户偏好设置
   - sessionStorage: 临时数据
   - HTTP缓存: 静态资源

2. **应用缓存（Redis）**：
   ```
   用户会话: user:session:{id} (TTL: 2h)
   书签列表: bookmarks:list:{user_id}:{folder_id} (TTL: 10m)
   标签云: tags:cloud:{user_id} (TTL: 1h)
   统计数据: stats:{user_id}:{type} (TTL: 1d)
   搜索结果: search:{user_id}:{query_hash} (TTL: 10m)
   ```

3. **数据库查询缓存**：
   - MySQL查询缓存
   - ORM模型缓存

### 8.2 缓存更新策略

**主动更新**：
- 创建/更新/删除操作后立即清除相关缓存
- 使用缓存标签批量清除

**被动更新**：
- TTL过期自动失效
- 内存不足时LRU淘汰

**缓存预热**：
- 用户登录时预加载常用数据
- 定时任务更新热门数据缓存

### 8.3 数据一致性保证

1. **事务处理**：
   ```php
   DB::transaction(function () {
       // 数据库操作
       $bookmark = Bookmark::create($data);
       
       // 清除缓存
       Cache::tags(['bookmarks', "user:{$userId}"])->flush();
   });
   ```

2. **延迟双删**：
   - 删除缓存
   - 更新数据库
   - 延迟500ms再次删除缓存

3. **版本控制**：
   - 使用updated_at字段进行乐观锁控制
   - 防止并发更新冲突

## 9. 异步任务数据流

### 9.1 队列任务类型

1. **AI解析任务**：
   - 优先级：普通
   - 超时时间：60秒
   - 重试次数：3次

2. **死链检测任务**：
   - 优先级：低
   - 超时时间：30秒
   - 批量处理：每批50个

3. **数据导入任务**：
   - 优先级：高
   - 超时时间：300秒
   - 进度跟踪：实时更新

4. **报告生成任务**：
   - 优先级：低
   - 执行时间：凌晨2点
   - 结果存储：7天

### 9.2 任务执行流程

```
任务创建 → Redis队列 → Supervisor监听 → Worker处理 → 结果记录 → 通知用户
```

**Supervisor配置**：
```ini
[program:bookmark-queue]
process_name=%(program_name)s_%(process_num)02d
command=php think queue:work --sleep=3 --tries=3
numprocs=4
autostart=true
autorestart=true
```

### 9.3 任务监控

1. **任务状态跟踪**：
   ```json
   {
       "job_id": "import_123456",
       "type": "import",
       "status": "processing",
       "progress": 45,
       "total": 245,
       "created_at": "2024-01-20 10:00:00",
       "started_at": "2024-01-20 10:00:05",
       "result": null
   }
   ```

2. **失败处理**：
   - 记录失败原因
   - 发送告警通知
   - 提供手动重试

## 10. 数据安全流程

### 10.1 数据加密

1. **传输加密**：
   - 全站HTTPS
   - API请求签名验证

2. **存储加密**：
   - 密码：bcrypt加密
   - 敏感配置：AES-256加密
   - Token：JWT签名

### 10.2 数据备份流程

```
定时触发 → 数据导出 → 压缩加密 → 上传备份 → 验证完整性 → 清理旧备份
```

**备份策略**：
- 全量备份：每周日凌晨2点
- 增量备份：每天凌晨3点
- 保留周期：30天
- 异地备份：阿里云OSS

### 10.3 数据恢复流程

1. 选择备份文件
2. 验证文件完整性
3. 解密解压
4. 恢复到临时库
5. 数据校验
6. 切换到生产库

## 11. 性能优化策略

### 11.1 查询优化

1. **索引优化**：
   - 基于查询模式创建复合索引
   - 定期分析慢查询日志
   - 使用EXPLAIN优化SQL

2. **分页优化**：
   ```sql
   -- 使用游标分页代替OFFSET
   SELECT * FROM bookmarks 
   WHERE user_id = ? AND id > ? 
   ORDER BY id 
   LIMIT 20
   ```

3. **N+1查询优化**：
   ```php
   // 预加载关联数据
   $bookmarks = Bookmark::with(['tags', 'folder'])
       ->where('user_id', $userId)
       ->paginate(20);
   ```

### 11.2 并发优化

1. **数据库连接池**：
   - 最小连接数：10
   - 最大连接数：100
   - 连接超时：30秒

2. **Redis连接池**：
   - 最大连接数：50
   - 连接超时：5秒

3. **限流控制**：
   - 令牌桶算法
   - 用户级别限流
   - API级别限流

## 12. 监控与告警

### 12.1 数据流监控指标

1. **性能指标**：
   - API响应时间
   - 数据库查询时间
   - 缓存命中率
   - 队列处理速度

2. **业务指标**：
   - 每秒请求数(QPS)
   - 并发用户数
   - 数据增长率
   - 错误率

### 12.2 告警规则

1. **性能告警**：
   - API响应时间 > 1秒
   - 数据库慢查询 > 200ms
   - Redis内存使用率 > 80%
   - 队列积压 > 1000

2. **业务告警**：
   - 错误率 > 1%
   - 登录失败率 > 10%
   - AI解析失败率 > 20%

## 13. 未来优化方向

1. **实时同步**：
   - WebSocket推送
   - 多端数据同步
   - 协同编辑支持

2. **大数据处理**：
   - 分库分表
   - 读写分离
   - ElasticSearch全文搜索

3. **智能推荐**：
   - 基于用户行为的推荐
   - 相似内容推荐
   - 标签自动推荐 