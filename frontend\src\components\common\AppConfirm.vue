<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="400px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="confirm-content">
      <div class="confirm-icon">
        <el-icon :size="48" :color="iconColor">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      <div class="confirm-message">
        <div class="confirm-title">{{ message }}</div>
        <div class="confirm-description" v-if="description">{{ description }}</div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">
          {{ cancelText }}
        </el-button>
        <el-button 
          :type="confirmType" 
          @click="handleConfirm"
          :loading="loading"
        >
          {{ confirmText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Warning, QuestionFilled, InfoFilled, SuccessFilled } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  type?: 'warning' | 'info' | 'success' | 'error'
  title?: string
  message: string
  description?: string
  confirmText?: string
  cancelText?: string
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'warning',
  title: '确认操作',
  confirmText: '确定',
  cancelText: '取消',
  loading: false
})

const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const iconMap = {
  warning: Warning,
  info: InfoFilled,
  success: SuccessFilled,
  error: Warning
}

const colorMap = {
  warning: '#e6a23c',
  info: '#409eff',
  success: '#67c23a',
  error: '#f56c6c'
}

const typeMap = {
  warning: 'warning',
  info: 'primary',
  success: 'success',
  error: 'danger'
}

const iconComponent = computed(() => iconMap[props.type])
const iconColor = computed(() => colorMap[props.type])
const confirmType = computed(() => typeMap[props.type])

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  visible.value = false
}

const handleClose = () => {
  if (!props.loading) {
    handleCancel()
  }
}
</script>

<style scoped>
.confirm-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px 0;
}

.confirm-icon {
  flex-shrink: 0;
}

.confirm-message {
  flex: 1;
}

.confirm-title {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
  margin-bottom: 8px;
  line-height: 1.4;
}

.confirm-description {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
