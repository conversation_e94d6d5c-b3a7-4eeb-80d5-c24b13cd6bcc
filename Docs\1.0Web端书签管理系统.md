- - - - # Web端书签管理系统 - **产品需求文档 (PRD) **

        ## 1. 项目愿景与目标

        ### 1.1. 项目愿景

        打造一款极致高效、智能的个人知识与信息导航中心，让用户从繁杂的书签中解放出来，专注于信息的消化与创造。

        ### 1.2. 核心目标

        - **效率提升**: 相比传统浏览器书签，在整理、查找、使用书签的效率上提升至少50%。
        - **智能驱动**: 利用AI技术，辅助完成书签的摘要、归类和标签化，降低用户管理负担。
        - **数据资产化**: 将用户的书签从简单的链接集合，转变为可分析、可洞察的个人知识资产。
        - **跨平台一致性**: 提供一致的Web端体验，为未来扩展（如浏览器插件、移动端App）奠定基础。

        ## 2. 功能需求详述 (Functional Requirements)

        ### 2.1. 书签与文件夹核心管理 (CRUD & Organization)

        - **无限层级分组与拖拽**: 支持通过拖拽操作自由移动书签和文件夹，实现任意层级的嵌套。
          - **交互反馈**: 拖拽操作需提供清晰的实时视觉反馈。拖动单个项目时，鼠标指针旁应有该项的半透明缩略图；拖动多个项目时，除缩略图外还需附带一个**数字角标**，实时显示被拖动项的总数。
        - **批量操作**:
          - **操作对象**: 支持对文件夹和书签进行批量的移动、删除、添加/移除标签等。
          - **操作入口**: 为满足不同用户习惯，批量操作入口需**同时支持**：1) 选中项目后，在页面顶部或底部出现的**动态工具栏**；2) 在选中项目上点击鼠标右键弹出的**上下文菜单**。
        - **自定义外观**: 支持为文件夹设置预设图标、Emoji表情和自定义颜色标签，便于视觉区分。
        - **视图模式**: 提供至少三种视图模式，并允许用户持久化选择：
          - **列表视图**: 简洁高效，信息密度高。
          - **网格视图**: 视觉化展示，适合包含网页快照的场景。
          - **列状视图 (类访达)**: 清晰展示层级关系，便于在多层级文件夹间快速导航。
        - **排序**: 支持按标题（拼音/字典序）、添加时间、最近访问时间、访问频率等多种方式进行升序或降序排列。
        - **快速路径导航 (面包屑)**: 在界面顶部清晰展示当前所在文件夹的层级路径，并支持点击任一父级快速跳转。
        - **回收站**: 所有删除的书签和文件夹将首先进入回收站。回收站提供恢复和永久删除两个选项，防止用户误操作。
      
        ### 2.2. 标签系统 (Tagging System)
      
        - **基础**: 书签和标签之间为多对多关系，一个书签可以关联多个标签，一个标签也可以关联多个书签。
        - **智能化**:
          - **标签自动建议**: 用户在为书签打标签时，系统根据网页内容、标题以及用户已有的标签库，利用AI进行分析并推荐相关标签。
          - **智能标签 (自动化规则)**: 用户可以创建自定义规则，例如“所有URL包含`github.com`的书签自动添加`开发`标签”，系统将自动为符合条件的书签打标。
        - **可视化**: 提供标签云视图，通过标签大小或颜色深浅直观展示不同标签下的书签数量或访问热度，帮助用户洞察收藏热点。
      
        ### 2.3. 搜索与筛选 (Search & Filter)
      
        - **体验优化**:
          - **即时搜索**: 输入关键词时实时显示搜索结果。
          - **智能建议**: 根据用户输入和历史记录提供搜索建议。
          - **容错处理**: 支持拼音、首字母和一定的错别字模糊搜索。
        - **核心能力**: 提供全文检索功能，搜索范围覆盖书签的标题、URL、标签、AI生成的摘要以及网页正文（如果已缓存）。并支持高级搜索语法，如：
          - `"关键词"` (精确匹配)
          - `-关键词` (排除)
          - `tag:标签名` (按标签搜索)
        - **结果呈现**: 在搜索结果列表中，匹配的关键词需要**高亮**显示，方便用户快速定位。
        - **高级筛选**: 提供多维度组合筛选面板，支持按添加时间范围、最近访问状态（已访问/未访问）、链接状态（正常/死链）、是否重复链接等条件进行组合筛选。
        - **智能文件夹**: 用户可以将常用的搜索或筛选条件保存为一个“智能文件夹”。点击该文件夹时，会自动执行预设的搜索和筛选，动态展示符合条件的结果。
      
        ### 2.4. AI 智能解析 (AI-Powered Analysis)
      
        - **触发机制**:
          - **批量导入时**: **自动**在后台触发AI解析任务队列。
          - **单条添加时**: 用户在添加或编辑书签时，**手动点击** [AI解析] 按钮触发，避免不必要的资源消耗。
        - **核心功能**:
          - 自动生成网页核心内容的**摘要**。
          - 自动提取**关键词**作为推荐标签。
          - 自动识别网页类型（如：文章、视频、代码库、新闻等）。
        - **基础功能**: 无论是否进行AI解析，都应自动抓取并保存网页的标题、描述（Description）和网站图标（Favicon）。
      
        ### 2.5. 导入与导出 (Data Portability)
      
        - **兼容性**: 支持从所有主流浏览器（Chrome, Firefox, Safari, Edge）导出的标准HTML书签文件进行导入。同时，支持以通用的 JSON 或 CSV 格式进行数据的导入和导出。
        - **导入策略**: 导入时若遇到已存在的书签（URL相同），应提供冲突处理策略选项，如：跳过、覆盖、保留两者。
      
        ### 2.6. 统计与洞察 (Analytics & Insights)
      
        - **仪表盘 (Dashboard)**: 提供一个可视化的仪表盘，展示核心指标图表，例如：
          - 书签总数、本周新增数。
          - 标签使用频率TOP 10。
          - 访问最频繁的网站TOP 10。
          - 书签分类占比（需AI识别网页类型）。
        - **报告**: 支持生成周报/月报，总结用户的收藏和浏览习惯，并可导出为图片或PDF格式，便于分享和回顾。
      
        ### 2.7. 用户系统 (User & Permission)
      
        - **基础功能**: 支持通过邮箱进行用户注册、登录、密码找回等标准账户操作。
        - **数据隔离**: 严格保证每个用户的数据物理或逻辑上完全隔离，确保数据隐私与安全。
      
        ## 3. 非功能性需求 (Non-Functional Requirements)
      
        - **性能**: 核心操作（如打开、搜索、移动）响应时间应在毫秒级。在处理超过10,000条书签数据时，系统仍能保持流畅。
        - **安全**: 全站强制使用HTTPS。后端需有效防范常见的Web攻击，如XSS, CSRF, SQL注入。所有需要授权的API接口必须经过严格的身份验证和权限检查。
        - **可扩展性**: **采用 ThinkPHP 8.0 内置的 `think-queue` 组件**，通过消息队列异步处理AI解析、报告生成等耗时操作，避免阻塞主流程，提升系统吞吐量。
        - **可靠性**: 数据库应配置每日自动备份策略，并具备可验证的数据恢复方案。
        - **兼容性**: 兼容主流现代浏览器（Chrome, Firefox, Safari, Edge）的最新两个大版本。
      
        ## 4.技术与API规范 (Tech Stack & API Specification)
      
        ### 4.1. 最终技术栈
      
        - **前端**: **Vue 3** (建议搭配 Vite 构建工具)
        - **后端**: **PHP (ThinkPHP 8.0 框架)**
        - **数据库**: **MySQL 8.0+**
        - **缓存与消息队列**: **Redis** (用于缓存、Session、以及 `think-queue` 的驱动)
      
        ### 4.2. API设计规范 (遵从 RESTful 最佳实践)
      
        > **设计理念**: 摒弃传统的 RPC 风格 (`/控制器/方法`)，全面转向更现代化、语义化、更利于前后端协作的 **RESTful** 风格。这能使 API 结构更清晰，并能充分利用 HTTP 协议本身的能力。
      
        1. **API风格**: **RESTful (Representational State Transfer)**
      
        2. **路由规则 (Routing)**:
      
           - **版本管理**: 在URL中加入版本号前缀，便于未来迭代，如 `/api/v1/`。
           - **资源化路由**: 使用 ThinkPHP 8 的资源路由 (`Route::resource()`) 或手动定义 RESTful 路由规则，以HTTP动词区分对资源的操作。
           - **示例**: | **操作** | **HTTP动词** | **URL路径** | **说明** | | :--- | :--- | :--- | :--- | | 获取书签列表 | `GET` | `/api/v1/bookmarks` | 支持分页、筛选、排序 | | 创建新书签 | `POST` | `/api/v1/bookmarks` | | | 获取单个书签详情 | `GET` | `/api/v1/bookmarks/{id}` | | | 更新书签 | `PUT` / `PATCH` | `/api/v1/bookmarks/{id}` | `PUT`全量更新,`PATCH`部分更新 | | 删除书签 | `DELETE` | `/api/v1/bookmarks/{id}` | | | 用户登录 | `POST` | `/api/v1/auth/login` | 创建一个新的会话(Token) | | 用户登出 | `POST` | `/api/v1/auth/logout` | 销毁当前会话 | | 获取当前用户信息 | `GET` | `/api/v1/user/profile` | | | 获取所有标签 | `GET` | `/api/v1/tags` | | | 创建标签 | `POST` | `/api/v1/tags` | | | 删除标签 | `DELETE` | `/api/v1/tags/{id}` | |
      
        3. **数据格式**: 所有请求体与响应体数据均使用 **JSON** 格式 (`Content-Type: application/json`)。
      
        4. **认证方式**: **JWT (JSON Web Token)**
      
           - **流程**: 用户使用凭据（邮箱/密码）调用登录接口成功后，服务器返回 `access_token` 和 `expires_in` (有效期)。
           - **实现**: 前端在后续所有需要授权的请求的 `Authorization` Header 中携带 Token：`Authorization: Bearer <token>`。
           - **权限控制**: 在 ThinkPHP 8 中，通过创建**全局或路由中间件**来验证 JWT 的有效性。需要登录的接口应用此中间件，无需登录的接口（如登录、注册）则不应用。
      
        5. **统一响应结构**:
      
           - **目的**: 保持API响应结构的一致性，便于前端进行统一的请求封装和错误处理。
      
           - **实现**: 建议在 `app/common` 目录下封装一个响应处理的 Trait 或在基类控制器 `BaseController` 中，提供 `success()` 和 `error()` 方法。
      
           - **成功响应**: HTTP状态码 `200 OK`, `201 Created`, `204 No Content`
      
             ```
             // 适用于 GET, POST, PUT/PATCH 等
             {
               "code": 0, // 约定 0 为成功
               "message": "操作成功",
               "data": { ... } // GET/POST/PUT 时返回的数据，或为 null
             }
             ```
      
           - **失败响应**: HTTP状态码 `400 Bad Request`, `401 Unauthorized`, `403 Forbidden`, `404 Not Found`, `422 Unprocessable Entity`
      
             ```
             // 适用于各类业务或系统错误
             {
               "code": 40001, // 自定义业务错误码，便于前端精确处理，甚至实现国际化提示
               "message": "请求参数验证失败",
               "errors": { // (可选) 提供详细的字段错误信息，便于前端展示
                   "title": "书签标题不能为空",
                   "url": "必须是合法的URL格式"
               },
               "data": null
             }
             ```
      
        6. **HTTP状态码**:
      
           - **核心**: 严格遵循HTTP标准，使用不同的状态码来反映请求的真实结果，而非总是返回`200`。
           - **常用规范**:
             - `200 OK`: 请求成功，常用于 `GET`, `PUT`, `PATCH`。
             - `201 Created`: 资源创建成功，常用于 `POST`。
             - `204 No Content`: 操作成功，但响应体为空，常用于 `DELETE`。
             - `400 Bad Request`: 客户端请求语法错误（如参数无效、格式错误）。
             - `401 Unauthorized`: 未认证或认证已过期，需要重新登录。
             - `403 Forbidden`: 已认证，但无权访问该资源。
             - `404 Not Found`: 请求的资源不存在。
             - `422 Unprocessable Entity`: 请求格式正确，但由于业务逻辑错误导致无法处理（如表单验证失败）。
             - `500 Internal Server Error`: 服务器内部发生未知错误。
      
        ## 5. 版本规划与实施路线图 (Roadmap)
      
        ### **v1.0 - MVP (最小可行产品)**
      
        - **核心目标**: 快速验证用户对“高效整理和快速查找”的核心需求。
        - **功能范围**:
          - 用户系统（注册、登录）
          - 书签/文件夹的CRUD（增删改查）
          - 无限层级与拖拽
          - 基础标签系统（添加、移除、按标签筛选）
          - 基础搜索（仅标题、URL）
          - 标准HTML导入/导出
      
        ### **v1.5 - 智能与可视化增强版**
      
        - **核心目标**: 引入AI和数据可视化，降低用户管理成本，提升产品价值。
        - **功能范围**:
          - AI智能解析（手动触发，生成摘要和关键词）
          - 死链检测功能
          - 标签云
          - 基础统计仪表盘
          - 批量操作
      
        ### **v2.0 - 高级与生态拓展**
      
        - **核心目标**: 提升高级用户体验，并为未来的协作和跨平台打下基础。
        - **功能范围**:
          - 全文检索与高级搜索语法
          - 智能文件夹
          - 智能标签（自动化规则）
          - 第三方账号登录（如GitHub, Google）
          - **[探索]** 文件夹共享与简单协作功能
      
        ## 6. 潜在风险与应对策略
      
        - **AI成本风险**:
          - **风险**: AI API调用可能产生高昂费用。
          - **应对**: MVP阶段采用手动触发机制；为每个用户设置每月免费解析额度，超出部分可引导用户购买增值包或暂时停用。
        - **数据处理性能风险**:
          - **风险**: 书签数量巨大时，查询和渲染可能变慢。
          - **应对**: 后端对常用查询字段（如`user_id`, `folder_id`, `url`）建立数据库索引；前端采用虚拟列表（Virtual List/Scrolling）技术来高效渲染长列表。
        - **异步任务可靠性**:
          - **风险**: 消息队列中的任务（如AI解析）可能失败。
          - **应对**: 使用 `think-queue` 的失败任务重试机制；建立任务监控和日志系统，对于多次失败的任务进行告警，以便人工介入。