<?php

namespace app\middleware;

use app\common\JwtAuth;
use app\common\Response;
use app\common\ErrorCode;
use think\Request;
use Closure;

/**
 * JWT认证中间件
 */
class Auth
{
    /**
     * 处理请求
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 获取Authorization头部
        $authorization = $request->header('Authorization');
        
        if (empty($authorization)) {
            return Response::unauthorized('缺少认证令牌');
        }

        // 提取Token
        $token = JwtAuth::extractTokenFromHeader($authorization);
        if (empty($token)) {
            return Response::unauthorized('认证令牌格式错误');
        }

        // 验证Token
        $payload = JwtAuth::validateAccessToken($token);
        if (!$payload) {
            return Response::error('认证令牌无效或已过期', ErrorCode::TOKEN_INVALID, null, 401);
        }

        // 检查用户ID
        if (!isset($payload['user_id']) || !is_numeric($payload['user_id'])) {
            return Response::unauthorized('认证令牌数据异常');
        }

        // 将用户信息存储到请求中，供控制器使用
        $request->userId = (int)$payload['user_id'];
        $request->tokenPayload = $payload;

        // 检查Token是否即将过期，添加提示头部
        if (JwtAuth::isTokenExpiringSoon($payload)) {
            $response = $next($request);
            if ($response instanceof \think\Response) {
                $response->header('X-Token-Expiring', 'true');
                $response->header('X-Token-Remaining', JwtAuth::getTokenRemainingTime($payload));
            }
            return $response;
        }

        return $next($request);
    }
}
