<?php

namespace app\validate;

use think\Validate;

class TagValidate extends Validate
{
    protected $rule = [
        'name' => 'require|max:50',
        'color' => 'regex:/^#[0-9A-Fa-f]{6}$/',
        'source_tag_ids' => 'require|array',
        'target_tag_id' => 'require|integer',
    ];

    protected $message = [
        'name.require' => '标签名称不能为空',
        'name.max' => '标签名称不能超过50个字符',
        'color.regex' => '颜色格式不正确，请使用#开头的6位16进制颜色代码',
        'source_tag_ids.require' => '源标签ID不能为空',
        'source_tag_ids.array' => '源标签ID必须是数组',
        'target_tag_id.require' => '目标标签ID不能为空',
        'target_tag_id.integer' => '目标标签ID必须是整数',
    ];

    protected $scene = [
        'create' => ['name', 'color'],
        'update' => ['name', 'color'],
        'merge' => ['source_tag_ids', 'target_tag_id'],
    ];
}
