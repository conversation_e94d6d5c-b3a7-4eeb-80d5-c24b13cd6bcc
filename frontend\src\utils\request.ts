import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { ApiResponse } from '@/types/api'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加认证token
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response



    // 检查业务状态码 - 支持多种成功状态码
    if (data.code === 200 || data.code === 0 || data.code === 201) {
      return data
    }

    // 处理业务错误
    const errorMessage = data.message || '请求失败'
    ElMessage.error(errorMessage)
    throw new Error(errorMessage)
  },
  async (error) => {
    const { response } = error

    if (response) {
      const { status, data } = response

      // 对于400状态码，如果响应包含业务数据，则当作业务错误处理
      if (status === 400 && data && typeof data === 'object' && 'code' in data) {
        // 这是业务错误，不显示错误消息，让业务代码自己处理
        return Promise.reject(error)
      }

      switch (status) {
        case 401:
          // Token过期或无效
          const authStore = useAuthStore()
          
          // 尝试刷新token
          const refreshSuccess = await authStore.refreshAccessToken()
          if (refreshSuccess) {
            // 重新发送原请求
            return request(error.config)
          } else {
            // 刷新失败，清除认证信息并跳转登录
            authStore.clearAuth()
            window.location.href = '/login'
            ElMessage.error('登录已过期，请重新登录')
          }
          break

        case 403:
          ElMessage.error('没有权限访问该资源')
          break

        case 404:
          ElMessage.error('请求的资源不存在')
          break

        case 422:
          // 表单验证错误
          if (data.errors) {
            const firstError = Object.values(data.errors)[0] as string[]
            ElMessage.error(firstError[0] || '参数验证失败')
          } else {
            ElMessage.error(data.message || '参数验证失败')
          }
          break

        case 429:
          ElMessage.error('请求过于频繁，请稍后再试')
          break

        case 500:
          ElMessage.error('服务器内部错误')
          break

        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请检查网络连接')
    } else if (error.message === 'Network Error') {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error('请求失败，请稍后重试')
    }

    return Promise.reject(error)
  }
)

// 封装常用请求方法
export const http = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return request.get(url, config)
  },

  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return request.post(url, data, config)
  },

  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return request.put(url, data, config)
  },

  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return request.patch(url, data, config)
  },

  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return request.delete(url, config)
  },

  // 上传文件
  upload<T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return request.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers
      }
    })
  },

  // 下载文件
  download(url: string, config?: AxiosRequestConfig): Promise<Blob> {
    return request.get(url, {
      ...config,
      responseType: 'blob'
    })
  }
}

export default request
