<?php

namespace app\common;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;

/**
 * JWT认证工具类
 */
class JwtAuth
{
    /**
     * 加密算法
     */
    const ALGORITHM = 'HS256';

    /**
     * 获取JWT密钥
     * @return string
     */
    private static function getSecret(): string
    {
        return env('JWT_SECRET', 'your_jwt_secret_key_here_change_in_production');
    }

    /**
     * 获取Access Token过期时间（秒）
     * @return int
     */
    private static function getAccessTtl(): int
    {
        return (int)env('JWT_TTL', 2592000); // 默认2小时
    }

    /**
     * 获取Refresh Token过期时间（秒）
     * @return int
     */
    private static function getRefreshTtl(): int
    {
        return (int)env('JWT_REFRESH_TTL', 604800); // 默认7天
    }

    /**
     * 生成Access Token
     * @param int $userId 用户ID
     * @param array $payload 额外载荷
     * @return string
     */
    public static function generateAccessToken(int $userId, array $payload = []): string
    {
        $now = time();
        $exp = $now + self::getAccessTtl();

        $data = array_merge([
            'iss' => 'bookmark-system',     // 签发者
            'aud' => 'bookmark-api',        // 接收者
            'iat' => $now,                  // 签发时间
            'exp' => $exp,                  // 过期时间
            'user_id' => $userId,           // 用户ID
            'type' => 'access'              // 令牌类型
        ], $payload);

        return JWT::encode($data, self::getSecret(), self::ALGORITHM);
    }

    /**
     * 生成Refresh Token
     * @param int $userId 用户ID
     * @param string $deviceInfo 设备信息
     * @return string
     */
    public static function generateRefreshToken(int $userId, string $deviceInfo = ''): string
    {
        $now = time();
        $exp = $now + self::getRefreshTtl();

        $data = [
            'iss' => 'bookmark-system',
            'aud' => 'bookmark-api',
            'iat' => $now,
            'exp' => $exp,
            'user_id' => $userId,
            'type' => 'refresh',
            'device' => $deviceInfo,
            'jti' => uniqid('', true)       // JWT ID，用于唯一标识
        ];

        return JWT::encode($data, self::getSecret(), self::ALGORITHM);
    }

    /**
     * 验证并解析Token
     * @param string $token JWT令牌
     * @return array|false 成功返回载荷数组，失败返回false
     */
    public static function parseToken(string $token)
    {
        try {
            $decoded = JWT::decode($token, new Key(self::getSecret(), self::ALGORITHM));
            return (array)$decoded;
        } catch (ExpiredException $e) {
            // Token已过期
            return false;
        } catch (SignatureInvalidException $e) {
            // 签名无效
            return false;
        } catch (BeforeValidException $e) {
            // Token还未生效
            return false;
        } catch (\Exception $e) {
            // 其他错误
            return false;
        }
    }

    /**
     * 验证Access Token
     * @param string $token
     * @return array|false
     */
    public static function validateAccessToken(string $token)
    {
        $payload = self::parseToken($token);
        
        if (!$payload || !isset($payload['type']) || $payload['type'] !== 'access') {
            return false;
        }

        return $payload;
    }

    /**
     * 验证Refresh Token
     * @param string $token
     * @return array|false
     */
    public static function validateRefreshToken(string $token)
    {
        $payload = self::parseToken($token);
        
        if (!$payload || !isset($payload['type']) || $payload['type'] !== 'refresh') {
            return false;
        }

        return $payload;
    }

    /**
     * 从请求头中提取Token
     * @param string $header Authorization头部值
     * @return string|null
     */
    public static function extractTokenFromHeader(string $header): ?string
    {
        if (preg_match('/Bearer\s+(.*)$/i', $header, $matches)) {
            return $matches[1];
        }
        return null;
    }

    /**
     * 检查Token是否即将过期（剩余时间少于30分钟）
     * @param array $payload Token载荷
     * @return bool
     */
    public static function isTokenExpiringSoon(array $payload): bool
    {
        if (!isset($payload['exp'])) {
            return true;
        }

        $remainingTime = $payload['exp'] - time();
        return $remainingTime < 1800; // 30分钟
    }

    /**
     * 获取Token剩余有效时间（秒）
     * @param array $payload Token载荷
     * @return int
     */
    public static function getTokenRemainingTime(array $payload): int
    {
        if (!isset($payload['exp'])) {
            return 0;
        }

        return max(0, $payload['exp'] - time());
    }
}
