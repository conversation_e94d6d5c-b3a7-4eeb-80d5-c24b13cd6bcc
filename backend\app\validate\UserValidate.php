<?php

namespace app\validate;

use think\Validate;

/**
 * 用户相关验证器
 */
class UserValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'email' => 'email|max:100',
        'username' => 'alphaNum|length:3,50',
        'nickname' => 'max:50',
        'avatar' => 'url|max:255',
        'theme' => 'in:light,dark',
        'language' => 'in:zh-CN,en-US',
        'items_per_page' => 'integer|between:10,100',
        'default_view' => 'in:list,grid,card',
        'auto_backup' => 'boolean',
        'email_notifications' => 'boolean',
    ];

    /**
     * 验证消息
     */
    protected $message = [
        'email.email' => '邮箱地址格式不正确',
        'email.max' => '邮箱地址长度不能超过100个字符',
        'username.alphaNum' => '用户名只能包含字母和数字',
        'username.length' => '用户名长度必须在3-50个字符之间',
        'nickname.max' => '昵称长度不能超过50个字符',
        'avatar.url' => '头像必须是有效的URL地址',
        'avatar.max' => '头像URL长度不能超过255个字符',
        'theme.in' => '主题只能是light或dark',
        'language.in' => '语言只能是zh-CN或en-US',
        'items_per_page.integer' => '每页显示数量必须是整数',
        'items_per_page.between' => '每页显示数量必须在10-100之间',
        'default_view.in' => '默认视图只能是list、grid或card',
        'auto_backup.boolean' => '自动备份参数必须是布尔值',
        'email_notifications.boolean' => '邮件通知参数必须是布尔值',
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'update' => ['email', 'username', 'nickname', 'avatar'],
        'preferences' => ['theme', 'language', 'items_per_page', 'default_view', 'auto_backup', 'email_notifications'],
    ];
}
