import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

/**
 * 日期格式化工具
 */
export const formatDate = {
  // 格式化为标准日期时间
  datetime(date: string | Date | number, format = 'YYYY-MM-DD HH:mm:ss'): string {
    return dayjs(date).format(format)
  },

  // 格式化为日期
  date(date: string | Date | number, format = 'YYYY-MM-DD'): string {
    return dayjs(date).format(format)
  },

  // 格式化为时间
  time(date: string | Date | number, format = 'HH:mm:ss'): string {
    return dayjs(date).format(format)
  },

  // 相对时间（如：2小时前）
  relative(date: string | Date | number): string {
    return dayjs(date).fromNow()
  },

  // 友好的时间显示
  friendly(date: string | Date | number): string {
    const now = dayjs()
    const target = dayjs(date)
    const diff = now.diff(target, 'day')

    if (diff === 0) {
      return target.format('HH:mm')
    } else if (diff === 1) {
      return `昨天 ${target.format('HH:mm')}`
    } else if (diff < 7) {
      return `${diff}天前`
    } else if (diff < 30) {
      return `${Math.floor(diff / 7)}周前`
    } else if (diff < 365) {
      return `${Math.floor(diff / 30)}个月前`
    } else {
      return target.format('YYYY-MM-DD')
    }
  }
}

/**
 * 数字格式化工具
 */
export const formatNumber = {
  // 千分位分隔符
  thousands(num: number): string {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  },

  // 文件大小格式化
  fileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 百分比格式化
  percentage(num: number, decimals = 2): string {
    return (num * 100).toFixed(decimals) + '%'
  },

  // 缩写大数字（如：1.2K, 3.4M）
  abbreviate(num: number): string {
    if (num < 1000) return num.toString()
    
    const units = ['', 'K', 'M', 'B', 'T']
    const unitIndex = Math.floor(Math.log10(num) / 3)
    const value = num / Math.pow(1000, unitIndex)
    
    return value.toFixed(1) + units[unitIndex]
  }
}

/**
 * 字符串格式化工具
 */
export const formatString = {
  // 截断字符串
  truncate(str: string, length: number, suffix = '...'): string {
    if (str.length <= length) return str
    return str.substring(0, length) + suffix
  },

  // 首字母大写
  capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1)
  },

  // 驼峰转短横线
  kebabCase(str: string): string {
    return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()
  },

  // 短横线转驼峰
  camelCase(str: string): string {
    return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase())
  },

  // 移除HTML标签
  stripHtml(str: string): string {
    return str.replace(/<[^>]*>/g, '')
  },

  // URL友好化
  slugify(str: string): string {
    return str
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '')
  }
}

/**
 * URL格式化工具
 */
export const formatUrl = {
  // 添加协议前缀
  addProtocol(url: string): string {
    if (!/^https?:\/\//i.test(url)) {
      return 'https://' + url
    }
    return url
  },

  // 获取域名
  getDomain(url: string): string {
    try {
      return new URL(this.addProtocol(url)).hostname
    } catch {
      return url
    }
  },

  // 获取favicon URL
  getFavicon(url: string): string {
    const domain = this.getDomain(url)
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`
  },

  // 验证URL格式
  isValid(url: string): boolean {
    try {
      new URL(this.addProtocol(url))
      return true
    } catch {
      return false
    }
  }
}

/**
 * 颜色格式化工具
 */
export const formatColor = {
  // 生成随机颜色
  random(): string {
    return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')
  },

  // 根据字符串生成固定颜色
  fromString(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash)
    }
    
    const color = (hash & 0x00FFFFFF).toString(16).toUpperCase()
    return '#' + '00000'.substring(0, 6 - color.length) + color
  },

  // 判断颜色是否为深色
  isDark(color: string): boolean {
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    
    // 使用相对亮度公式
    const brightness = (r * 299 + g * 587 + b * 114) / 1000
    return brightness < 128
  }
}
