<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddNicknameToUsersTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     */
    public function change()
    {
        $table = $this->table('users');
        
        $table->addColumn('nickname', 'string', [
            'limit' => 50,
            'null' => true,
            'default' => null,
            'comment' => '用户昵称',
            'after' => 'username'
        ])
        ->update();
    }
}
