<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateFoldersTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $table = $this->table('folders', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => '文件夹表'
        ]);

        $table->addColumn('id', 'biginteger', [
            'identity' => true,
            'signed' => false,
            'comment' => '文件夹ID'
        ])
        ->addColumn('user_id', 'biginteger', [
            'signed' => false,
            'null' => false,
            'comment' => '用户ID'
        ])
        ->addColumn('parent_id', 'biginteger', [
            'signed' => false,
            'null' => true,
            'default' => null,
            'comment' => '父文件夹ID'
        ])
        ->addColumn('name', 'string', [
            'limit' => 100,
            'null' => false,
            'comment' => '文件夹名称'
        ])
        ->addColumn('icon', 'string', [
            'limit' => 50,
            'null' => true,
            'default' => null,
            'comment' => '图标（emoji或预设图标代码）'
        ])
        ->addColumn('color', 'string', [
            'limit' => 7,
            'null' => true,
            'default' => null,
            'comment' => '颜色代码（#开头的16进制）'
        ])
        ->addColumn('sort_order', 'integer', [
            'null' => false,
            'default' => 0,
            'comment' => '排序顺序'
        ])
        ->addColumn('path', 'string', [
            'limit' => 500,
            'null' => false,
            'default' => '',
            'comment' => '文件夹路径（如：/1/2/3/）'
        ])
        ->addColumn('level', 'integer', [
            'limit' => 1,
            'null' => false,
            'default' => 1,
            'comment' => '层级深度'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => false,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addColumn('updated_at', 'timestamp', [
            'null' => false,
            'default' => 'CURRENT_TIMESTAMP',
            'update' => 'CURRENT_TIMESTAMP',
            'comment' => '更新时间'
        ])
        ->addIndex(['user_id'], ['name' => 'idx_user_id'])
        ->addIndex(['parent_id'], ['name' => 'idx_parent_id'])
        ->addIndex(['path'], ['name' => 'idx_path'])
        ->addIndex(['sort_order'], ['name' => 'idx_sort_order'])
        ->addForeignKey('user_id', 'users', 'id', [
            'delete' => 'CASCADE',
            'constraint' => 'fk_folders_users'
        ])
        ->addForeignKey('parent_id', 'folders', 'id', [
            'delete' => 'CASCADE',
            'constraint' => 'fk_folders_parent'
        ])
        ->create();
    }
}
