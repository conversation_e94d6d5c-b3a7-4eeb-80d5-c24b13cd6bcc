<?php
namespace app;

use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\Response;
use Throwable;
use app\common\Response as ApiResponse;
use app\common\ErrorCode;
use app\exception\BusinessException;

/**
 * 应用异常处理类
 */
class ExceptionHandle extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ValidateException::class,
    ];

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     *
     * @access public
     * @param  Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 使用内置的方式记录异常日志
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @access public
     * @param \think\Request   $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        // 判断是否为API请求
        if ($this->isApiRequest($request)) {
            return $this->renderApiException($request, $e);
        }

        // 其他错误交给系统处理
        return parent::render($request, $e);
    }

    /**
     * 判断是否为API请求
     * @param \think\Request $request
     * @return bool
     */
    protected function isApiRequest($request): bool
    {
        // 检查请求路径是否以api开头
        $path = $request->pathinfo();
        if (strpos($path, 'api/') === 0) {
            return true;
        }

        // 检查Accept头部是否包含application/json
        $accept = $request->header('Accept', '');
        if (strpos($accept, 'application/json') !== false) {
            return true;
        }

        // 检查Content-Type是否为application/json
        $contentType = $request->header('Content-Type', '');
        if (strpos($contentType, 'application/json') !== false) {
            return true;
        }

        return false;
    }

    /**
     * 渲染API异常响应
     * @param \think\Request $request
     * @param Throwable $e
     * @return Response
     */
    protected function renderApiException($request, Throwable $e): Response
    {
        // 业务异常
        if ($e instanceof BusinessException) {
            return ApiResponse::error(
                $e->getMessage(),
                $e->getErrorCode(),
                $e->getErrors(),
                $e->getCode()
            );
        }

        // 参数验证异常
        if ($e instanceof ValidateException) {
            return ApiResponse::validateFailed($e->getError(), $e->getMessage());
        }

        // HTTP异常
        if ($e instanceof HttpException) {
            $statusCode = $e->getStatusCode();
            $message = $e->getMessage() ?: $this->getHttpErrorMessage($statusCode);

            switch ($statusCode) {
                case 401:
                    return ApiResponse::unauthorized($message);
                case 403:
                    return ApiResponse::forbidden($message);
                case 404:
                    return ApiResponse::notFound($message);
                case 429:
                    return ApiResponse::tooManyRequests($message);
                default:
                    return ApiResponse::error($message, ErrorCode::SERVER_ERROR, null, $statusCode);
            }
        }

        // 数据库相关异常
        if ($e instanceof ModelNotFoundException || $e instanceof DataNotFoundException) {
            return ApiResponse::notFound('请求的资源不存在');
        }

        // 开发环境显示详细错误信息
        if (env('APP_DEBUG', false)) {
            return ApiResponse::serverError($e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
        }

        // 生产环境返回通用错误信息
        return ApiResponse::serverError('服务器内部错误');
    }

    /**
     * 获取HTTP错误状态码对应的默认消息
     * @param int $statusCode
     * @return string
     */
    protected function getHttpErrorMessage(int $statusCode): string
    {
        $messages = [
            400 => '请求参数错误',
            401 => '未认证或认证失效',
            403 => '无权限访问',
            404 => '请求的资源不存在',
            405 => '请求方法不被允许',
            422 => '请求格式正确但无法处理',
            429 => '请求过于频繁',
            500 => '服务器内部错误',
            502 => '网关错误',
            503 => '服务暂时不可用',
        ];

        return $messages[$statusCode] ?? '未知错误';
    }
}
