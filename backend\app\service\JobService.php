<?php

namespace app\service;

use app\exception\BusinessException;
use think\facade\Cache;

/**
 * 任务服务类
 */
class JobService
{
    /**
     * 获取任务状态
     * @param int $userId 用户ID
     * @param string $jobId 任务ID
     * @return array 任务状态信息
     * @throws BusinessException
     */
    public function getJobStatus(int $userId, string $jobId): array
    {
        // 从缓存中获取任务状态
        $cacheKey = "job_status:{$userId}:{$jobId}";
        $jobStatus = Cache::get($cacheKey);

        if (!$jobStatus) {
            // 如果缓存中没有，尝试从模拟数据中获取
            $jobStatus = $this->getMockJobStatus($jobId);
            if (!$jobStatus) {
                throw new BusinessException('任务不存在或已过期', 30006);
            }
        }

        return $jobStatus;
    }

    /**
     * 创建任务状态记录
     * @param int $userId 用户ID
     * @param string $jobId 任务ID
     * @param string $type 任务类型
     * @param array $initialData 初始数据
     * @return bool
     */
    public function createJobStatus(int $userId, string $jobId, string $type, array $initialData = []): bool
    {
        $jobStatus = array_merge([
            'job_id' => $jobId,
            'user_id' => $userId,
            'type' => $type,
            'status' => 'pending',
            'progress' => 0,
            'total_items' => 0,
            'processed_items' => 0,
            'success_items' => 0,
            'failed_items' => 0,
            'start_time' => date('Y-m-d H:i:s'),
            'end_time' => null,
            'duration' => 0,
            'result' => null,
            'errors' => []
        ], $initialData);

        $cacheKey = "job_status:{$userId}:{$jobId}";
        return Cache::set($cacheKey, $jobStatus, 3600); // 缓存1小时
    }

    /**
     * 更新任务状态
     * @param int $userId 用户ID
     * @param string $jobId 任务ID
     * @param array $updateData 更新数据
     * @return bool
     */
    public function updateJobStatus(int $userId, string $jobId, array $updateData): bool
    {
        $cacheKey = "job_status:{$userId}:{$jobId}";
        $jobStatus = Cache::get($cacheKey);

        if (!$jobStatus) {
            return false;
        }

        // 合并更新数据
        $jobStatus = array_merge($jobStatus, $updateData);

        // 如果任务完成，计算持续时间
        if (isset($updateData['status']) && in_array($updateData['status'], ['completed', 'failed', 'cancelled'])) {
            $jobStatus['end_time'] = date('Y-m-d H:i:s');
            if ($jobStatus['start_time']) {
                $startTime = strtotime($jobStatus['start_time']);
                $endTime = strtotime($jobStatus['end_time']);
                $jobStatus['duration'] = $endTime - $startTime;
            }
        }

        return Cache::set($cacheKey, $jobStatus, 3600);
    }

    /**
     * 删除任务状态记录
     * @param int $userId 用户ID
     * @param string $jobId 任务ID
     * @return bool
     */
    public function deleteJobStatus(int $userId, string $jobId): bool
    {
        $cacheKey = "job_status:{$userId}:{$jobId}";
        return Cache::delete($cacheKey);
    }

    /**
     * 获取用户的所有任务状态
     * @param int $userId 用户ID
     * @param int $limit 限制数量
     * @return array 任务状态列表
     */
    public function getUserJobs(int $userId, int $limit = 20): array
    {
        // 这里简化处理，实际项目中可能需要从数据库获取
        // 或者使用更复杂的缓存策略
        return [];
    }

    /**
     * 获取模拟任务状态（用于演示）
     * @param string $jobId 任务ID
     * @return array|null 任务状态
     */
    private function getMockJobStatus(string $jobId): ?array
    {
        // 解析任务ID获取类型和时间
        if (strpos($jobId, 'import_') === 0) {
            return [
                'job_id' => $jobId,
                'type' => 'import',
                'status' => 'completed',
                'progress' => 100,
                'total_items' => 150,
                'processed_items' => 150,
                'success_items' => 145,
                'failed_items' => 5,
                'start_time' => date('Y-m-d H:i:s', time() - 300),
                'end_time' => date('Y-m-d H:i:s', time() - 30),
                'duration' => 270,
                'result' => [
                    'imported_count' => 145,
                    'skipped_count' => 5,
                    'folders_created' => 3,
                    'tags_created' => 8,
                    'errors' => [
                        '第10行：URL格式无效',
                        '第25行：标题为空',
                        '第67行：重复的书签',
                        '第89行：文件夹创建失败',
                        '第123行：标签名称过长'
                    ]
                ]
            ];
        } elseif (strpos($jobId, 'export_') === 0) {
            return [
                'job_id' => $jobId,
                'type' => 'export',
                'status' => 'completed',
                'progress' => 100,
                'total_items' => 200,
                'processed_items' => 200,
                'success_items' => 200,
                'failed_items' => 0,
                'start_time' => date('Y-m-d H:i:s', time() - 120),
                'end_time' => date('Y-m-d H:i:s', time() - 10),
                'duration' => 110,
                'result' => [
                    'file_name' => 'bookmarks_' . date('Ymd_His') . '.html',
                    'file_size' => 1024000,
                    'download_url' => '/api/v1/export/download/' . $jobId,
                    'expires_at' => date('Y-m-d H:i:s', time() + 3600)
                ]
            ];
        }

        return null;
    }

    /**
     * 清理过期的任务状态
     * @param int $expireTime 过期时间（秒）
     * @return int 清理的数量
     */
    public function cleanExpiredJobs(int $expireTime = 3600): int
    {
        // 这里简化处理，实际项目中需要实现具体的清理逻辑
        return 0;
    }

    /**
     * 获取任务统计信息
     * @param int $userId 用户ID
     * @param string $type 任务类型
     * @param int $days 统计天数
     * @return array 统计信息
     */
    public function getJobStatistics(int $userId, string $type = '', int $days = 7): array
    {
        // 这里返回模拟数据，实际项目中需要从数据库统计
        return [
            'total_jobs' => 15,
            'completed_jobs' => 12,
            'failed_jobs' => 2,
            'running_jobs' => 1,
            'success_rate' => 80.0,
            'avg_duration' => 180
        ];
    }
}
