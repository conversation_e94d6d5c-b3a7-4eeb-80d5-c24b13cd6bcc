<?php
// +----------------------------------------------------------------------
// | 书签管理系统 API 路由配置 - 简化版本
// +----------------------------------------------------------------------
use think\facade\Route;

// 默认首页（保留ThinkPHP欢迎页）
Route::get('/', function () {
    return 'hello,ThinkPHP8!';
});

// API路由组
Route::group('api/v1', function () {

    // 认证相关路由（无需认证）
    Route::group('auth', function () {
        Route::post('register', 'app\\controller\\api\\v1\\Auth@register');        // 用户注册
        Route::post('login', 'app\\controller\\api\\v1\\Auth@login');              // 用户登录
        Route::post('refresh', 'app\\controller\\api\\v1\\Auth@refresh');          // 刷新令牌
        Route::post('logout', 'app\\controller\\api\\v1\\Auth@logout')->middleware('auth'); // 用户登出
    });

    // 系统配置路由
    Route::group('system', function () {
        Route::get('config', 'app\\controller\\api\\v1\\System@config');           // 获取系统配置（公开）
        Route::get('status', 'app\\controller\\api\\v1\\System@status');           // 获取系统状态（公开）
    });

});

// 兼容旧版本路由
Route::get('think', function () {
    return 'hello,ThinkPHP8!';
});

Route::get('hello/:name', 'app\\controller\\Index@hello');
