<template>
  <div class="app-sidebar">
    <div class="sidebar-logo" v-if="!collapsed">
      <h2>📚 书签管理</h2>
    </div>
    <div class="sidebar-logo-mini" v-else>
      <span>📚</span>
    </div>

    <el-menu
      :default-active="activeMenu"
      class="sidebar-menu"
      router
      :collapse="collapsed"
      :unique-opened="true"
    >
      <el-menu-item index="/dashboard">
        <el-icon><House /></el-icon>
        <template #title>仪表板</template>
      </el-menu-item>

      <el-sub-menu index="bookmarks">
        <template #title>
          <el-icon><Collection /></el-icon>
          <span>书签管理</span>
        </template>
        <el-menu-item index="/bookmarks">
          <el-icon><List /></el-icon>
          <template #title>我的书签</template>
        </el-menu-item>
        <el-menu-item index="/bookmarks/starred">
          <el-icon><Star /></el-icon>
          <template #title>收藏书签</template>
        </el-menu-item>
        <el-menu-item index="/bookmarks/recent">
          <el-icon><Clock /></el-icon>
          <template #title>最近添加</template>
        </el-menu-item>
      </el-sub-menu>

      <el-menu-item index="/folders">
        <el-icon><Folder /></el-icon>
        <template #title>文件夹管理</template>
      </el-menu-item>

      <el-menu-item index="/tags">
        <el-icon><PriceTag /></el-icon>
        <template #title>标签管理</template>
      </el-menu-item>

      <el-menu-item index="/search">
        <el-icon><Search /></el-icon>
        <template #title>搜索</template>
      </el-menu-item>

      <el-sub-menu index="advanced">
        <template #title>
          <el-icon><Setting /></el-icon>
          <span>高级功能</span>
        </template>
        <el-menu-item index="/smart-folders">
          <el-icon><MagicStick /></el-icon>
          <template #title>智能文件夹</template>
        </el-menu-item>
        <el-menu-item index="/import-export">
          <el-icon><Download /></el-icon>
          <template #title>导入导出</template>
        </el-menu-item>
        <el-menu-item index="/stats">
          <el-icon><DataAnalysis /></el-icon>
          <template #title>统计分析</template>
        </el-menu-item>
      </el-sub-menu>

      <el-menu-item index="/trash">
        <el-icon><Delete /></el-icon>
        <template #title>回收站</template>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import {
  House,
  Collection,
  Folder,
  PriceTag,
  Search,
  DataAnalysis,
  Delete,
  List,
  Star,
  Clock,
  Setting,
  MagicStick,
  Download
} from '@element-plus/icons-vue'

interface Props {
  collapsed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false
})

const route = useRoute()
const activeMenu = computed(() => {
  const path = route.path
  // 处理子菜单的高亮
  if (path.startsWith('/bookmarks')) {
    return path
  }
  return path
})
</script>

<style scoped>
.app-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-logo {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #e4e7ed;
}

.sidebar-logo h2 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.sidebar-logo-mini {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #e4e7ed;
  font-size: 24px;
}

.sidebar-menu {
  flex: 1;
  border-right: none;
  overflow-y: auto;
}

.sidebar-menu .el-menu-item {
  height: 48px;
  line-height: 48px;
  margin: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s;
}

.sidebar-menu .el-sub-menu {
  margin: 4px 8px;
}

.sidebar-menu .el-sub-menu .el-menu-item {
  margin: 2px 0;
  border-radius: 4px;
}

.sidebar-menu .el-menu-item:hover {
  background-color: #ecf5ff;
  color: #409eff;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #409eff;
  color: white;
}

.sidebar-menu .el-menu-item.is-active .el-icon {
  color: white;
}

.sidebar-menu .el-sub-menu__title:hover {
  background-color: #ecf5ff;
  color: #409eff;
}

/* 折叠状态样式 */
.sidebar-menu.el-menu--collapse {
  width: 64px;
}

.sidebar-menu.el-menu--collapse .el-menu-item {
  margin: 4px 8px;
  text-align: center;
}

.sidebar-menu.el-menu--collapse .el-sub-menu {
  margin: 4px 8px;
}

/* 滚动条样式 */
.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
