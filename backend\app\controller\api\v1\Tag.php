<?php

namespace app\controller\api\v1;

use app\BaseController;
use app\service\TagService;
use app\validate\TagValidate;
use app\exception\BusinessException;
use think\Response;

/**
 * 标签管理控制器
 *
 * 提供标签的CRUD操作、合并、标签云等功能
 */
class Tag extends BaseController
{
    /**
     * 标签服务
     * @var TagService
     */
    protected $tagService;

    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->tagService = new TagService();
    }

    /**
     * 获取标签列表
     *
     * 获取当前用户的标签列表，支持搜索和排序
     *
     * @route GET /api/v1/tags
     * @middleware auth
     * @param string $keyword 关键词搜索，可选
     * @param string $sort 排序方式：name|bookmark_count|created_at，默认bookmark_count
     * @param string $order 排序顺序：asc|desc，默认desc
     * @return Response JSON响应，包含标签列表
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/tags?keyword=技术&sort=bookmark_count&order=desc
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取标签列表成功",
     *   "data": [
     *     {
     *       "id": 1,
     *       "name": "技术",
     *       "color": "#1890ff",
     *       "bookmark_count": 25,
     *       "created_at": "2024-06-30 12:00:00"
     *     }
     *   ]
     * }
     */
    public function index(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $params = $this->request->get();
            $tags = $this->tagService->getTagList($userId, $params);
            return $this->success($tags, '获取标签列表成功');
        } catch (\Exception $e) {
            return $this->error('获取标签列表失败：' . $e->getMessage());
        }
    }

    /**
     * 创建标签
     *
     * 创建新的标签，支持设置颜色等属性
     *
     * @route POST /api/v1/tags
     * @middleware auth
     * @param string $name 标签名称，必填，1-50个字符，同一用户下不能重复
     * @param string $color 标签颜色，可选，十六进制颜色值，如：#1890ff
     * @return Response JSON响应，包含创建的标签信息
     * @throws BusinessException 参数验证失败或标签名称已存在
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/tags
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "name": "技术",
     *   "color": "#1890ff"
     * }
     * Response: {
     *   "code": 201,
     *   "message": "创建标签成功",
     *   "data": {
     *     "id": 1,
     *     "name": "技术",
     *     "color": "#1890ff",
     *     "bookmark_count": 0,
     *     "created_at": "2024-06-30 12:00:00"
     *   }
     * }
     */
    public function create(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $data = $this->request->post();
            $this->validate($data, TagValidate::class . '.create');

            $tag = $this->tagService->createTag($userId, $data);
            return $this->created($tag->toApiArray(), '创建标签成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('创建标签失败：' . $e->getMessage());
        }
    }

    /**
     * 更新标签
     *
     * 更新指定标签的信息，支持部分字段更新
     *
     * @route PUT /api/v1/tags/{id}
     * @middleware auth
     * @param int $id 标签ID，路径参数
     * @param string $name 标签名称，可选，1-50个字符
     * @param string $color 标签颜色，可选，十六进制颜色值
     * @return Response JSON响应，包含更新后的标签信息
     * @throws BusinessException 参数验证失败、标签不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * PUT /api/v1/tags/1
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "name": "技术 - 更新",
     *   "color": "#52c41a"
     * }
     * Response: {
     *   "code": 200,
     *   "message": "更新标签成功",
     *   "data": {
     *     "id": 1,
     *     "name": "技术 - 更新",
     *     "color": "#52c41a",
     *     "updated_at": "2024-06-30 12:30:00"
     *   }
     * }
     */
    public function update(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $tagId = (int)$this->request->param('id');
            $data = $this->request->put();
            $this->validate($data, TagValidate::class . '.update');

            $tag = $this->tagService->updateTag($userId, $tagId, $data);
            return $this->success($tag->toApiArray(), '更新标签成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('更新标签失败：' . $e->getMessage());
        }
    }

    /**
     * 删除标签
     *
     * 删除指定标签，会解除与所有书签的关联关系
     *
     * @route DELETE /api/v1/tags/{id}
     * @middleware auth
     * @param int $id 标签ID，路径参数
     * @return Response JSON响应，确认删除成功
     * @throws BusinessException 标签不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * DELETE /api/v1/tags/1
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "删除标签成功",
     *   "data": null
     * }
     */
    public function delete(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $tagId = (int)$this->request->param('id');

            $this->tagService->deleteTag($userId, $tagId);
            return $this->success(null, '删除标签成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('删除标签失败：' . $e->getMessage());
        }
    }

    /**
     * 获取标签云
     *
     * 获取标签云数据，包含标签使用频率和权重信息，用于可视化展示
     *
     * @route GET /api/v1/tags/cloud
     * @middleware auth
     * @param int $limit 返回标签数量限制，默认50，最大200
     * @param int $min_count 最小使用次数，默认1
     * @return Response JSON响应，包含标签云数据
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/tags/cloud?limit=50&min_count=1
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取标签云成功",
     *   "data": [
     *     {
     *       "id": 1,
     *       "name": "技术",
     *       "color": "#1890ff",
     *       "bookmark_count": 25,
     *       "weight": 0.8
     *     }
     *   ]
     * }
     */
    public function cloud(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $tagCloud = $this->tagService->getTagCloud($userId);
            return $this->success($tagCloud, '获取标签云成功');
        } catch (\Exception $e) {
            return $this->error('获取标签云失败：' . $e->getMessage());
        }
    }

    /**
     * 合并标签
     *
     * 将多个标签合并为一个标签，所有关联的书签将转移到目标标签
     *
     * @route POST /api/v1/tags/merge
     * @middleware auth
     * @param array $source_tag_ids 要合并的源标签ID列表，必填
     * @param int $target_tag_id 目标标签ID，必填
     * @return Response JSON响应，包含合并结果统计
     * @throws BusinessException 参数验证失败、标签不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/tags/merge
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "source_tag_ids": [2, 3, 4],
     *   "target_tag_id": 1
     * }
     * Response: {
     *   "code": 200,
     *   "message": "合并标签成功",
     *   "data": {
     *     "target_tag": {
     *       "id": 1,
     *       "name": "技术",
     *       "bookmark_count": 35
     *     },
     *     "merged_count": 3,
     *     "affected_bookmarks": 15
     *   }
     * }
     */
    public function merge(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $data = $this->request->post();
            $this->validate($data, TagValidate::class . '.merge');

            $this->tagService->mergeTags($userId, $data['source_tag_ids'], $data['target_tag_id']);
            return $this->success(null, '合并标签成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('合并标签失败：' . $e->getMessage());
        }
    }
}
