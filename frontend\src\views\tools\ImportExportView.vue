<template>
  <div class="import-export">
    <div class="page-header">
      <h1>导入导出</h1>
      <p>支持多种格式的书签导入导出功能</p>
    </div>
    
    <div class="tools-content">
      <el-row :gutter="20">
        <!-- 导入区域 -->
        <el-col :span="12">
          <el-card title="导入书签">
            <template #header>
              <div class="card-header">
                <span>导入书签</span>
                <el-icon><Upload /></el-icon>
              </div>
            </template>
            
            <div class="import-section">
              <el-alert
                title="支持的格式"
                type="info"
                :closable="false"
                show-icon
              >
                <template #default>
                  <ul class="format-list">
                    <li>Chrome/Edge 书签文件 (.html)</li>
                    <li>Firefox 书签文件 (.json)</li>
                    <li>Safari 书签文件 (.plist)</li>
                    <li>通用 JSON 格式 (.json)</li>
                    <li>CSV 格式 (.csv)</li>
                  </ul>
                </template>
              </el-alert>
              
              <el-upload
                ref="uploadRef"
                class="upload-area"
                drag
                :auto-upload="false"
                :show-file-list="false"
                accept=".html,.json,.plist,.csv"
                @change="handleFileChange"
              >
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    支持 .html, .json, .plist, .csv 格式文件
                  </div>
                </template>
              </el-upload>
              
              <div v-if="importFile" class="file-info">
                <div class="file-item">
                  <el-icon><Document /></el-icon>
                  <span class="file-name">{{ importFile.name }}</span>
                  <span class="file-size">({{ formatFileSize(importFile.size) }})</span>
                  <el-button @click="clearFile" text type="danger">
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
              </div>
              
              <div class="import-options" v-if="importFile">
                <el-form :model="importOptions" label-width="120px">
                  <el-form-item label="导入到文件夹">
                    <el-tree-select
                      v-model="importOptions.folderId"
                      :data="folderOptions"
                      :props="{ label: 'name', value: 'id', children: 'children' }"
                      placeholder="选择文件夹（可选）"
                      clearable
                      check-strictly
                    />
                  </el-form-item>
                  
                  <el-form-item label="重复处理">
                    <el-radio-group v-model="importOptions.duplicateAction">
                      <el-radio label="skip">跳过重复项</el-radio>
                      <el-radio label="update">更新重复项</el-radio>
                      <el-radio label="create">创建副本</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  
                  <el-form-item>
                    <el-button 
                      type="primary" 
                      @click="handleImport"
                      :loading="importing"
                    >
                      开始导入
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 导出区域 -->
        <el-col :span="12">
          <el-card title="导出书签">
            <template #header>
              <div class="card-header">
                <span>导出书签</span>
                <el-icon><Download /></el-icon>
              </div>
            </template>
            
            <div class="export-section">
              <el-form :model="exportOptions" label-width="100px">
                <el-form-item label="导出格式">
                  <el-select v-model="exportOptions.format" placeholder="选择格式">
                    <el-option label="HTML (Chrome/Edge)" value="html" />
                    <el-option label="JSON (Firefox)" value="json" />
                    <el-option label="CSV" value="csv" />
                    <el-option label="通用 JSON" value="generic_json" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="导出范围">
                  <el-radio-group v-model="exportOptions.scope">
                    <el-radio label="all">全部书签</el-radio>
                    <el-radio label="folder">指定文件夹</el-radio>
                    <el-radio label="starred">收藏书签</el-radio>
                    <el-radio label="tag">指定标签</el-radio>
                  </el-radio-group>
                </el-form-item>
                
                <el-form-item 
                  v-if="exportOptions.scope === 'folder'" 
                  label="选择文件夹"
                >
                  <el-tree-select
                    v-model="exportOptions.folderId"
                    :data="folderOptions"
                    :props="{ label: 'name', value: 'id', children: 'children' }"
                    placeholder="选择文件夹"
                    check-strictly
                  />
                </el-form-item>
                
                <el-form-item 
                  v-if="exportOptions.scope === 'tag'" 
                  label="选择标签"
                >
                  <el-select v-model="exportOptions.tagId" placeholder="选择标签">
                    <el-option
                      v-for="tag in tagOptions"
                      :key="tag.id"
                      :label="tag.name"
                      :value="tag.id"
                    />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="包含内容">
                  <el-checkbox-group v-model="exportOptions.includes">
                    <el-checkbox label="description">描述</el-checkbox>
                    <el-checkbox label="tags">标签</el-checkbox>
                    <el-checkbox label="folder">文件夹信息</el-checkbox>
                    <el-checkbox label="metadata">元数据</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                
                <el-form-item>
                  <el-button 
                    type="primary" 
                    @click="handleExport"
                    :loading="exporting"
                  >
                    开始导出
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 导入进度 -->
      <el-card v-if="importProgress.show" class="progress-card">
        <template #header>
          <div class="card-header">
            <span>导入进度</span>
            <el-button @click="cancelImport" text type="danger" v-if="importing">
              取消导入
            </el-button>
          </div>
        </template>
        
        <div class="progress-content">
          <el-progress 
            :percentage="importProgress.percentage" 
            :status="importProgress.status"
          />
          <div class="progress-info">
            <span>{{ importProgress.message }}</span>
            <span class="progress-stats">
              成功: {{ importProgress.success }} | 
              失败: {{ importProgress.failed }} | 
              跳过: {{ importProgress.skipped }}
            </span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  Upload, 
  Download, 
  UploadFilled, 
  Document, 
  Close 
} from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import { useFolderStore } from '@/stores/folder'
import { useTagStore } from '@/stores/tag'
import { useImportExportStore } from '@/stores/importExport'

const folderStore = useFolderStore()
const tagStore = useTagStore()
const importExportStore = useImportExportStore()

const uploadRef = ref()
const importFile = ref<File | null>(null)
const importing = ref(false)
const exporting = ref(false)

const importOptions = reactive({
  folderId: null,
  duplicateAction: 'skip' as 'skip' | 'update' | 'create'
})

const exportOptions = reactive({
  format: 'html' as 'html' | 'json' | 'csv' | 'generic_json',
  scope: 'all' as 'all' | 'folder' | 'tag' | 'starred',
  folderId: null,
  tagId: null,
  includes: ['description', 'tags', 'folder']
})

const importProgress = reactive({
  show: false,
  percentage: 0,
  status: 'success' as 'success' | 'exception' | 'warning',
  message: '',
  success: 0,
  failed: 0,
  skipped: 0
})

const folderOptions = computed(() => folderStore.folderTree)
const tagOptions = computed(() => tagStore.tags)

const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

const handleFileChange = (file: UploadFile) => {
  importFile.value = file.raw || null
}

const clearFile = () => {
  importFile.value = null
  uploadRef.value?.clearFiles()
}

const handleImport = async () => {
  if (!importFile.value) {
    ElMessage.warning('请先选择要导入的文件')
    return
  }
  
  importing.value = true
  importProgress.show = true
  importProgress.percentage = 0
  importProgress.status = 'success'
  importProgress.message = '正在解析文件...'
  importProgress.success = 0
  importProgress.failed = 0
  importProgress.skipped = 0
  
  try {
    await importExportStore.importBookmarks(importFile.value, importOptions, (progress) => {
      importProgress.percentage = progress.percentage
      importProgress.message = progress.message
      importProgress.success = progress.success
      importProgress.failed = progress.failed
      importProgress.skipped = progress.skipped
    })
    
    importProgress.status = 'success'
    importProgress.message = '导入完成'
    ElMessage.success('书签导入成功')
  } catch (error) {
    console.error('导入失败:', error)
    importProgress.status = 'exception'
    importProgress.message = '导入失败'
    ElMessage.error('导入失败，请检查文件格式')
  } finally {
    importing.value = false
  }
}

const handleExport = async () => {
  if (!exportOptions.format) {
    ElMessage.warning('请选择导出格式')
    return
  }
  
  exporting.value = true
  
  try {
    const blob = await importExportStore.exportBookmarks(exportOptions)
    
    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `bookmarks_${new Date().toISOString().split('T')[0]}.${exportOptions.format}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exporting.value = false
  }
}

const cancelImport = () => {
  importExportStore.cancelImport()
  importing.value = false
  importProgress.show = false
}

// 设置页面标题
useHead({
  title: '导入导出 - 书签管理系统'
})

onMounted(async () => {
  // 获取文件夹和标签列表
  await Promise.all([
    folderStore.fetchFolders(),
    tagStore.fetchTags()
  ])
})
</script>

<style scoped>
.import-export {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.tools-content {
  flex: 1;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.format-list {
  margin: 0;
  padding-left: 20px;
}

.format-list li {
  margin: 4px 0;
}

.upload-area {
  margin: 20px 0;
}

.file-info {
  margin: 16px 0;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.file-name {
  flex: 1;
  font-weight: 500;
  color: #303133;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.import-options {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.progress-card {
  margin-top: 20px;
}

.progress-content {
  padding: 20px 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  font-size: 14px;
  color: #606266;
}

.progress-stats {
  font-size: 12px;
  color: #909399;
}

@media (max-width: 768px) {
  .tools-content .el-row {
    margin: 0;
  }
  
  .tools-content .el-col {
    padding: 0 0 20px 0;
  }
}
</style>
