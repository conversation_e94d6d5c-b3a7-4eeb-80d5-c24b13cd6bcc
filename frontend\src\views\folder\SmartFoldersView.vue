<template>
  <div class="smart-folders">
    <div class="page-header">
      <h1>智能文件夹</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建智能文件夹
      </el-button>
    </div>
    
    <div class="smart-folders-content">
      <div class="folders-grid">
        <div 
          v-for="folder in smartFolders" 
          :key="folder.id"
          class="smart-folder-card"
          @click="openFolder(folder)"
        >
          <div class="folder-header">
            <div class="folder-icon">
              <el-icon><MagicStick /></el-icon>
            </div>
            <div class="folder-actions">
              <el-dropdown @command="(cmd) => handleFolderAction(cmd, folder)">
                <el-button :icon="MoreFilled" text />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="duplicate">复制</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          
          <div class="folder-content">
            <h3 class="folder-name">{{ folder.name }}</h3>
            <p class="folder-description">{{ folder.description }}</p>
            <div class="folder-rules">
              <el-tag 
                v-for="rule in folder.rules.slice(0, 2)" 
                :key="rule.id"
                size="small"
                class="rule-tag"
              >
                {{ formatRule(rule) }}
              </el-tag>
              <el-tag v-if="folder.rules.length > 2" size="small" type="info">
                +{{ folder.rules.length - 2 }}
              </el-tag>
            </div>
          </div>
          
          <div class="folder-footer">
            <span class="bookmark-count">{{ folder.bookmarkCount }} 个书签</span>
            <span class="update-time">{{ formatDate.friendly(folder.updated_at) }}</span>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="smartFolders.length === 0" class="empty-state">
          <AppEmpty 
            type="folder"
            title="暂无智能文件夹"
            description="智能文件夹可以根据规则自动收集符合条件的书签"
          >
            <template #actions>
              <el-button type="primary" @click="showCreateDialog = true">
                创建第一个智能文件夹
              </el-button>
            </template>
          </AppEmpty>
        </div>
      </div>
    </div>
    
    <!-- 创建/编辑智能文件夹对话框 -->
    <SmartFolderForm
      v-model="showCreateDialog"
      :folder="currentFolder"
      @success="handleFolderSuccess"
    />
    
    <!-- 删除确认对话框 -->
    <AppConfirm
      v-model="showDeleteDialog"
      type="warning"
      title="删除智能文件夹"
      :message="`确定要删除智能文件夹「${currentFolder?.name}」吗？`"
      @confirm="handleDeleteConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { Plus, MagicStick, MoreFilled } from '@element-plus/icons-vue'
import type { SmartFolder, SmartFolderRule } from '@/types/api'
import { formatDate } from '@/utils/format'
import { useSmartFolderStore } from '@/stores/smartFolder'
import SmartFolderForm from '@/components/business/SmartFolderForm.vue'
import AppEmpty from '@/components/common/AppEmpty.vue'
import AppConfirm from '@/components/common/AppConfirm.vue'

const router = useRouter()
const smartFolderStore = useSmartFolderStore()

const showCreateDialog = ref(false)
const showDeleteDialog = ref(false)
const currentFolder = ref<SmartFolder | null>(null)

const smartFolders = computed(() => smartFolderStore.smartFolders)

const formatRule = (rule: SmartFolderRule) => {
  const fieldNames = {
    title: '标题',
    url: '网址',
    description: '描述',
    tags: '标签',
    folder: '文件夹'
  }
  
  const operatorNames = {
    contains: '包含',
    not_contains: '不包含',
    equals: '等于',
    not_equals: '不等于',
    starts_with: '开始于',
    ends_with: '结束于'
  }
  
  return `${fieldNames[rule.field] || rule.field} ${operatorNames[rule.operator] || rule.operator} ${rule.value}`
}

const openFolder = (folder: SmartFolder) => {
  router.push({
    name: 'Bookmarks',
    query: { smart_folder: folder.id }
  })
}

const handleFolderAction = (command: string, folder: SmartFolder) => {
  currentFolder.value = folder
  
  switch (command) {
    case 'edit':
      showCreateDialog.value = true
      break
    case 'duplicate':
      duplicateFolder(folder)
      break
    case 'delete':
      showDeleteDialog.value = true
      break
  }
}

const duplicateFolder = async (folder: SmartFolder) => {
  try {
    await smartFolderStore.duplicateSmartFolder(folder.id)
    ElMessage.success('智能文件夹复制成功')
  } catch (error) {
    console.error('复制智能文件夹失败:', error)
    ElMessage.error('复制失败')
  }
}

const handleFolderSuccess = () => {
  currentFolder.value = null
  fetchSmartFolders()
}

const handleDeleteConfirm = async () => {
  if (!currentFolder.value) return
  
  try {
    await smartFolderStore.deleteSmartFolder(currentFolder.value.id)
    ElMessage.success('智能文件夹删除成功')
    currentFolder.value = null
  } catch (error) {
    console.error('删除智能文件夹失败:', error)
    ElMessage.error('删除失败')
  }
}

const fetchSmartFolders = async () => {
  try {
    await smartFolderStore.fetchSmartFolders()
  } catch (error) {
    console.error('获取智能文件夹失败:', error)
  }
}

// 设置页面标题
useHead({
  title: '智能文件夹 - 书签管理系统'
})

onMounted(() => {
  fetchSmartFolders()
})
</script>

<style scoped>
.smart-folders {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.smart-folders-content {
  flex: 1;
  overflow-y: auto;
}

.folders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  padding: 4px;
}

.smart-folder-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  height: 200px;
  display: flex;
  flex-direction: column;
}

.smart-folder-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
}

.folder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.folder-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.folder-actions {
  opacity: 0;
  transition: opacity 0.3s;
}

.smart-folder-card:hover .folder-actions {
  opacity: 1;
}

.folder-content {
  flex: 1;
  margin-bottom: 16px;
}

.folder-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.folder-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.folder-rules {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.rule-tag {
  font-size: 12px;
}

.folder-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #c0c4cc;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.bookmark-count {
  font-weight: 500;
  color: #409eff;
}

.empty-state {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

@media (max-width: 768px) {
  .folders-grid {
    grid-template-columns: 1fr;
  }
}
</style>
