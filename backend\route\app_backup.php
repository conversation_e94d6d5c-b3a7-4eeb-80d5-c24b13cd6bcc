<?php
// +----------------------------------------------------------------------
// | 书签管理系统 API 路由配置
// +----------------------------------------------------------------------
use think\facade\Route;

// 默认首页（保留ThinkPHP欢迎页）
Route::get('/', function () {
    return 'hello,ThinkPHP8!';
});

// API路由组
Route::group('api/v1', function () {

    // 认证相关路由（无需认证）
    Route::group('auth', function () {
        Route::post('register', 'app\\controller\\api\\v1\\Auth@register');        // 用户注册
        Route::post('login', 'app\\controller\\api\\v1\\Auth@login');              // 用户登录
        Route::post('refresh', 'app\\controller\\api\\v1\\Auth@refresh');          // 刷新令牌
        Route::post('logout', 'app\\controller\\api\\v1\\Auth@logout')->middleware('auth'); // 用户登出
    });

    // 系统配置路由
    Route::group('system', function () {
        Route::get('config', 'app\\controller\\api\\v1\\System@config');           // 获取系统配置（公开）
        Route::get('status', 'app\\controller\\api\\v1\\System@status');           // 获取系统状态（公开）
    });

    // 文件夹管理路由（需要认证）
    Route::group('folders', function () {
        Route::get('', [Folder::class, 'index']);                  // 获取文件夹列表
        Route::post('', [Folder::class, 'create']);                // 创建文件夹
        Route::get(':id', [Folder::class, 'show']);                // 获取文件夹详情
        Route::put(':id', [Folder::class, 'update']);              // 更新文件夹
        Route::delete(':id', [Folder::class, 'delete']);           // 删除文件夹
        Route::patch(':id/move', [Folder::class, 'move']);         // 移动文件夹
        Route::patch('sort', [Folder::class, 'sort']);             // 批量排序
    })->middleware('auth');

    // 标签管理路由（需要认证）
    Route::group('tags', function () {
        Route::get('', [Tag::class, 'index']);                     // 获取标签列表
        Route::post('', [Tag::class, 'create']);                   // 创建标签
        Route::put(':id', [Tag::class, 'update']);                 // 更新标签
        Route::delete(':id', [Tag::class, 'delete']);              // 删除标签
        Route::post('merge', [Tag::class, 'merge']);               // 合并标签
        Route::get('cloud', [Tag::class, 'cloud']);                // 获取标签云
    })->middleware('auth');

    // 书签管理路由（需要认证）
    Route::group('bookmarks', function () {
        Route::get('', [Bookmark::class, 'index']);                // 获取书签列表
        Route::post('', [Bookmark::class, 'create']);              // 创建书签
        Route::post('batch', [Bookmark::class, 'batch']);          // 批量操作
        Route::post('parse', [Bookmark::class, 'parse']);          // AI解析URL
        Route::post('batch-check', [Bookmark::class, 'batchCheck']); // 批量检测死链
        Route::get('dead-link-stats', [Bookmark::class, 'deadLinkStats']); // 死链统计
        Route::get(':id', [Bookmark::class, 'show']);              // 获取书签详情
        Route::put(':id', [Bookmark::class, 'update']);            // 更新书签
        Route::delete(':id', [Bookmark::class, 'delete']);         // 删除书签
        Route::post(':id/visit', [Bookmark::class, 'visit']);      // 访问统计
        Route::post(':id/parse', [Bookmark::class, 'parse']);      // AI解析书签
        Route::post(':id/check', [Bookmark::class, 'check']);      // 检查死链
    })->middleware('auth');

    // 搜索相关路由（需要认证）
    Route::group('search', function () {
        Route::get('', [Search::class, 'index']);                  // 全文搜索
        Route::post('filter', [Search::class, 'filter']);          // 高级筛选
    })->middleware('auth');

    // 智能文件夹路由（需要认证）
    Route::group('smart-folders', function () {
        Route::get('', [SmartFolder::class, 'index']);             // 获取智能文件夹列表
        Route::post('', [SmartFolder::class, 'create']);           // 创建智能文件夹
        Route::get(':id/bookmarks', [SmartFolder::class, 'bookmarks']); // 获取智能文件夹内容
        Route::put(':id', [SmartFolder::class, 'update']);         // 更新智能文件夹
        Route::delete(':id', [SmartFolder::class, 'delete']);      // 删除智能文件夹
    })->middleware('auth');

    // 导入导出路由（需要认证）
    Route::group('', function () {
        Route::post('import', [Import::class, 'import']);           // 导入书签
        Route::post('export', [Export::class, 'export']);          // 导出书签
        Route::get('jobs/:id', [Job::class, 'status']);            // 获取任务状态
    })->middleware('auth');

    // 统计分析路由（需要认证）
    Route::group('stats', function () {
        Route::get('overview', [Stats::class, 'overview']);         // 统计概览
        Route::get('tags', [Stats::class, 'tags']);                // 标签统计
        Route::get('visits', [Stats::class, 'visits']);            // 访问统计
        Route::post('report', [Stats::class, 'report']);           // 生成报告
    })->middleware('auth');

    // 回收站路由（需要认证）
    Route::group('trash', function () {
        Route::get('', [Trash::class, 'index']);                   // 获取回收站列表
        Route::post(':id/restore', [Trash::class, 'restore']);     // 恢复项目
        Route::delete(':id', [Trash::class, 'destroy']);           // 永久删除
        Route::delete('clear', [Trash::class, 'clear']);           // 清空回收站
    })->middleware('auth');

    // 系统配置路由
    Route::group('system', function () {
        Route::get('config', 'app\\controller\\api\\v1\\System@config');           // 获取系统配置（公开）
        Route::get('status', 'app\\controller\\api\\v1\\System@status');           // 获取系统状态（公开）
    });



});

// 兼容旧版本路由
Route::get('think', function () {
    return 'hello,ThinkPHP8!';
});

Route::get('hello/:name', [\app\controller\Index::class, 'hello']);
