<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书签管理系统 API 测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        button {
            padding: 10px 20px;
            margin: 8px 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        input, textarea {
            padding: 10px;
            width: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin: 5px;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        .result {
            margin-top: 15px;
            min-height: 50px;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #f5c6cb;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #c3e6cb;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        .status-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #343a40;
            color: white;
            padding: 10px;
            text-align: center;
            z-index: 1000;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .token-display {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="status-bar" id="statusBar">
        API服务器状态: <span id="serverStatus">检测中...</span> | Token状态: <span id="tokenStatus">未登录</span>
    </div>

    <div class="container" style="margin-top: 60px;">
        <h1>📚 书签管理系统 API 测试工具</h1>
        <p>这是一个用于测试书签管理系统API接口的工具页面。请确保后端服务器正在运行。</p>

        <div class="section">
            <h2>🔧 系统配置</h2>
            <div class="form-group">
                <label for="apiBaseUrl">API基础URL:</label>
                <input type="text" id="apiBaseUrl" value="http://localhost:8000" placeholder="输入API基础URL">
                <button class="btn-warning" onclick="updateApiBase()">更新配置</button>
                <button class="btn-primary" onclick="checkServerStatus()">检测服务器</button>
            </div>
            <div id="serverCheckResult" class="result"></div>
        </div>

        <div class="section">
            <h2>👤 用户注册</h2>
            <div class="form-group">
                <label for="regUsername">用户名:</label>
                <input type="text" id="regUsername" placeholder="输入用户名（3-20个字符）">
            </div>
            <div class="form-group">
                <label for="regEmail">邮箱:</label>
                <input type="email" id="regEmail" placeholder="输入邮箱地址">
            </div>
            <div class="form-group">
                <label for="regPassword">密码:</label>
                <input type="password" id="regPassword" placeholder="输入密码（6-20个字符）">
            </div>
            <button class="btn-primary" onclick="register()">注册用户</button>
            <button class="btn-warning" onclick="generateTestUser()">生成测试用户</button>
            <div id="registerResult" class="result"></div>
        </div>

        <div class="section">
            <h2>🔐 用户登录</h2>
            <div class="form-group">
                <label for="loginUsername">用户名:</label>
                <input type="text" id="loginUsername" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label for="loginPassword">密码:</label>
                <input type="password" id="loginPassword" placeholder="输入密码">
            </div>
            <button class="btn-success" onclick="login()">登录</button>
            <button class="btn-warning" onclick="useTestCredentials()">使用测试账号</button>
            <div id="loginResult" class="result"></div>
            <div id="tokenDisplay" class="token-display" style="display: none;"></div>
        </div>

        <div class="section">
            <h2>🤖 AI解析URL</h2>
            <div class="form-group">
                <label for="urlInput">要解析的URL:</label>
                <input type="text" id="urlInput" value="https://www.faxianai.com/" placeholder="输入要解析的URL">
            </div>
            <button class="btn-primary" onclick="parseUrl()">解析URL</button>
            <button class="btn-warning" onclick="clearParseResult()">清除结果</button>
            <button class="btn-success" onclick="testCommonUrls()">测试常用URL</button>
            <div id="parseResult" class="result"></div>
        </div>

        <div class="section">
            <h2>🔍 其他API测试</h2>
            <button class="btn-primary" onclick="testSystemStatus()">测试系统状态</button>
            <button class="btn-primary" onclick="testUserInfo()">获取用户信息</button>
            <button class="btn-warning" onclick="testLogout()">退出登录</button>
            <div id="otherApiResult" class="result"></div>
        </div>

        <div class="section">
            <h2>📊 测试报告</h2>
            <button class="btn-success" onclick="runFullTest()">运行完整测试</button>
            <button class="btn-warning" onclick="clearAllResults()">清除所有结果</button>
            <div id="testReport" class="result"></div>
        </div>

    <script>
        // 全局配置
        let config = {
            apiBaseUrl: 'http://localhost:8000',
            token: '',
            lastUser: null
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();
            checkServerStatus();
            updateTokenStatus();
        });

        // 保存和加载配置
        function saveConfig() {
            localStorage.setItem('bookmarkApiTestConfig', JSON.stringify(config));
        }

        function loadConfig() {
            const saved = localStorage.getItem('bookmarkApiTestConfig');
            if (saved) {
                config = { ...config, ...JSON.parse(saved) };
                document.getElementById('apiBaseUrl').value = config.apiBaseUrl;
                updateTokenStatus();
            }
        }

        // 更新API基础URL
        function updateApiBase() {
            const newUrl = document.getElementById('apiBaseUrl').value.trim();
            if (!newUrl) {
                showError('serverCheckResult', '请输入有效的API基础URL');
                return;
            }
            config.apiBaseUrl = newUrl;
            saveConfig();
            showSuccess('serverCheckResult', 'API基础URL已更新为: ' + newUrl);
            checkServerStatus();
        }

        // 检测服务器状态
        async function checkServerStatus() {
            const statusElement = document.getElementById('serverStatus');
            const resultElement = document.getElementById('serverCheckResult');

            statusElement.textContent = '检测中...';
            statusElement.style.color = '#ffc107';

            try {
                const response = await fetch(config.apiBaseUrl + '/api/v1/system/status', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: 5000
                });

                if (response.ok) {
                    const data = await response.json();
                    statusElement.textContent = '在线';
                    statusElement.style.color = '#28a745';
                    showSuccess('serverCheckResult', '服务器连接正常: ' + JSON.stringify(data, null, 2));
                } else {
                    throw new Error('HTTP ' + response.status);
                }
            } catch (error) {
                statusElement.textContent = '离线';
                statusElement.style.color = '#dc3545';
                showError('serverCheckResult', '服务器连接失败: ' + error.message +
                    '<br><br>请检查：<br>1. 后端服务器是否启动<br>2. API基础URL是否正确<br>3. 网络连接是否正常');
            }
        }

        // 更新Token状态显示
        function updateTokenStatus() {
            const tokenStatusElement = document.getElementById('tokenStatus');
            const tokenDisplayElement = document.getElementById('tokenDisplay');

            if (config.token) {
                tokenStatusElement.textContent = '已登录';
                tokenStatusElement.style.color = '#28a745';
                tokenDisplayElement.style.display = 'block';
                tokenDisplayElement.innerHTML = '<strong>当前Token:</strong><br>' + config.token;
            } else {
                tokenStatusElement.textContent = '未登录';
                tokenStatusElement.style.color = '#dc3545';
                tokenDisplayElement.style.display = 'none';
            }
        }

        // 生成测试用户数据
        function generateTestUser() {
            const timestamp = Date.now();
            document.getElementById('regUsername').value = 'test_' + timestamp;
            document.getElementById('regEmail').value = 'test_' + timestamp + '@example.com';
            document.getElementById('regPassword').value = '123456';
            showSuccess('registerResult', '已生成测试用户数据，请点击注册按钮');
        }

        // 使用测试账号登录
        function useTestCredentials() {
            if (config.lastUser) {
                document.getElementById('loginUsername').value = config.lastUser.username;
                document.getElementById('loginPassword').value = '123456';
                showSuccess('loginResult', '已填入最后注册的测试账号信息');
            } else {
                document.getElementById('loginUsername').value = 'test';
                document.getElementById('loginPassword').value = '123456';
                showSuccess('loginResult', '已填入默认测试账号信息');
            }
        }

        // 用户注册
        async function register() {
            const username = document.getElementById('regUsername').value.trim();
            const email = document.getElementById('regEmail').value.trim();
            const password = document.getElementById('regPassword').value.trim();

            if (!username || !email || !password) {
                showError('registerResult', '请填写完整的注册信息');
                return;
            }

            if (username.length < 3 || username.length > 20) {
                showError('registerResult', '用户名长度必须在3-20个字符之间');
                return;
            }

            if (password.length < 6 || password.length > 20) {
                showError('registerResult', '密码长度必须在6-20个字符之间');
                return;
            }

            try {
                showLoading('registerResult', '正在注册用户...');

                const response = await fetch(config.apiBaseUrl + '/api/v1/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        email: email,
                        password: password,
                        password_confirm: password
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 0) {
                    config.lastUser = { username, email };
                    if (data.data && data.data.token) {
                        config.token = data.data.token;
                        saveConfig();
                        updateTokenStatus();
                    }
                    showSuccess('registerResult', '注册成功！<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                } else {
                    showError('registerResult', '注册失败: ' + (data.message || '未知错误') +
                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                }
            } catch (error) {
                showError('registerResult', '注册请求失败: ' + error.message);
            }
        }

        // 用户登录
        async function login() {
            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value.trim();

            if (!username || !password) {
                showError('loginResult', '请输入用户名和密码');
                return;
            }

            try {
                showLoading('loginResult', '正在登录...');

                const response = await fetch(config.apiBaseUrl + '/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 0) {
                    if (data.data && data.data.token) {
                        config.token = data.data.token;
                        saveConfig();
                        updateTokenStatus();
                    }
                    showSuccess('loginResult', '登录成功！<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                } else {
                    showError('loginResult', '登录失败: ' + (data.message || '未知错误') +
                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                }
            } catch (error) {
                showError('loginResult', '登录请求失败: ' + error.message);
            }
        }

        // AI解析URL
        async function parseUrl() {
            if (!config.token) {
                showError('parseResult', '请先注册或登录获取Token');
                return;
            }

            const url = document.getElementById('urlInput').value.trim();
            if (!url) {
                showError('parseResult', '请输入要解析的URL');
                return;
            }

            // 简单的URL格式验证
            try {
                new URL(url);
            } catch (e) {
                showError('parseResult', '请输入有效的URL格式');
                return;
            }

            try {
                showLoading('parseResult', '正在使用AI解析URL，请稍候...');

                const response = await fetch(config.apiBaseUrl + '/api/v1/bookmarks/parse', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + config.token
                    },
                    body: JSON.stringify({
                        url: url
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 0) {
                    let resultHtml = '<div class="success">AI解析成功！</div>';
                    resultHtml += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';

                    // 特别显示解析结果
                    if (data.data) {
                        resultHtml += '<div style="background: #e7f3ff; padding: 15px; margin-top: 15px; border-radius: 4px; border-left: 4px solid #007bff;">';
                        resultHtml += '<h4 style="margin-top: 0; color: #007bff;">📊 解析结果摘要</h4>';

                        if (data.data.title) {
                            resultHtml += '<p><strong>标题:</strong> ' + data.data.title + '</p>';
                        }

                        if (data.data.summary) {
                            resultHtml += '<p><strong>摘要:</strong> ' + data.data.summary + '</p>';
                        }

                        if (data.data.tags && data.data.tags.length > 0) {
                            resultHtml += '<p><strong>建议标签:</strong> ';
                            data.data.tags.forEach(tag => {
                                resultHtml += '<span style="background: #007bff; color: white; padding: 2px 8px; margin: 2px; border-radius: 12px; font-size: 12px;">' + tag + '</span> ';
                            });
                            resultHtml += '</p>';
                        }

                        resultHtml += '</div>';
                    }

                    document.getElementById('parseResult').innerHTML = resultHtml;
                } else {
                    showError('parseResult', 'AI解析失败: ' + (data.message || '未知错误') +
                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                }
            } catch (error) {
                showError('parseResult', 'AI解析请求失败: ' + error.message);
            }
        }

        // 清除解析结果
        function clearParseResult() {
            document.getElementById('parseResult').innerHTML = '';
        }

        // 工具函数：显示加载状态
        function showLoading(elementId, message) {
            document.getElementById(elementId).innerHTML =
                '<div class="loading">⏳ ' + message + '</div>';
        }

        // 工具函数：显示成功信息
        function showSuccess(elementId, message) {
            document.getElementById(elementId).innerHTML =
                '<div class="success">✅ ' + message + '</div>';
        }

        // 工具函数：显示错误信息
        function showError(elementId, message) {
            document.getElementById(elementId).innerHTML =
                '<div class="error">❌ ' + message + '</div>';
        }

        // 测试常用URL
        async function testCommonUrls() {
            const testUrls = [
                'https://www.github.com',
                'https://www.baidu.com',
                'https://www.zhihu.com'
            ];

            showLoading('parseResult', '正在测试多个URL...');
            let results = [];

            for (let i = 0; i < testUrls.length; i++) {
                const url = testUrls[i];
                try {
                    const response = await fetch(config.apiBaseUrl + '/api/v1/bookmarks/parse', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + config.token
                        },
                        body: JSON.stringify({ url: url })
                    });

                    const data = await response.json();
                    results.push({
                        url: url,
                        success: response.ok && data.code === 0,
                        data: data
                    });
                } catch (error) {
                    results.push({
                        url: url,
                        success: false,
                        error: error.message
                    });
                }
            }

            // 显示测试结果
            let resultHtml = '<div class="success">批量URL测试完成</div>';
            results.forEach((result, index) => {
                const status = result.success ? '✅' : '❌';
                resultHtml += `<div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">`;
                resultHtml += `<strong>${status} ${result.url}</strong><br>`;
                if (result.success && result.data.data) {
                    resultHtml += `标题: ${result.data.data.title || '未获取'}<br>`;
                    if (result.data.data.tags) {
                        resultHtml += `标签: ${result.data.data.tags.join(', ')}<br>`;
                    }
                } else {
                    resultHtml += `错误: ${result.error || result.data.message}<br>`;
                }
                resultHtml += '</div>';
            });

            document.getElementById('parseResult').innerHTML = resultHtml;
        }

        // 测试系统状态
        async function testSystemStatus() {
            try {
                showLoading('otherApiResult', '正在检查系统状态...');

                const response = await fetch(config.apiBaseUrl + '/api/v1/system/status', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showSuccess('otherApiResult', '系统状态正常<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                } else {
                    showError('otherApiResult', '系统状态检查失败<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                }
            } catch (error) {
                showError('otherApiResult', '系统状态检查请求失败: ' + error.message);
            }
        }

        // 获取用户信息（需要实现相应的API）
        async function testUserInfo() {
            if (!config.token) {
                showError('otherApiResult', '请先登录获取Token');
                return;
            }

            try {
                showLoading('otherApiResult', '正在获取用户信息...');

                const response = await fetch(config.apiBaseUrl + '/api/v1/user/profile', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + config.token
                    }
                });

                const data = await response.json();

                if (response.ok && data.code === 0) {
                    showSuccess('otherApiResult', '用户信息获取成功<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                } else {
                    showError('otherApiResult', '用户信息获取失败: ' + (data.message || '未知错误') +
                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                }
            } catch (error) {
                showError('otherApiResult', '用户信息获取请求失败: ' + error.message);
            }
        }

        // 退出登录
        async function testLogout() {
            if (!config.token) {
                showError('otherApiResult', '当前未登录');
                return;
            }

            try {
                showLoading('otherApiResult', '正在退出登录...');

                const response = await fetch(config.apiBaseUrl + '/api/v1/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + config.token
                    }
                });

                const data = await response.json();

                if (response.ok && data.code === 0) {
                    config.token = '';
                    saveConfig();
                    updateTokenStatus();
                    showSuccess('otherApiResult', '退出登录成功<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                } else {
                    showError('otherApiResult', '退出登录失败: ' + (data.message || '未知错误') +
                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                }
            } catch (error) {
                showError('otherApiResult', '退出登录请求失败: ' + error.message);
            }
        }

        // 运行完整测试
        async function runFullTest() {
            showLoading('testReport', '正在运行完整测试套件...');

            const testResults = [];

            // 测试1: 系统状态
            try {
                const response = await fetch(config.apiBaseUrl + '/api/v1/system/status');
                const data = await response.json();
                testResults.push({
                    name: '系统状态检查',
                    success: response.ok && data.code === 0,
                    message: data.message || '系统正常'
                });
            } catch (error) {
                testResults.push({
                    name: '系统状态检查',
                    success: false,
                    message: error.message
                });
            }

            // 测试2: 用户注册（如果没有token）
            if (!config.token) {
                try {
                    const testUser = {
                        username: 'autotest_' + Date.now(),
                        email: 'autotest_' + Date.now() + '@example.com',
                        password: '123456',
                        password_confirm: '123456'
                    };

                    const response = await fetch(config.apiBaseUrl + '/api/v1/auth/register', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testUser)
                    });

                    const data = await response.json();
                    const success = response.ok && data.code === 0;

                    if (success && data.data && data.data.token) {
                        config.token = data.data.token;
                        saveConfig();
                        updateTokenStatus();
                    }

                    testResults.push({
                        name: '用户注册',
                        success: success,
                        message: data.message || '注册成功'
                    });
                } catch (error) {
                    testResults.push({
                        name: '用户注册',
                        success: false,
                        message: error.message
                    });
                }
            }

            // 测试3: AI解析URL
            if (config.token) {
                try {
                    const response = await fetch(config.apiBaseUrl + '/api/v1/bookmarks/parse', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + config.token
                        },
                        body: JSON.stringify({ url: 'https://www.github.com' })
                    });

                    const data = await response.json();
                    testResults.push({
                        name: 'AI解析URL',
                        success: response.ok && data.code === 0,
                        message: data.message || 'AI解析成功'
                    });
                } catch (error) {
                    testResults.push({
                        name: 'AI解析URL',
                        success: false,
                        message: error.message
                    });
                }
            }

            // 生成测试报告
            let reportHtml = '<div class="success">🎯 完整测试报告</div>';
            reportHtml += '<div style="margin: 15px 0;">';

            const successCount = testResults.filter(r => r.success).length;
            const totalCount = testResults.length;

            reportHtml += `<p><strong>测试概览:</strong> ${successCount}/${totalCount} 项测试通过</p>`;

            testResults.forEach(result => {
                const status = result.success ? '✅' : '❌';
                const bgColor = result.success ? '#d4edda' : '#f8d7da';
                reportHtml += `<div style="background: ${bgColor}; padding: 10px; margin: 5px 0; border-radius: 4px;">`;
                reportHtml += `${status} <strong>${result.name}:</strong> ${result.message}`;
                reportHtml += '</div>';
            });

            reportHtml += '</div>';
            document.getElementById('testReport').innerHTML = reportHtml;
        }

        // 清除所有结果
        function clearAllResults() {
            document.getElementById('registerResult').innerHTML = '';
            document.getElementById('loginResult').innerHTML = '';
            document.getElementById('parseResult').innerHTML = '';
            document.getElementById('otherApiResult').innerHTML = '';
            document.getElementById('testReport').innerHTML = '';
            document.getElementById('serverCheckResult').innerHTML = '';
        }

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Ctrl+Enter 快速执行当前焦点元素相关的操作
            if (e.ctrlKey && e.key === 'Enter') {
                const activeElement = document.activeElement;
                if (activeElement.id === 'urlInput') {
                    parseUrl();
                } else if (activeElement.id === 'loginUsername' || activeElement.id === 'loginPassword') {
                    login();
                } else if (activeElement.id.startsWith('reg')) {
                    register();
                }
            }
        });
    </script>
</body>
</html>
