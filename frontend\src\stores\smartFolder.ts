import { defineStore } from 'pinia'
import type { SmartFolder, CreateSmartFolderRequest } from '@/types/api'
import { http } from '@/utils/request'

export const useSmartFolderStore = defineStore('smartFolder', () => {
  // 状态
  const smartFolders = ref<SmartFolder[]>([])
  const currentSmartFolder = ref<SmartFolder | null>(null)
  const loading = ref(false)

  // 获取智能文件夹列表
  const fetchSmartFolders = async () => {
    try {
      loading.value = true
      const response = await http.get('/smart-folders')
      
      smartFolders.value = response.data
      
      return response.data
    } catch (error) {
      console.error('获取智能文件夹列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取智能文件夹详情
  const fetchSmartFolder = async (id: number) => {
    try {
      const response = await http.get(`/smart-folders/${id}`)
      currentSmartFolder.value = response.data
      return response.data
    } catch (error) {
      console.error('获取智能文件夹详情失败:', error)
      throw error
    }
  }

  // 创建智能文件夹
  const createSmartFolder = async (data: CreateSmartFolderRequest) => {
    try {
      const response = await http.post('/smart-folders', data)
      const newSmartFolder = response.data
      
      // 添加到列表
      smartFolders.value.unshift(newSmartFolder)
      
      return newSmartFolder
    } catch (error) {
      console.error('创建智能文件夹失败:', error)
      throw error
    }
  }

  // 更新智能文件夹
  const updateSmartFolder = async (id: number, data: Partial<CreateSmartFolderRequest>) => {
    try {
      const response = await http.put(`/smart-folders/${id}`, data)
      const updatedSmartFolder = response.data
      
      // 更新列表中的智能文件夹
      const index = smartFolders.value.findIndex(folder => folder.id === id)
      if (index !== -1) {
        smartFolders.value[index] = updatedSmartFolder
      }
      
      // 更新当前智能文件夹
      if (currentSmartFolder.value?.id === id) {
        currentSmartFolder.value = updatedSmartFolder
      }
      
      return updatedSmartFolder
    } catch (error) {
      console.error('更新智能文件夹失败:', error)
      throw error
    }
  }

  // 删除智能文件夹
  const deleteSmartFolder = async (id: number) => {
    try {
      await http.delete(`/smart-folders/${id}`)
      
      // 从列表中移除
      smartFolders.value = smartFolders.value.filter(folder => folder.id !== id)
      
      // 清除当前智能文件夹
      if (currentSmartFolder.value?.id === id) {
        currentSmartFolder.value = null
      }
    } catch (error) {
      console.error('删除智能文件夹失败:', error)
      throw error
    }
  }

  // 复制智能文件夹
  const duplicateSmartFolder = async (id: number) => {
    try {
      const response = await http.post(`/smart-folders/${id}/duplicate`)
      const newSmartFolder = response.data
      
      // 添加到列表
      smartFolders.value.unshift(newSmartFolder)
      
      return newSmartFolder
    } catch (error) {
      console.error('复制智能文件夹失败:', error)
      throw error
    }
  }

  // 执行智能文件夹规则
  const executeSmartFolder = async (id: number) => {
    try {
      const response = await http.post(`/smart-folders/${id}/execute`)
      return response.data
    } catch (error) {
      console.error('执行智能文件夹规则失败:', error)
      throw error
    }
  }

  // 获取智能文件夹的书签
  const getSmartFolderBookmarks = async (id: number, params?: any) => {
    try {
      const response = await http.get(`/smart-folders/${id}/bookmarks`, { params })
      return response.data
    } catch (error) {
      console.error('获取智能文件夹书签失败:', error)
      throw error
    }
  }

  // 测试智能文件夹规则
  const testSmartFolderRules = async (rules: any[]) => {
    try {
      const response = await http.post('/smart-folders/test-rules', { rules })
      return response.data
    } catch (error) {
      console.error('测试智能文件夹规则失败:', error)
      throw error
    }
  }

  // 清空状态
  const clearSmartFolders = () => {
    smartFolders.value = []
    currentSmartFolder.value = null
  }

  return {
    // 状态
    smartFolders: readonly(smartFolders),
    currentSmartFolder: readonly(currentSmartFolder),
    loading: readonly(loading),
    
    // 方法
    fetchSmartFolders,
    fetchSmartFolder,
    createSmartFolder,
    updateSmartFolder,
    deleteSmartFolder,
    duplicateSmartFolder,
    executeSmartFolder,
    getSmartFolderBookmarks,
    testSmartFolderRules,
    clearSmartFolders
  }
})
