<?php

namespace app\exception;

use Exception;
use app\common\ErrorCode;

/**
 * 业务异常类
 */
class BusinessException extends Exception
{
    /**
     * 错误码
     * @var int
     */
    protected $errorCode;

    /**
     * 错误详情
     * @var mixed
     */
    protected $errors;

    /**
     * 构造函数
     * @param string $message 错误消息
     * @param int $errorCode 错误码
     * @param mixed $errors 错误详情
     * @param int $httpCode HTTP状态码
     */
    public function __construct(
        string $message = '', 
        int $errorCode = ErrorCode::BUSINESS_ERROR, 
        $errors = null, 
        int $httpCode = 400
    ) {
        $this->errorCode = $errorCode;
        $this->errors = $errors;
        
        // 如果没有提供消息，使用错误码对应的默认消息
        if (empty($message)) {
            $message = ErrorCode::getMessage($errorCode);
        }
        
        parent::__construct($message, $httpCode);
    }

    /**
     * 获取错误码
     * @return int
     */
    public function getErrorCode(): int
    {
        return $this->errorCode;
    }

    /**
     * 获取错误详情
     * @return mixed
     */
    public function getErrors()
    {
        return $this->errors;
    }

    /**
     * 创建用户相关异常
     */
    public static function userEmailExists(): self
    {
        return new self('', ErrorCode::USER_EMAIL_EXISTS);
    }

    public static function userUsernameExists(): self
    {
        return new self('', ErrorCode::USER_USERNAME_EXISTS);
    }

    public static function userLoginFailed(): self
    {
        return new self('', ErrorCode::USER_LOGIN_FAILED, null, 401);
    }

    public static function userNotFound(): self
    {
        return new self('', ErrorCode::USER_NOT_FOUND, null, 404);
    }

    public static function userDisabled(): self
    {
        return new self('', ErrorCode::USER_DISABLED, null, 403);
    }

    /**
     * 创建文件夹相关异常
     */
    public static function folderNameExists(): self
    {
        return new self('', ErrorCode::FOLDER_NAME_EXISTS);
    }

    public static function folderNotFound(): self
    {
        return new self('', ErrorCode::FOLDER_NOT_FOUND, null, 404);
    }

    public static function folderMoveInvalid(): self
    {
        return new self('', ErrorCode::FOLDER_MOVE_INVALID);
    }

    /**
     * 创建书签相关异常
     */
    public static function bookmarkUrlInvalid(): self
    {
        return new self('', ErrorCode::BOOKMARK_URL_INVALID);
    }

    public static function bookmarkExists(): self
    {
        return new self('', ErrorCode::BOOKMARK_EXISTS);
    }

    public static function bookmarkNotFound(): self
    {
        return new self('', ErrorCode::BOOKMARK_NOT_FOUND, null, 404);
    }

    /**
     * 创建标签相关异常
     */
    public static function tagNameExists(): self
    {
        return new self('', ErrorCode::TAG_NAME_EXISTS);
    }

    public static function tagNotFound(): self
    {
        return new self('', ErrorCode::TAG_NOT_FOUND, null, 404);
    }

    /**
     * 创建通用异常
     */
    public static function paramError(string $message = '', $errors = null): self
    {
        return new self($message ?: ErrorCode::getMessage(ErrorCode::PARAM_ERROR), ErrorCode::PARAM_ERROR, $errors);
    }

    public static function unauthorized(string $message = ''): self
    {
        return new self($message ?: ErrorCode::getMessage(ErrorCode::UNAUTHORIZED), ErrorCode::UNAUTHORIZED, null, 401);
    }

    public static function forbidden(string $message = ''): self
    {
        return new self($message ?: ErrorCode::getMessage(ErrorCode::FORBIDDEN), ErrorCode::FORBIDDEN, null, 403);
    }

    public static function notFound(string $message = ''): self
    {
        return new self($message ?: ErrorCode::getMessage(ErrorCode::NOT_FOUND), ErrorCode::NOT_FOUND, null, 404);
    }
}
