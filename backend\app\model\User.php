<?php

namespace app\model;

use think\Model;
use think\model\concern\SoftDelete;

/**
 * 用户模型
 */
class User extends Model
{
    // 表名
    protected $name = 'users';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'status' => 'integer',
        'last_login_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // 隐藏字段
    protected $hidden = [
        'password'
    ];

    // JSON字段
    protected $json = [];

    // 字段自动完成
    protected $auto = [];

    // 新增自动完成
    protected $insert = [];

    // 更新自动完成
    protected $update = [];

    /**
     * 密码修改器 - 自动加密密码
     * @param string $value
     * @return string
     */
    public function setPasswordAttr(string $value): string
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }

    /**
     * 状态获取器 - 返回状态文本
     * @param int $value
     * @return array
     */
    public function getStatusTextAttr(int $value): array
    {
        $status = [
            0 => '禁用',
            1 => '正常'
        ];
        
        return [
            'value' => $value,
            'text' => $status[$value] ?? '未知'
        ];
    }

    /**
     * 头像获取器 - 处理头像URL
     * @param string|null $value
     * @return string|null
     */
    public function getAvatarAttr(?string $value): ?string
    {
        if (empty($value)) {
            return null;
        }

        // 如果是完整URL，直接返回
        if (strpos($value, 'http') === 0) {
            return $value;
        }

        // 相对路径，拼接域名
        return request()->domain() . $value;
    }

    /**
     * 验证密码
     * @param string $password 明文密码
     * @return bool
     */
    public function verifyPassword(string $password): bool
    {
        return password_verify($password, $this->getData('password'));
    }

    /**
     * 检查邮箱是否已存在
     * @param string $email
     * @param int|null $excludeId 排除的用户ID
     * @return bool
     */
    public static function emailExists(string $email, ?int $excludeId = null): bool
    {
        $query = self::where('email', $email);
        
        if ($excludeId) {
            $query->where('id', '<>', $excludeId);
        }
        
        return $query->count() > 0;
    }

    /**
     * 检查用户名是否已存在
     * @param string $username
     * @param int|null $excludeId 排除的用户ID
     * @return bool
     */
    public static function usernameExists(string $username, ?int $excludeId = null): bool
    {
        $query = self::where('username', $username);
        
        if ($excludeId) {
            $query->where('id', '<>', $excludeId);
        }
        
        return $query->count() > 0;
    }

    /**
     * 根据邮箱或用户名查找用户
     * @param string $account 邮箱或用户名
     * @return User|null
     */
    public static function findByAccount(string $account): ?User
    {
        return self::where('email', $account)
            ->whereOr('username', $account)
            ->where('status', 1)
            ->find();
    }

    /**
     * 更新最后登录信息
     * @param string|null $ip 登录IP
     * @return bool
     */
    public function updateLastLogin(?string $ip = null): bool
    {
        $this->last_login_at = date('Y-m-d H:i:s');
        $this->last_login_ip = $ip ?: request()->ip();
        
        return $this->save();
    }

    /**
     * 获取用户统计信息
     * @return array
     */
    public function getStats(): array
    {
        return [
            'bookmark_count' => $this->bookmarks()->count(),
            'folder_count' => $this->folders()->count(),
            'tag_count' => $this->tags()->count(),
        ];
    }

    /**
     * 关联书签
     * @return \think\model\relation\HasMany
     */
    public function bookmarks()
    {
        return $this->hasMany(Bookmark::class, 'user_id');
    }

    /**
     * 关联文件夹
     * @return \think\model\relation\HasMany
     */
    public function folders()
    {
        return $this->hasMany(Folder::class, 'user_id');
    }

    /**
     * 关联标签
     * @return \think\model\relation\HasMany
     */
    public function tags()
    {
        return $this->hasMany(Tag::class, 'user_id');
    }

    /**
     * 关联认证令牌
     * @return \think\model\relation\HasMany
     */
    public function tokens()
    {
        return $this->hasMany(UserToken::class, 'user_id');
    }

    /**
     * 关联用户偏好设置
     * @return \think\model\relation\HasOne
     */
    public function preference()
    {
        return $this->hasOne(UserPreference::class, 'user_id');
    }

    /**
     * 获取用户的有效刷新令牌
     * @return \think\model\relation\HasMany
     */
    public function validTokens()
    {
        return $this->hasMany(UserToken::class, 'user_id')
            ->where('expires_at', '>', date('Y-m-d H:i:s'));
    }

    /**
     * 清理过期的令牌
     * @return int 清理的数量
     */
    public function cleanExpiredTokens(): int
    {
        return $this->tokens()
            ->where('expires_at', '<=', date('Y-m-d H:i:s'))
            ->delete();
    }

    /**
     * 转换为API输出格式
     * @param bool $withStats 是否包含统计信息
     * @return array
     */
    public function toApiArray(bool $withStats = false): array
    {
        $data = [
            'id' => $this->id,
            'email' => $this->email,
            'username' => $this->username,
            'nickname' => $this->nickname ?? $this->username,
            'avatar' => $this->avatar,
            'status' => $this->status,
            'last_login_at' => $this->formatDateTime($this->last_login_at),
            'created_at' => $this->formatDateTime($this->created_at),
        ];

        if ($withStats) {
            $data['stats'] = $this->getStats();
        }

        return $data;
    }

    /**
     * 安全地格式化日期时间
     * @param mixed $datetime 日期时间对象或字符串
     * @return string|null 格式化后的日期时间字符串或null
     */
    private function formatDateTime($datetime): ?string
    {
        if (empty($datetime)) {
            return null;
        }

        // 如果是字符串，尝试转换为DateTime对象
        if (is_string($datetime)) {
            try {
                $datetime = new \DateTime($datetime);
            } catch (\Exception $e) {
                return null;
            }
        }

        // 如果是DateTime对象，格式化输出
        if ($datetime instanceof \DateTime || $datetime instanceof \DateTimeInterface) {
            return $datetime->format('Y-m-d H:i:s');
        }

        return null;
    }
}
