# Web端书签管理系统 - 数据字典

## 1. 概述

本数据字典详细定义了Web端书签管理系统中所有数据表的字段规范，包括字段的业务含义、数据类型、约束条件、取值范围等信息。

## 2. 数据类型说明

| MySQL类型 | 说明 | 使用场景 |
|----------|------|---------|
| BIGINT UNSIGNED | 无符号大整数(0 ~ 2^64-1) | 主键、外键ID |
| INT UNSIGNED | 无符号整数(0 ~ 2^32-1) | 计数器、排序字段 |
| TINYINT | 小整数(-128 ~ 127) | 状态标志、布尔值 |
| VARCHAR(n) | 可变长字符串 | 标题、名称、URL等 |
| TEXT | 长文本 | 描述、摘要等 |
| JSON | JSON格式数据 | 结构化配置、条件等 |
| TIMESTAMP | 时间戳 | 日期时间记录 |

## 3. 通用字段说明

### 3.1 主键字段
- **字段名**: `id`
- **类型**: BIGINT UNSIGNED AUTO_INCREMENT
- **说明**: 所有表的主键统一命名为 id，使用自增长策略

### 3.2 时间戳字段
- **created_at**: 记录创建时间，默认 CURRENT_TIMESTAMP
- **updated_at**: 记录最后更新时间，默认 CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
- **deleted_at**: 软删除时间（如需要）

### 3.3 外键字段
- **命名规则**: `关联表名_id`（如 user_id, folder_id）
- **类型**: 与关联表主键类型一致
- **约束**: 通常设置 ON DELETE CASCADE 或 ON DELETE SET NULL

## 4. 数据表字段详解

### 4.1 用户表 (users)

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 | 取值范围/示例 |
|--------|----------|----------|--------|------|---------------|
| id | BIGINT UNSIGNED | 是 | AUTO_INCREMENT | 用户唯一标识 | 1, 2, 3... |
| email | VARCHAR(100) | 是 | - | 用户邮箱地址 | <EMAIL> |
| username | VARCHAR(50) | 是 | - | 用户名 | 字母、数字、下划线，3-50字符 |
| password | VARCHAR(255) | 是 | - | 密码（bcrypt加密） | $2y$12$... |
| avatar | VARCHAR(255) | 否 | NULL | 用户头像URL | /storage/avatars/xxx.jpg |
| status | TINYINT | 是 | 1 | 账户状态 | 0=禁用, 1=正常 |
| last_login_at | TIMESTAMP | 否 | NULL | 最后登录时间 | 2024-01-01 12:00:00 |
| last_login_ip | VARCHAR(45) | 否 | NULL | 最后登录IP | 支持IPv4/IPv6 |
| created_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 注册时间 | 自动生成 |
| updated_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 更新时间 | 自动更新 |

**业务规则**：
- email 必须符合邮箱格式，全局唯一
- username 仅允许字母、数字、下划线，长度3-50字符，全局唯一
- password 使用 bcrypt 算法加密，cost factor = 12
- avatar 支持 jpg/png/webp 格式，最大 2MB
- 用户状态为 0 时无法登录系统

### 4.2 用户认证令牌表 (user_tokens)

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 | 取值范围/示例 |
|--------|----------|----------|--------|------|---------------|
| id | BIGINT UNSIGNED | 是 | AUTO_INCREMENT | 令牌ID | 自增主键 |
| user_id | BIGINT UNSIGNED | 是 | - | 关联用户ID | 外键->users.id |
| token_type | VARCHAR(20) | 是 | refresh | 令牌类型 | 固定值: refresh |
| token | VARCHAR(500) | 是 | - | 令牌值 | JWT格式字符串 |
| expires_at | TIMESTAMP | 是 | - | 过期时间 | 创建时间+30天 |
| device_info | VARCHAR(255) | 否 | NULL | 设备信息 | 浏览器UA等信息 |
| created_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 创建时间 | 自动生成 |

**业务规则**：
- Refresh Token 有效期 30 天
- 每个用户可以有多个有效的 Refresh Token（支持多设备登录）
- Token 过期后需自动清理

### 4.3 文件夹表 (folders)

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 | 取值范围/示例 |
|--------|----------|----------|--------|------|---------------|
| id | BIGINT UNSIGNED | 是 | AUTO_INCREMENT | 文件夹ID | 自增主键 |
| user_id | BIGINT UNSIGNED | 是 | - | 所属用户ID | 外键->users.id |
| parent_id | BIGINT UNSIGNED | 否 | NULL | 父文件夹ID | 外键->folders.id |
| name | VARCHAR(100) | 是 | - | 文件夹名称 | 1-100字符 |
| icon | VARCHAR(50) | 否 | NULL | 图标 | emoji或图标代码 |
| color | VARCHAR(7) | 否 | NULL | 颜色代码 | #FF5733 |
| sort_order | INT | 是 | 0 | 排序顺序 | 整数，越小越靠前 |
| path | VARCHAR(500) | 是 | '' | 文件夹路径 | /1/2/3/ |
| level | TINYINT | 是 | 1 | 层级深度 | 1-10（建议限制） |
| created_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 创建时间 | 自动生成 |
| updated_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 更新时间 | 自动更新 |

**业务规则**：
- parent_id 为 NULL 表示根目录下的文件夹
- path 字段自动维护，格式为 `/祖先id/父id/当前id/`
- level 字段自动计算，根目录为 1
- 删除文件夹时，子文件夹级联删除
- icon 支持 Unicode Emoji 或预定义图标代码

### 4.4 书签表 (bookmarks)

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 | 取值范围/示例 |
|--------|----------|----------|--------|------|---------------|
| id | BIGINT UNSIGNED | 是 | AUTO_INCREMENT | 书签ID | 自增主键 |
| user_id | BIGINT UNSIGNED | 是 | - | 所属用户ID | 外键->users.id |
| folder_id | BIGINT UNSIGNED | 否 | NULL | 所属文件夹ID | 外键->folders.id |
| title | VARCHAR(255) | 是 | - | 书签标题 | 1-255字符 |
| url | VARCHAR(2000) | 是 | - | URL地址 | http(s)://... |
| description | TEXT | 否 | NULL | 描述信息 | 不限长度 |
| favicon | VARCHAR(500) | 否 | NULL | 网站图标URL | 自动抓取 |
| sort_order | INT | 是 | 0 | 排序顺序 | 整数值 |
| is_star | TINYINT | 是 | 0 | 是否星标 | 0=否, 1=是 |
| visit_count | INT UNSIGNED | 是 | 0 | 访问次数 | >=0 |
| last_visit_at | TIMESTAMP | 否 | NULL | 最后访问时间 | 点击书签时更新 |
| is_dead | TINYINT | 是 | 0 | 是否死链 | 0=正常, 1=死链 |
| last_check_at | TIMESTAMP | 否 | NULL | 最后检测时间 | 死链检测时间 |
| created_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 创建时间 | 自动生成 |
| updated_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 更新时间 | 自动更新 |

**业务规则**：
- folder_id 为 NULL 表示在根目录
- url 必须是有效的 HTTP/HTTPS URL
- favicon 自动从网站抓取，失败则为 NULL
- visit_count 每次访问自增 1
- is_dead 通过定期检测任务更新

### 4.5 标签表 (tags)

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 | 取值范围/示例 |
|--------|----------|----------|--------|------|---------------|
| id | BIGINT UNSIGNED | 是 | AUTO_INCREMENT | 标签ID | 自增主键 |
| user_id | BIGINT UNSIGNED | 是 | - | 所属用户ID | 外键->users.id |
| name | VARCHAR(50) | 是 | - | 标签名称 | 1-50字符 |
| color | VARCHAR(7) | 否 | NULL | 标签颜色 | #FF5733 |
| usage_count | INT UNSIGNED | 是 | 0 | 使用次数 | >=0 |
| created_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 创建时间 | 自动生成 |
| updated_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 更新时间 | 自动更新 |

**业务规则**：
- 同一用户下标签名称唯一
- usage_count 自动维护，表示关联的书签数量
- 标签删除时，相关关联自动清除
- 标签名称不区分大小写

### 4.6 书签标签关联表 (bookmark_tags)

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 | 取值范围/示例 |
|--------|----------|----------|--------|------|---------------|
| id | BIGINT UNSIGNED | 是 | AUTO_INCREMENT | 关联ID | 自增主键 |
| bookmark_id | BIGINT UNSIGNED | 是 | - | 书签ID | 外键->bookmarks.id |
| tag_id | BIGINT UNSIGNED | 是 | - | 标签ID | 外键->tags.id |
| created_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 创建时间 | 自动生成 |

**业务规则**：
- bookmark_id + tag_id 组合唯一
- 删除书签或标签时，关联记录自动删除
- 添加/删除关联时，需更新标签的 usage_count

### 4.7 AI解析结果表 (ai_parse_results)

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 | 取值范围/示例 |
|--------|----------|----------|--------|------|---------------|
| id | BIGINT UNSIGNED | 是 | AUTO_INCREMENT | 解析结果ID | 自增主键 |
| bookmark_id | BIGINT UNSIGNED | 是 | - | 书签ID | 外键->bookmarks.id |
| summary | TEXT | 否 | NULL | AI生成摘要 | 200-500字 |
| keywords | JSON | 否 | NULL | 关键词列表 | ["关键词1", "关键词2"] |
| content_type | VARCHAR(50) | 否 | NULL | 内容类型 | article/video/code/news |
| parse_status | TINYINT | 是 | 0 | 解析状态 | 0=待解析,1=解析中,2=成功,3=失败 |
| error_message | TEXT | 否 | NULL | 错误信息 | 解析失败原因 |
| parsed_at | TIMESTAMP | 否 | NULL | 解析时间 | 解析完成时间 |
| created_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 创建时间 | 自动生成 |
| updated_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 更新时间 | 自动更新 |

**业务规则**：
- 每个书签只有一条解析结果
- keywords 存储为 JSON 数组，建议 3-10 个关键词
- content_type 通过 AI 分析得出
- 失败后可重试，最多重试 3 次

### 4.8 智能文件夹表 (smart_folders)

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 | 取值范围/示例 |
|--------|----------|----------|--------|------|---------------|
| id | BIGINT UNSIGNED | 是 | AUTO_INCREMENT | 智能文件夹ID | 自增主键 |
| user_id | BIGINT UNSIGNED | 是 | - | 所属用户ID | 外键->users.id |
| name | VARCHAR(100) | 是 | - | 文件夹名称 | 1-100字符 |
| icon | VARCHAR(50) | 否 | NULL | 图标 | emoji或图标代码 |
| conditions | JSON | 是 | - | 搜索条件 | 见下方JSON结构 |
| sort_order | INT | 是 | 0 | 排序顺序 | 整数值 |
| created_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 创建时间 | 自动生成 |
| updated_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 更新时间 | 自动更新 |

**conditions 字段JSON结构**：
```json
{
    "conditions": [
        {
            "field": "title|url|tags|created_at|visit_count|is_star|is_dead",
            "operator": "contains|equals|not_equals|starts_with|ends_with|greater_than|less_than|between|in|not_in",
            "value": "根据operator类型变化"
        }
    ],
    "logic": "AND|OR"
}
```

**业务规则**：
- 最多支持 10 个搜索条件
- 条件之间支持 AND/OR 逻辑关系
- 智能文件夹内容动态生成，不存储具体书签

### 4.9 回收站表 (trash)

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 | 取值范围/示例 |
|--------|----------|----------|--------|------|---------------|
| id | BIGINT UNSIGNED | 是 | AUTO_INCREMENT | 回收站ID | 自增主键 |
| user_id | BIGINT UNSIGNED | 是 | - | 所属用户ID | 外键->users.id |
| item_type | VARCHAR(20) | 是 | - | 项目类型 | bookmark/folder |
| item_id | BIGINT UNSIGNED | 是 | - | 原始项目ID | 被删除项的ID |
| item_data | JSON | 是 | - | 项目数据 | 完整的原始数据 |
| deleted_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 删除时间 | 自动生成 |
| expire_at | TIMESTAMP | 是 | +365天 | 过期时间 | 自动计算 |

**业务规则**：
- item_data 存储删除时的完整数据快照
- 保留期 365 天，过期自动清理
- 恢复时需检查原ID是否被占用
- 文件夹恢复时需递归恢复子项

### 4.10 操作日志表 (operation_logs)

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 | 取值范围/示例 |
|--------|----------|----------|--------|------|---------------|
| id | BIGINT UNSIGNED | 是 | AUTO_INCREMENT | 日志ID | 自增主键 |
| user_id | BIGINT UNSIGNED | 是 | - | 用户ID | 外键->users.id |
| operation | VARCHAR(50) | 是 | - | 操作类型 | login/logout/create/update/delete |
| module | VARCHAR(30) | 是 | - | 模块名称 | user/bookmark/folder/tag |
| content | JSON | 否 | NULL | 操作详情 | {"old": {}, "new": {}} |
| ip | VARCHAR(45) | 是 | - | IP地址 | 支持IPv4/IPv6 |
| user_agent | VARCHAR(255) | 否 | NULL | 浏览器UA | Mozilla/5.0... |
| created_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 操作时间 | 自动生成 |

**业务规则**：
- 记录关键操作：登录、修改密码、批量删除等
- content 字段记录操作前后的数据对比
- 日志保留 90 天
- 不记录查询操作，避免日志过多

### 4.11 系统配置表 (system_configs)

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 | 取值范围/示例 |
|--------|----------|----------|--------|------|---------------|
| id | INT UNSIGNED | 是 | AUTO_INCREMENT | 配置ID | 自增主键 |
| key | VARCHAR(100) | 是 | - | 配置键 | site.name |
| value | TEXT | 否 | NULL | 配置值 | 根据type而定 |
| type | VARCHAR(20) | 是 | string | 值类型 | string/number/boolean/json |
| description | VARCHAR(255) | 否 | NULL | 配置说明 | 网站名称 |
| created_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 创建时间 | 自动生成 |
| updated_at | TIMESTAMP | 是 | CURRENT_TIMESTAMP | 更新时间 | 自动更新 |

**常用配置项**：
| 配置键 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| site.name | string | 书签管理系统 | 网站名称 |
| site.logo | string | /assets/logo.png | 网站Logo |
| ai.provider | string | qwen | AI服务商 |
| ai.model | string | qwen-turbo | AI模型 |
| mail.driver | string | smtp | 邮件驱动 |
| backup.retain_days | number | 30 | 备份保留天数 |
| user.avatar_max_size | number | 2097152 | 头像最大字节数(2MB) |

### 4.12 队列任务表 (queue_jobs)

| 字段名 | 数据类型 | 是否必填 | 默认值 | 说明 | 取值范围/示例 |
|--------|----------|----------|--------|------|---------------|
| id | BIGINT UNSIGNED | 是 | AUTO_INCREMENT | 任务ID | 自增主键 |
| queue | VARCHAR(255) | 是 | - | 队列名称 | default |
| payload | LONGTEXT | 是 | - | 任务数据 | 序列化的任务信息 |
| attempts | TINYINT UNSIGNED | 是 | - | 尝试次数 | 0-255 |
| reserved_at | INT UNSIGNED | 否 | NULL | 保留时间 | Unix时间戳 |
| available_at | INT UNSIGNED | 是 | - | 可用时间 | Unix时间戳 |
| created_at | INT UNSIGNED | 是 | - | 创建时间 | Unix时间戳 |

**业务规则**：
- ThinkPHP Queue 框架表，通常使用 Redis 驱动
- 此表仅在使用数据库驱动时需要
- attempts 超过 3 次标记为失败

## 5. 枚举值定义

### 5.1 用户状态 (users.status)
| 值 | 含义 | 说明 |
|----|------|------|
| 0 | 禁用 | 无法登录系统 |
| 1 | 正常 | 可正常使用 |

### 5.2 书签星标状态 (bookmarks.is_star)
| 值 | 含义 | 说明 |
|----|------|------|
| 0 | 普通 | 普通书签 |
| 1 | 星标 | 重要书签 |

### 5.3 死链状态 (bookmarks.is_dead)
| 值 | 含义 | 说明 |
|----|------|------|
| 0 | 正常 | 链接可访问 |
| 1 | 死链 | 链接无法访问 |

### 5.4 AI解析状态 (ai_parse_results.parse_status)
| 值 | 含义 | 说明 |
|----|------|------|
| 0 | 待解析 | 等待处理 |
| 1 | 解析中 | 正在处理 |
| 2 | 成功 | 解析完成 |
| 3 | 失败 | 解析出错 |

### 5.5 内容类型 (ai_parse_results.content_type)
| 值 | 含义 | 说明 |
|----|------|------|
| article | 文章 | 博客、新闻类文章 |
| video | 视频 | 视频网站页面 |
| code | 代码 | GitHub等代码仓库 |
| news | 新闻 | 新闻资讯页面 |
| doc | 文档 | 技术文档、教程 |
| tool | 工具 | 在线工具网站 |
| other | 其他 | 无法分类的内容 |

### 5.6 项目类型 (trash.item_type)
| 值 | 含义 | 说明 |
|----|------|------|
| bookmark | 书签 | 删除的书签 |
| folder | 文件夹 | 删除的文件夹 |

### 5.7 操作类型 (operation_logs.operation)
| 值 | 含义 | 说明 |
|----|------|------|
| login | 登录 | 用户登录 |
| logout | 登出 | 用户登出 |
| create | 创建 | 创建资源 |
| update | 更新 | 更新资源 |
| delete | 删除 | 删除资源 |
| batch_delete | 批量删除 | 批量删除资源 |
| import | 导入 | 导入数据 |
| export | 导出 | 导出数据 |

### 5.8 模块名称 (operation_logs.module)
| 值 | 含义 | 说明 |
|----|------|------|
| user | 用户 | 用户相关操作 |
| bookmark | 书签 | 书签相关操作 |
| folder | 文件夹 | 文件夹相关操作 |
| tag | 标签 | 标签相关操作 |
| system | 系统 | 系统配置操作 |

## 6. 数据完整性规则

### 6.1 级联删除规则
- 删除用户时，该用户的所有数据（书签、文件夹、标签等）全部删除
- 删除文件夹时，子文件夹级联删除，书签的 folder_id 设为 NULL
- 删除书签时，相关的标签关联和AI解析结果自动删除
- 删除标签时，相关的书签关联自动删除

### 6.2 数据一致性维护
- 标签的 usage_count 需与实际关联数保持一致
- 文件夹的 path 和 level 需正确反映层级关系
- 书签的 visit_count 只增不减
- 回收站数据过期后需定时清理

### 6.3 并发控制
- 使用数据库事务保证数据一致性
- 批量操作限制为 300 条，避免长事务
- 更新操作使用乐观锁（基于 updated_at）

## 7. 性能优化建议

### 7.1 索引使用建议
- 高频查询字段都已建立索引
- 避免在已有索引字段上使用函数
- 定期分析慢查询日志优化索引

### 7.2 查询优化建议
- 大数据量查询必须分页
- 避免 SELECT *，明确指定需要的字段
- 使用 EXPLAIN 分析复杂查询

### 7.3 数据清理建议
- 定期清理过期的回收站数据
- 定期清理过期的操作日志
- 定期清理无效的认证令牌 