<?php
declare (strict_types = 1);

namespace app;

use think\App;
use think\exception\ValidateException;
use think\Validate;
use app\common\Response;
use app\common\ErrorCode;

/**
 * 控制器基础类
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var \think\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * 是否批量验证
     * @var bool
     */
    protected $batchValidate = false;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    /**
     * 当前登录用户ID
     * @var int|null
     */
    protected $userId = null;

    /**
     * 当前登录用户信息
     * @var array|null
     */
    protected $user = null;

    /**
     * 构造方法
     * @access public
     * @param  App  $app  应用对象
     */
    public function __construct(App $app)
    {
        $this->app     = $app;
        $this->request = $this->app->request;

        // 控制器初始化
        $this->initialize();
    }

    // 初始化
    protected function initialize()
    {}

    /**
     * 验证数据
     * @access protected
     * @param  array        $data     数据
     * @param  string|array $validate 验证器名或者验证规则数组
     * @param  array        $message  提示信息
     * @param  bool         $batch    是否批量验证
     * @return array|string|true
     * @throws ValidateException
     */
    protected function validate(array $data, string|array $validate, array $message = [], bool $batch = false)
    {
        if (is_array($validate)) {
            $v = new Validate();
            $v->rule($validate);
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                [$validate, $scene] = explode('.', $validate);
            }
            $class = false !== strpos($validate, '\\') ? $validate : $this->app->parseClass('validate', $validate);
            $v     = new $class();
            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        $v->message($message);

        // 是否批量验证
        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }

        return $v->failException(true)->check($data);
    }

    /**
     * 成功响应
     * @param mixed $data
     * @param string $message
     * @return \think\Response
     */
    protected function success($data = null, string $message = '操作成功')
    {
        return Response::success($data, $message);
    }

    /**
     * 失败响应
     * @param string $message
     * @param int $code
     * @param mixed $errors
     * @return \think\Response
     */
    protected function error(string $message = '操作失败', int $code = ErrorCode::BUSINESS_ERROR, $errors = null)
    {
        return Response::error($message, $code, $errors);
    }

    /**
     * 分页响应
     * @param array $items
     * @param array $meta
     * @param string $message
     * @return \think\Response
     */
    protected function paginate(array $items, array $meta, string $message = '获取成功')
    {
        return Response::paginate($items, $meta, $message);
    }

    /**
     * 创建成功响应
     * @param mixed $data
     * @param string $message
     * @return \think\Response
     */
    protected function created($data = null, string $message = '创建成功')
    {
        return Response::created($data, $message);
    }

    /**
     * 获取当前用户ID
     * @return int|null
     */
    protected function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * 获取当前用户信息
     * @return array|null
     */
    protected function getUser(): ?array
    {
        return $this->user;
    }

    /**
     * 设置当前用户信息
     * @param int $userId
     * @param array $user
     */
    protected function setUser(int $userId, array $user = [])
    {
        $this->userId = $userId;
        $this->user = $user;
    }
}
