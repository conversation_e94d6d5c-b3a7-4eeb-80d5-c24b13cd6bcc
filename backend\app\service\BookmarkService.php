<?php

namespace app\service;

use app\model\Bookmark;
use app\model\BookmarkTag;
use app\model\Tag;
use app\model\Folder;
use app\exception\BusinessException;
use app\service\MoonshotService;
use app\service\DeadLinkCheckService;
use think\facade\Db;
use think\facade\Log;

class BookmarkService
{
    public function createBookmark(int $userId, array $data): Bookmark
    {
        // 验证文件夹是否存在
        if (!empty($data['folder_id'])) {
            $folder = Folder::where('id', $data['folder_id'])->where('user_id', $userId)->find();
            if (!$folder) {
                throw BusinessException::folderNotFound();
            }
        }

        // 验证URL格式
        if (!filter_var($data['url'], FILTER_VALIDATE_URL)) {
            throw BusinessException::bookmarkUrlInvalid();
        }

        Db::startTrans();
        try {
            $bookmark = new Bookmark();
            $bookmark->user_id = $userId;
            $bookmark->folder_id = $data['folder_id'] ?? null;
            $bookmark->title = $data['title'];
            $bookmark->url = $data['url'];
            $bookmark->description = $data['description'] ?? null;
            $bookmark->favicon = $data['favicon'] ?? null;
            $bookmark->is_star = $data['is_star'] ?? 0;
            $bookmark->sort_order = $this->getNextSortOrder($userId, $data['folder_id'] ?? null);

            if (!$bookmark->save()) {
                throw new BusinessException('创建书签失败');
            }

            // 处理标签
            if (!empty($data['tags'])) {
                $this->syncBookmarkTags($bookmark->id, $data['tags'], $userId);
            }

            Db::commit();
            // 重新加载书签并包含关联的标签
            return Bookmark::with('tags')->find($bookmark->id);
        } catch (\Exception $e) {
            Db::rollback();
            throw new BusinessException('创建书签失败：' . $e->getMessage());
        }
    }

    public function updateBookmark(int $userId, int $bookmarkId, array $data): Bookmark
    {
        $bookmark = Bookmark::where('id', $bookmarkId)->where('user_id', $userId)->find();
        if (!$bookmark) {
            throw BusinessException::bookmarkNotFound();
        }

        // 验证文件夹
        if (isset($data['folder_id']) && $data['folder_id']) {
            $folder = Folder::where('id', $data['folder_id'])->where('user_id', $userId)->find();
            if (!$folder) {
                throw BusinessException::folderNotFound();
            }
        }

        // 验证URL
        if (isset($data['url']) && !filter_var($data['url'], FILTER_VALIDATE_URL)) {
            throw BusinessException::bookmarkUrlInvalid();
        }

        Db::startTrans();
        try {
            $allowedFields = ['title', 'url', 'description', 'favicon', 'folder_id', 'is_star'];
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $bookmark->$field = $data[$field];
                }
            }

            if (!$bookmark->save()) {
                throw new BusinessException('更新书签失败');
            }

            // 处理标签
            if (isset($data['tags'])) {
                $this->syncBookmarkTags($bookmark->id, $data['tags'], $userId);
            }

            Db::commit();
            // 重新加载书签并包含关联的标签
            return Bookmark::with('tags')->find($bookmark->id);
        } catch (\Exception $e) {
            Db::rollback();
            throw new BusinessException('更新书签失败：' . $e->getMessage());
        }
    }

    public function deleteBookmark(int $userId, int $bookmarkId): bool
    {
        $bookmark = Bookmark::where('id', $bookmarkId)->where('user_id', $userId)->find();
        if (!$bookmark) {
            throw BusinessException::bookmarkNotFound();
        }

        Db::startTrans();
        try {
            // 删除标签关联
            BookmarkTag::where('bookmark_id', $bookmarkId)->delete();
            
            // 删除书签
            $bookmark->delete();

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw new BusinessException('删除书签失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户书签列表
     * 
     * @param int $userId 用户ID
     * @param array $params 查询参数，可选包含：
     *     - folder_id: 文件夹ID筛选
     *     - is_star: 星标筛选
     *     - search: 搜索关键词(匹配标题/描述/URL)
     *     - order_by: 排序字段(默认sort_order)
     *     - order_dir: 排序方向(默认asc)
     *     - page: 页码(默认1)
     *     - per_page/limit: 每页条数(默认20)
     * 
     * @return array 包含分页书签列表和分页信息
     *     - items: 书签数组(已转换为API格式)
     *     - meta: 分页元信息(total/page/limit/pages)
     */
    public function getBookmarkList(int $userId, array $params = []): array
    {
        $query = Bookmark::where('user_id', $userId)->with('tags');

        // 文件夹筛选
        if (isset($params['folder_id']) && $params['folder_id'] !== '' && $params['folder_id'] !== null) {
            $query->where('folder_id', $params['folder_id']);
        }

        // 标签筛选
        if (isset($params['tag_id']) && $params['tag_id'] !== '' && $params['tag_id'] !== null) {
            $query->whereExists(function($q) use ($params) {
                $q->table('bookmark_tags')->where('bookmark_id', 'bookmarks.id')
                  ->where('tag_id', $params['tag_id']);
            });
        }

        // 星标筛选
        if (isset($params['is_star']) && $params['is_star'] !== '' && $params['is_star'] !== null) {
            $query->where('is_star', $params['is_star']);
        }

        // 搜索
        $keyword = $params['search'] ?? $params['keyword'] ?? '';
        if (!empty($keyword)) {
            $query->where(function($q) use ($keyword) {
                $q->where('title', 'like', '%' . $keyword . '%')
                  ->whereOr('description', 'like', '%' . $keyword . '%')
                  ->whereOr('url', 'like', '%' . $keyword . '%');
            });
        }

        // 排序
        $orderBy = !empty($params['order_by']) ? $params['order_by'] :
                   (!empty($params['sort']) ? $params['sort'] : 'sort_order');
        $orderDir = !empty($params['order_dir']) ? $params['order_dir'] :
                    (!empty($params['order']) ? $params['order'] : 'asc');
        $query->order($orderBy, $orderDir);

        // 分页
        $page = max(1, (int)($params['page'] ?? 1));
        $limit = max(1, (int)($params['per_page'] ?? $params['limit'] ?? 20));
        
        $total = $query->count();
        $bookmarks = $query->page($page, $limit)->select();

        return [
            'items' => $bookmarks->map(function($bookmark) {
                return $bookmark->toApiArray();
            })->toArray(),
            'meta' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]
        ];
    }

    public function visitBookmark(int $userId, int $bookmarkId): bool
    {
        $bookmark = Bookmark::where('id', $bookmarkId)->where('user_id', $userId)->find();
        if (!$bookmark) {
            throw BusinessException::bookmarkNotFound();
        }

        return $bookmark->incrementVisit();
    }

    private function syncBookmarkTags(int $bookmarkId, array $tagNames, int $userId): void
    {
        // 删除现有关联
        BookmarkTag::where('bookmark_id', $bookmarkId)->delete();

        foreach ($tagNames as $tagName) {
            if (empty($tagName)) continue;

            // 查找或创建标签
            $tag = Tag::where('user_id', $userId)->where('name', $tagName)->find();
            if (!$tag) {
                $tag = new Tag();
                $tag->user_id = $userId;
                $tag->name = $tagName;
                $tag->usage_count = 0;
                $tag->save();
            }

            // 创建关联
            $bookmarkTag = new BookmarkTag();
            $bookmarkTag->bookmark_id = $bookmarkId;
            $bookmarkTag->tag_id = $tag->id;
            $bookmarkTag->save();

            // 增加标签使用次数
            $tag->incrementUsage();
        }
    }

    private function getNextSortOrder(int $userId, ?int $folderId): int
    {
        $maxSort = Bookmark::where('user_id', $userId)
            ->where('folder_id', $folderId)
            ->max('sort_order');
        return ($maxSort ?? 0) + 1;
    }

    /**
     * AI解析书签内容
     *
     * @param int $userId 用户ID
     * @param int $bookmarkId 书签ID
     * @return array 解析结果
     * @throws BusinessException
     */
    public function parseBookmarkWithAI(int $userId, int $bookmarkId): array
    {
        // 查找书签
        $bookmark = Bookmark::where('id', $bookmarkId)
            ->where('user_id', $userId)
            ->find();

        if (!$bookmark) {
            throw BusinessException::bookmarkNotFound();
        }

        try {
            // 使用Moonshot AI服务解析网页内容
            $moonshotService = new MoonshotService();
            $parseResult = $moonshotService->parseWebContent($bookmark->url);

            // 记录解析日志
            Log::info('书签AI解析成功', [
                'user_id' => $userId,
                'bookmark_id' => $bookmarkId,
                'url' => $bookmark->url,
                'title' => $parseResult['title'],
                'tags_count' => count($parseResult['tags'])
            ]);

            return $parseResult;

        } catch (\Exception $e) {
            Log::error('书签AI解析失败', [
                'user_id' => $userId,
                'bookmark_id' => $bookmarkId,
                'url' => $bookmark->url,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * AI解析URL内容（不需要现有书签）
     *
     * @param string $url 要解析的URL
     * @return array 解析结果
     * @throws BusinessException
     */
    public function parseUrlWithAI(string $url): array
    {
        // 验证URL格式
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw new BusinessException('无效的URL格式');
        }

        try {
            // 使用Moonshot AI服务解析网页内容
            $moonshotService = new MoonshotService();
            $parseResult = $moonshotService->parseWebContent($url);

            // 记录解析日志
            Log::info('URL AI解析成功', [
                'url' => $url,
                'title' => $parseResult['title'],
                'tags_count' => count($parseResult['tags'])
            ]);

            return $parseResult;

        } catch (\Exception $e) {
            Log::error('URL AI解析失败', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * 检测书签死链
     *
     * @param int $userId 用户ID
     * @param int $bookmarkId 书签ID
     * @return array 检测结果
     * @throws BusinessException
     */
    public function checkBookmarkDeadLink(int $userId, int $bookmarkId): array
    {
        $deadLinkService = new DeadLinkCheckService();
        return $deadLinkService->checkBookmark($userId, $bookmarkId);
    }

    /**
     * 批量检测用户书签死链
     *
     * @param int $userId 用户ID
     * @param array $options 检测选项
     * @return array 批量检测结果
     */
    public function batchCheckDeadLinks(int $userId, array $options = []): array
    {
        $deadLinkService = new DeadLinkCheckService();
        return $deadLinkService->batchCheckBookmarks($userId, $options);
    }

    /**
     * 获取用户死链统计信息
     *
     * @param int $userId 用户ID
     * @return array 统计信息
     */
    public function getDeadLinkStats(int $userId): array
    {
        $deadLinkService = new DeadLinkCheckService();
        return $deadLinkService->getDeadLinkStats($userId);
    }

    /**
     * 批量操作书签
     *
     * @param int $userId 用户ID
     * @param array $bookmarkIds 书签ID列表
     * @param string $action 操作类型
     * @param array $params 操作参数
     * @return array 操作结果
     * @throws BusinessException
     */
    public function batchOperation(int $userId, array $bookmarkIds, string $action, array $params = []): array
    {
        if (empty($bookmarkIds)) {
            throw new BusinessException('请选择要操作的书签');
        }

        // 验证书签所有权
        $bookmarks = Bookmark::where('user_id', $userId)
            ->whereIn('id', $bookmarkIds)
            ->select();

        $validIds = $bookmarks->column('id');
        $invalidIds = array_diff($bookmarkIds, $validIds);

        $successCount = 0;
        $failedCount = count($invalidIds); // 先计算无效ID的数量
        $failedIds = $invalidIds; // 先添加无效ID到失败列表

        // 如果没有有效的书签，直接返回结果
        if ($bookmarks->isEmpty()) {
            return [
                'total_requested' => count($bookmarkIds),
                'success_count' => 0,
                'failed_count' => $failedCount,
                'failed_ids' => $failedIds,
                'action' => $action
            ];
        }

        try {
            Db::startTrans();

            foreach ($bookmarks as $bookmark) {
                try {
                    switch ($action) {
                        case 'delete':
                            $this->deleteBookmark($userId, $bookmark->id);
                            $successCount++;
                            break;

                        case 'move':
                            if (!isset($params['folder_id'])) {
                                throw new BusinessException('移动操作需要指定目标文件夹');
                            }
                            $this->updateBookmark($userId, $bookmark->id, ['folder_id' => $params['folder_id']]);
                            $successCount++;
                            break;

                        case 'star':
                            $this->updateBookmark($userId, $bookmark->id, ['is_star' => 1]);
                            $successCount++;
                            break;

                        case 'unstar':
                            $this->updateBookmark($userId, $bookmark->id, ['is_star' => 0]);
                            $successCount++;
                            break;

                        case 'add_tags':
                            if (!isset($params['tags']) || !is_array($params['tags'])) {
                                throw new BusinessException('添加标签操作需要指定标签列表');
                            }
                            $this->addBookmarkTags($bookmark->id, $params['tags']);
                            $successCount++;
                            break;

                        case 'remove_tags':
                            if (!isset($params['tags']) || !is_array($params['tags'])) {
                                throw new BusinessException('移除标签操作需要指定标签列表');
                            }
                            $this->removeBookmarkTags($bookmark->id, $params['tags']);
                            $successCount++;
                            break;

                        default:
                            throw new BusinessException('不支持的操作类型：' . $action);
                    }
                } catch (\Exception $e) {
                    $failedCount++;
                    $failedIds[] = $bookmark->id;
                    Log::error('批量操作失败', [
                        'user_id' => $userId,
                        'bookmark_id' => $bookmark->id,
                        'action' => $action,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Db::commit();

            Log::info('批量操作完成', [
                'user_id' => $userId,
                'action' => $action,
                'total_requested' => count($bookmarkIds),
                'success_count' => $successCount,
                'failed_count' => $failedCount
            ]);

            return [
                'total_requested' => count($bookmarkIds),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'failed_ids' => $failedIds,
                'action' => $action
            ];

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 添加书签标签
     *
     * @param int $bookmarkId 书签ID
     * @param array $tagNames 标签名称列表
     */
    private function addBookmarkTags(int $bookmarkId, array $tagNames): void
    {
        foreach ($tagNames as $tagName) {
            $tagName = trim($tagName);
            if (empty($tagName)) {
                continue;
            }

            // 查找或创建标签
            $tag = Tag::where('name', $tagName)->find();
            if (!$tag) {
                $tag = Tag::create(['name' => $tagName]);
            }

            // 检查关联是否已存在
            $exists = BookmarkTag::where('bookmark_id', $bookmarkId)
                ->where('tag_id', $tag->id)
                ->find();

            if (!$exists) {
                BookmarkTag::create([
                    'bookmark_id' => $bookmarkId,
                    'tag_id' => $tag->id
                ]);
            }
        }
    }

    /**
     * 移除书签标签
     *
     * @param int $bookmarkId 书签ID
     * @param array $tagNames 标签名称列表
     */
    private function removeBookmarkTags(int $bookmarkId, array $tagNames): void
    {
        if (empty($tagNames)) {
            return;
        }

        $tagIds = Tag::whereIn('name', $tagNames)->column('id');
        if (!empty($tagIds)) {
            BookmarkTag::where('bookmark_id', $bookmarkId)
                ->whereIn('tag_id', $tagIds)
                ->delete();
        }
    }

    /**
     * 获取URL信息
     *
     * @param string $url URL地址
     * @return array URL信息
     * @throws BusinessException
     */
    public function getUrlInfo(string $url): array
    {
        try {
            // 初始化默认信息
            $urlInfo = [
                'title' => '',
                'description' => '',
                'favicon' => '',
                'url' => $url
            ];

            // 设置请求上下文选项
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                        'Accept-Encoding: gzip, deflate',
                        'Connection: close'
                    ],
                    'timeout' => 10,
                    'follow_location' => true,
                    'max_redirects' => 3
                ]
            ]);

            // 获取网页内容
            $html = @file_get_contents($url, false, $context);

            if ($html === false) {
                // 如果获取失败，返回基本信息
                $urlInfo['title'] = $this->extractDomainFromUrl($url);
                return $urlInfo;
            }

            // 解析HTML内容
            $this->parseHtmlContent($html, $urlInfo, $url);

            return $urlInfo;

        } catch (\Exception $e) {
            Log::error('获取URL信息失败: ' . $e->getMessage(), ['url' => $url]);

            // 返回基本信息
            return [
                'title' => $this->extractDomainFromUrl($url),
                'description' => '',
                'favicon' => '',
                'url' => $url
            ];
        }
    }

    /**
     * 解析HTML内容
     *
     * @param string $html HTML内容
     * @param array &$urlInfo URL信息引用
     * @param string $url 原始URL
     */
    private function parseHtmlContent(string $html, array &$urlInfo, string $url): void
    {
        // 创建DOMDocument对象
        $dom = new \DOMDocument();

        // 禁用错误报告，因为HTML可能不完全符合XML标准
        libxml_use_internal_errors(true);

        // 加载HTML内容
        $dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));

        // 恢复错误报告
        libxml_clear_errors();

        // 获取标题
        $titleNodes = $dom->getElementsByTagName('title');
        if ($titleNodes->length > 0) {
            $urlInfo['title'] = trim($titleNodes->item(0)->textContent);
        }

        // 获取meta标签信息
        $metaTags = $dom->getElementsByTagName('meta');
        foreach ($metaTags as $meta) {
            $name = $meta->getAttribute('name');
            $property = $meta->getAttribute('property');
            $content = $meta->getAttribute('content');

            // 获取描述
            if (in_array($name, ['description', 'Description']) || $property === 'og:description') {
                if (empty($urlInfo['description']) && !empty($content)) {
                    $urlInfo['description'] = trim($content);
                }
            }

            // 获取标题（优先使用og:title）
            if ($property === 'og:title' && !empty($content)) {
                $urlInfo['title'] = trim($content);
            }
        }

        // 获取favicon
        $this->extractFavicon($dom, $urlInfo, $url);

        // 如果没有标题，使用域名
        if (empty($urlInfo['title'])) {
            $urlInfo['title'] = $this->extractDomainFromUrl($url);
        }
    }

    /**
     * 提取favicon
     *
     * @param \DOMDocument $dom DOM对象
     * @param array &$urlInfo URL信息引用
     * @param string $url 原始URL
     */
    private function extractFavicon(\DOMDocument $dom, array &$urlInfo, string $url): void
    {
        $linkTags = $dom->getElementsByTagName('link');

        foreach ($linkTags as $link) {
            $rel = $link->getAttribute('rel');
            $href = $link->getAttribute('href');

            if (in_array($rel, ['icon', 'shortcut icon', 'apple-touch-icon']) && !empty($href)) {
                // 处理相对URL
                if (strpos($href, 'http') !== 0) {
                    $parsedUrl = parse_url($url);
                    $baseUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];

                    if (strpos($href, '/') === 0) {
                        $href = $baseUrl . $href;
                    } else {
                        $href = $baseUrl . '/' . $href;
                    }
                }

                $urlInfo['favicon'] = $href;
                break;
            }
        }

        // 如果没有找到favicon，尝试默认路径
        if (empty($urlInfo['favicon'])) {
            $parsedUrl = parse_url($url);
            $baseUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
            $urlInfo['favicon'] = $baseUrl . '/favicon.ico';
        }
    }

    /**
     * 从URL中提取域名
     *
     * @param string $url URL地址
     * @return string 域名
     */
    private function extractDomainFromUrl(string $url): string
    {
        $parsedUrl = parse_url($url);
        return $parsedUrl['host'] ?? $url;
    }
}
