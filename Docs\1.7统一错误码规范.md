# Web端书签管理系统 - 统一错误码规范

## 1. 概述

本文档定义了Web端书签管理系统的统一错误码规范，包括错误码的设计原则、分类体系、使用规范和最佳实践。通过标准化的错误处理机制，提升系统的可维护性和用户体验。

## 2. 错误码设计原则

### 2.1 基本原则

1. **唯一性**: 每个错误码在系统中必须唯一
2. **可读性**: 错误码应具有明确的业务含义
3. **可扩展**: 预留足够的错误码空间供未来使用
4. **层次性**: 通过错误码可以快速定位问题模块
5. **国际化**: 错误信息支持多语言切换

### 2.2 错误码格式

```
[状态码类型][模块代码][具体错误]
    │           │          │
    │           │          └─── 具体错误编号 (01-99)
    │           └──────────────── 模块代码 (00-99)
    └──────────────────────────── HTTP状态码前缀 (400/401/403/404/422/500)
```

**示例**: 
- `40001` = 400(请求错误) + 00(通用模块) + 01(参数错误)
- `40101` = 401(未授权) + 01(用户模块) + 01(未登录)

### 2.3 模块代码定义

| 模块代码 | 模块名称 | 说明 |
|---------|---------|------|
| 00 | 通用模块 | 系统级通用错误 |
| 01 | 用户模块 | 用户认证、账户管理 |
| 02 | 文件夹模块 | 文件夹管理相关 |
| 03 | 书签模块 | 书签CRUD操作 |
| 04 | 标签模块 | 标签管理相关 |
| 05 | 搜索模块 | 搜索与筛选功能 |
| 06 | AI模块 | AI解析相关 |
| 07 | 导入导出 | 数据导入导出 |
| 08 | 统计模块 | 数据统计分析 |
| 09 | 系统配置 | 系统设置相关 |

## 3. 错误码详细定义

### 3.1 通用错误码 (00)

| 错误码 | 错误标识 | 错误信息 | 说明 |
|--------|----------|----------|------|
| 0 | SUCCESS | 操作成功 | 请求处理成功 |
| 40001 | INVALID_PARAMS | 请求参数错误 | 参数格式或类型错误 |
| 40002 | MISSING_PARAMS | 缺少必要参数 | 必填参数未提供 |
| 40003 | INVALID_JSON | JSON格式错误 | 请求体不是有效的JSON |
| 40004 | METHOD_NOT_ALLOWED | 请求方法不允许 | HTTP方法错误 |
| 40005 | INVALID_SIGNATURE | 签名验证失败 | API签名错误 |
| 40301 | ACCESS_DENIED | 访问被拒绝 | 无权限访问该资源 |
| 40401 | NOT_FOUND | 资源不存在 | 请求的资源未找到 |
| 42201 | VALIDATION_FAILED | 数据验证失败 | 业务规则验证失败 |
| 42901 | RATE_LIMIT_EXCEEDED | 请求过于频繁 | 触发限流规则 |
| 50001 | INTERNAL_ERROR | 服务器内部错误 | 系统异常 |
| 50002 | DATABASE_ERROR | 数据库错误 | 数据库操作失败 |
| 50003 | CACHE_ERROR | 缓存服务错误 | Redis连接或操作失败 |
| 50004 | EXTERNAL_SERVICE_ERROR | 外部服务错误 | 第三方服务调用失败 |

### 3.2 用户模块错误码 (01)

| 错误码 | 错误标识 | 错误信息 | 说明 |
|--------|----------|----------|------|
| 40101 | UNAUTHORIZED | 未认证 | 未提供有效的认证信息 |
| 40102 | TOKEN_EXPIRED | Token已过期 | Access Token过期 |
| 40103 | TOKEN_INVALID | Token无效 | Token格式错误或已失效 |
| 40104 | REFRESH_TOKEN_EXPIRED | Refresh Token已过期 | 需要重新登录 |
| 40105 | SESSION_EXPIRED | 会话已过期 | 用户会话超时 |
| 10001 | EMAIL_EXISTS | 邮箱已被注册 | 注册时邮箱重复 |
| 10002 | USERNAME_EXISTS | 用户名已被使用 | 注册时用户名重复 |
| 10003 | INVALID_CREDENTIALS | 账号或密码错误 | 登录失败 |
| 10004 | WRONG_PASSWORD | 原密码错误 | 修改密码时原密码验证失败 |
| 10005 | ACCOUNT_DISABLED | 账号已被禁用 | 用户状态异常 |
| 10006 | PASSWORD_TOO_WEAK | 密码强度不足 | 密码不符合安全要求 |
| 10007 | LOGIN_ATTEMPTS_EXCEEDED | 登录尝试次数过多 | 触发登录保护 |
| 10008 | EMAIL_NOT_VERIFIED | 邮箱未验证 | 需要先验证邮箱 |
| 10009 | AVATAR_TOO_LARGE | 头像文件过大 | 超过2MB限制 |
| 10010 | INVALID_AVATAR_FORMAT | 头像格式不支持 | 仅支持jpg/png/webp |

### 3.3 文件夹模块错误码 (02)

| 错误码 | 错误标识 | 错误信息 | 说明 |
|--------|----------|----------|------|
| 20001 | FOLDER_NAME_EXISTS | 文件夹名称已存在 | 同级目录下名称重复 |
| 20002 | INVALID_PARENT_FOLDER | 无效的父文件夹 | 不能移动到自身或子文件夹 |
| 20003 | FOLDER_LEVEL_EXCEEDED | 文件夹层级超过限制 | 最多支持10级 |
| 20004 | FOLDER_NOT_EMPTY | 文件夹不为空 | 删除时包含子项 |
| 20005 | FOLDER_NOT_FOUND | 文件夹不存在 | 文件夹ID无效 |
| 20006 | FOLDER_ACCESS_DENIED | 无权访问该文件夹 | 不是文件夹所有者 |
| 20007 | CIRCULAR_REFERENCE | 循环引用错误 | 文件夹结构出现循环 |
| 20008 | FOLDER_NAME_TOO_LONG | 文件夹名称过长 | 超过100字符限制 |

### 3.4 书签模块错误码 (03)

| 错误码 | 错误标识 | 错误信息 | 说明 |
|--------|----------|----------|------|
| 30001 | INVALID_URL | URL格式不正确 | 不是有效的HTTP(S)链接 |
| 30002 | BOOKMARK_EXISTS | 书签已存在 | URL重复 |
| 30003 | BATCH_LIMIT_EXCEEDED | 批量操作数量超限 | 超过300条限制 |
| 30004 | BOOKMARK_NOT_FOUND | 书签不存在 | 书签ID无效 |
| 30005 | BOOKMARK_ACCESS_DENIED | 无权访问该书签 | 不是书签所有者 |
| 30006 | FETCH_PAGE_FAILED | 获取网页信息失败 | 无法抓取网页内容 |
| 30007 | BOOKMARK_TITLE_TOO_LONG | 书签标题过长 | 超过255字符限制 |
| 30008 | INVALID_BOOKMARK_DATA | 书签数据无效 | 数据格式错误 |
| 30009 | BOOKMARK_IN_TRASH | 书签在回收站中 | 需要先恢复 |

### 3.5 标签模块错误码 (04)

| 错误码 | 错误标识 | 错误信息 | 说明 |
|--------|----------|----------|------|
| 40001 | TAG_NAME_EXISTS | 标签名称已存在 | 用户下标签名重复 |
| 40002 | TAG_NOT_FOUND | 标签不存在 | 标签ID无效 |
| 40003 | TAG_IN_USE | 标签正在使用中 | 有书签关联此标签 |
| 40004 | TAG_LIMIT_EXCEEDED | 标签数量超限 | 单个书签最多10个标签 |
| 40005 | TAG_NAME_TOO_LONG | 标签名称过长 | 超过50字符限制 |
| 40006 | INVALID_TAG_COLOR | 无效的标签颜色 | 颜色格式错误 |
| 40007 | TAG_MERGE_FAILED | 标签合并失败 | 目标标签不存在 |

### 3.6 搜索模块错误码 (05)

| 错误码 | 错误标识 | 错误信息 | 说明 |
|--------|----------|----------|------|
| 50001 | SEARCH_QUERY_TOO_SHORT | 搜索关键词过短 | 至少需要2个字符 |
| 50002 | SEARCH_SYNTAX_ERROR | 搜索语法错误 | 高级搜索语法错误 |
| 50003 | SEARCH_TIMEOUT | 搜索超时 | 搜索耗时过长 |
| 50004 | TOO_MANY_CONDITIONS | 搜索条件过多 | 智能文件夹条件超过10个 |
| 50005 | INVALID_FILTER_FIELD | 无效的筛选字段 | 字段名不存在 |
| 50006 | INVALID_FILTER_OPERATOR | 无效的筛选操作符 | 操作符不支持 |

### 3.7 AI模块错误码 (06)

| 错误码 | 错误标识 | 错误信息 | 说明 |
|--------|----------|----------|------|
| 60001 | AI_SERVICE_UNAVAILABLE | AI服务暂时不可用 | AI接口调用失败 |
| 60002 | AI_QUOTA_EXCEEDED | AI解析额度不足 | 超过月度限额 |
| 60003 | AI_PARSE_FAILED | AI解析失败 | 内容解析出错 |
| 60004 | CONTENT_TOO_LONG | 内容过长无法解析 | 超过AI处理限制 |
| 60005 | AI_TASK_TIMEOUT | AI任务超时 | 解析时间过长 |
| 60006 | UNSUPPORTED_CONTENT_TYPE | 不支持的内容类型 | 无法解析此类内容 |
| 60007 | AI_RATE_LIMITED | AI请求过于频繁 | 触发AI服务限流 |

### 3.8 导入导出错误码 (07)

| 错误码 | 错误标识 | 错误信息 | 说明 |
|--------|----------|----------|------|
| 50001 | UNSUPPORTED_FILE_FORMAT | 文件格式不支持 | 仅支持HTML/JSON/CSV |
| 50002 | FILE_TOO_LARGE | 文件过大 | 超过10MB限制 |
| 50003 | IMPORT_PARSE_ERROR | 导入文件解析失败 | 文件内容格式错误 |
| 50004 | EXPORT_FAILED | 导出失败 | 生成文件出错 |
| 50005 | IMPORT_TASK_NOT_FOUND | 导入任务不存在 | 任务ID无效 |
| 50006 | IMPORT_IN_PROGRESS | 导入正在进行中 | 有其他导入任务执行 |
| 50007 | INVALID_IMPORT_DATA | 导入数据无效 | 数据格式不符合要求 |

### 3.9 统计模块错误码 (08)

| 错误码 | 错误标识 | 错误信息 | 说明 |
|--------|----------|----------|------|
| 80001 | INVALID_DATE_RANGE | 无效的日期范围 | 开始日期大于结束日期 |
| 80002 | DATE_RANGE_TOO_LARGE | 日期范围过大 | 最多查询一年数据 |
| 80003 | REPORT_NOT_FOUND | 报告不存在 | 报告未生成或已过期 |
| 80004 | REPORT_GENERATION_FAILED | 报告生成失败 | 数据处理出错 |
| 80005 | STATS_NOT_AVAILABLE | 统计数据不可用 | 数据尚未生成 |

### 3.10 系统配置错误码 (09)

| 错误码 | 错误标识 | 错误信息 | 说明 |
|--------|----------|----------|------|
| 90001 | CONFIG_NOT_FOUND | 配置项不存在 | 配置键无效 |
| 90002 | INVALID_CONFIG_VALUE | 配置值无效 | 不符合配置类型 |
| 90003 | CONFIG_UPDATE_FAILED | 配置更新失败 | 系统配置无法修改 |
| 90004 | PREFERENCE_INVALID | 偏好设置无效 | 用户偏好格式错误 |

## 4. 错误响应格式

### 4.1 标准错误响应

```json
{
    "code": 40001,
    "message": "请求参数错误",
    "errors": {
        "email": "邮箱格式不正确",
        "password": "密码长度必须在8-32位之间"
    },
    "data": null,
    "timestamp": "2024-01-20T10:30:00Z",
    "path": "/api/v1/auth/register"
}
```

### 4.2 响应字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| code | integer | 是 | 错误码 |
| message | string | 是 | 错误信息（用户可读） |
| errors | object | 否 | 详细错误信息（字段级别） |
| data | any | 否 | 附加数据（通常为null） |
| timestamp | string | 是 | 错误发生时间（ISO 8601） |
| path | string | 是 | 请求路径 |

### 4.3 字段验证错误格式

当请求参数验证失败时，errors字段包含具体的字段错误：

```json
{
    "code": 42201,
    "message": "数据验证失败",
    "errors": {
        "email": "邮箱格式不正确",
        "username": "用户名只能包含字母、数字和下划线",
        "password": "密码必须包含字母和数字",
        "tags": {
            "0": "标签名称不能为空",
            "3": "标签名称过长"
        }
    }
}
```

## 5. 错误处理最佳实践

### 5.1 后端错误处理

#### 5.1.1 统一异常处理器

```php
namespace app\exception;

class ApiException extends \Exception
{
    protected $code;
    protected $errors;
    
    public function __construct($code, $message = '', $errors = null)
    {
        $this->code = $code;
        $this->errors = $errors;
        parent::__construct($message);
    }
    
    public function getErrorCode()
    {
        return $this->code;
    }
    
    public function getErrors()
    {
        return $this->errors;
    }
}
```

#### 5.1.2 错误码常量定义

```php
namespace app\constant;

class ErrorCode
{
    // 通用错误
    const SUCCESS = 0;
    const INVALID_PARAMS = 40001;
    const MISSING_PARAMS = 40002;
    
    // 用户模块
    const UNAUTHORIZED = 40101;
    const TOKEN_EXPIRED = 40102;
    const EMAIL_EXISTS = 10001;
    
    // ... 其他错误码
}
```

#### 5.1.3 错误信息配置

```php
// config/error_messages.php
return [
    'zh-CN' => [
        0 => '操作成功',
        40001 => '请求参数错误',
        40101 => '未认证',
        10001 => '邮箱已被注册',
        // ...
    ],
    'en-US' => [
        0 => 'Success',
        40001 => 'Invalid parameters',
        40101 => 'Unauthorized',
        10001 => 'Email already exists',
        // ...
    ]
];
```

### 5.2 前端错误处理

#### 5.2.1 统一错误拦截

```javascript
// api/interceptors.js
import { ElMessage } from 'element-plus'
import router from '@/router'

// 响应拦截器
axios.interceptors.response.use(
    response => response,
    error => {
        const { code, message } = error.response.data
        
        // 通用错误处理
        switch (code) {
            case 40101: // 未认证
            case 40102: // Token过期
                router.push('/login')
                break
            
            case 42901: // 限流
                ElMessage.warning('请求过于频繁，请稍后再试')
                break
                
            case 50001: // 服务器错误
                ElMessage.error('服务器开小差了，请稍后再试')
                break
                
            default:
                ElMessage.error(message || '请求失败')
        }
        
        return Promise.reject(error)
    }
)
```

#### 5.2.2 字段级错误显示

```vue
<template>
  <el-form :model="form" :rules="rules" ref="formRef">
    <el-form-item label="邮箱" prop="email" :error="fieldErrors.email">
      <el-input v-model="form.email" />
    </el-form-item>
  </el-form>
</template>

<script setup>
const fieldErrors = ref({})

const handleSubmit = async () => {
    try {
        await api.register(form.value)
    } catch (error) {
        if (error.response.data.errors) {
            fieldErrors.value = error.response.data.errors
        }
    }
}
</script>
```

### 5.3 错误日志记录

#### 5.3.1 日志格式

```json
{
    "timestamp": "2024-01-20T10:30:00Z",
    "level": "ERROR",
    "code": 50001,
    "message": "Database connection failed",
    "user_id": 123,
    "request_id": "req_abc123",
    "stack_trace": "...",
    "context": {
        "url": "/api/v1/bookmarks",
        "method": "POST",
        "ip": "*************",
        "user_agent": "Mozilla/5.0..."
    }
}
```

#### 5.3.2 错误监控告警

- 错误率超过阈值时发送告警
- 关键错误（5xx）立即通知
- 定期生成错误分析报告

## 6. 错误码维护规范

### 6.1 新增错误码流程

1. 确定错误所属模块
2. 在对应模块中选择未使用的错误码
3. 更新错误码文档
4. 添加多语言错误信息
5. 编写单元测试
6. 代码评审

### 6.2 错误码弃用流程

1. 标记错误码为已弃用
2. 提供替代方案
3. 设置弃用过渡期（至少3个月）
4. 监控使用情况
5. 完全移除

### 6.3 文档维护

- 每次新增/修改错误码必须更新文档
- 定期审查错误码使用情况
- 保持错误信息的准确性和时效性

## 7. 客户端错误处理指南

### 7.1 移动端处理建议

1. 缓存常用错误信息，减少网络请求
2. 提供离线错误处理机制
3. 错误信息本地化存储

### 7.2 浏览器扩展处理

1. 轻量级错误提示
2. 不阻塞用户操作
3. 提供快速重试机制

## 8. 错误码速查表

### 8.1 按HTTP状态码分类

**400 Bad Request**
- 40001-40099: 通用请求错误
- 其他: 各模块参数错误

**401 Unauthorized**
- 40101-40109: 认证相关错误

**403 Forbidden**
- 40301-40309: 权限相关错误

**404 Not Found**
- 40401-40409: 资源不存在

**422 Unprocessable Entity**
- 42201-42299: 业务逻辑错误

**429 Too Many Requests**
- 42901-42909: 限流相关

**500 Internal Server Error**
- 50001-50099: 服务器错误

### 8.2 常见场景错误码

**用户注册**
- 10001: 邮箱已存在
- 10002: 用户名已存在
- 10006: 密码强度不足

**用户登录**
- 10003: 账号或密码错误
- 10005: 账号已被禁用
- 10007: 登录尝试次数过多

**书签操作**
- 30001: URL格式错误
- 30002: 书签已存在
- 30003: 批量操作超限

**文件操作**
- 50001: 文件格式不支持
- 50002: 文件过大

## 9. 附录

### 9.1 错误码生成工具

提供错误码生成脚本，确保错误码的唯一性：

```bash
# 生成新的错误码
php think error:generate --module=user --type=validation

# 检查错误码冲突
php think error:check

# 生成错误码文档
php think error:docs
```

### 9.2 错误码迁移指南

当需要调整错误码体系时的迁移步骤：

1. 发布迁移公告
2. 新旧错误码并存期
3. 客户端适配
4. 监控迁移进度
5. 废弃旧错误码

### 9.3 相关工具和资源

- 错误码在线查询工具
- 错误码SDK（各语言版本）
- 错误处理最佳实践视频教程
- 错误码变更日志订阅 