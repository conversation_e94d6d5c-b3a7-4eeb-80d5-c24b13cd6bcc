import { defineStore } from 'pinia'
import { http } from '@/utils/request'

interface ImportOptions {
  folderId?: number | null
  duplicateAction: 'skip' | 'update' | 'create'
}

interface ExportOptions {
  format: 'html' | 'json' | 'csv' | 'generic_json'
  scope: 'all' | 'folder' | 'starred' | 'tag'
  folderId?: number | null
  tagId?: number | null
  includes: string[]
}

interface ImportProgress {
  percentage: number
  message: string
  success: number
  failed: number
  skipped: number
}

export const useImportExportStore = defineStore('importExport', () => {
  // 状态
  const importing = ref(false)
  const exporting = ref(false)
  const importAbortController = ref<AbortController | null>(null)

  // 导入书签
  const importBookmarks = async (
    file: File, 
    options: ImportOptions,
    onProgress?: (progress: ImportProgress) => void
  ) => {
    try {
      importing.value = true
      importAbortController.value = new AbortController()
      
      const formData = new FormData()
      formData.append('file', file)
      formData.append('folder_id', options.folderId?.toString() || '')
      formData.append('duplicate_action', options.duplicateAction)
      
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        if (onProgress) {
          onProgress({
            percentage: Math.min(90, Math.random() * 80 + 10),
            message: '正在导入书签...',
            success: Math.floor(Math.random() * 50),
            failed: Math.floor(Math.random() * 5),
            skipped: Math.floor(Math.random() * 10)
          })
        }
      }, 500)
      
      const response = await http.post('/import/bookmarks', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        signal: importAbortController.value.signal,
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            onProgress({
              percentage: Math.min(percentage, 95),
              message: '正在上传文件...',
              success: 0,
              failed: 0,
              skipped: 0
            })
          }
        }
      })
      
      clearInterval(progressInterval)
      
      if (onProgress) {
        onProgress({
          percentage: 100,
          message: '导入完成',
          success: response.data.success || 0,
          failed: response.data.failed || 0,
          skipped: response.data.skipped || 0
        })
      }
      
      return response.data
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('导入已取消')
      }
      console.error('导入书签失败:', error)
      throw error
    } finally {
      importing.value = false
      importAbortController.value = null
    }
  }

  // 导出书签
  const exportBookmarks = async (options: ExportOptions): Promise<Blob> => {
    try {
      exporting.value = true
      
      const params = {
        format: options.format,
        scope: options.scope,
        folder_id: options.folderId,
        tag_id: options.tagId,
        includes: options.includes.join(',')
      }
      
      const response = await http.get('/export/bookmarks', {
        params,
        responseType: 'blob'
      })
      
      return response.data
    } catch (error) {
      console.error('导出书签失败:', error)
      throw error
    } finally {
      exporting.value = false
    }
  }

  // 取消导入
  const cancelImport = () => {
    if (importAbortController.value) {
      importAbortController.value.abort()
    }
  }

  // 获取导入历史
  const getImportHistory = async () => {
    try {
      const response = await http.get('/import/history')
      return response.data
    } catch (error) {
      console.error('获取导入历史失败:', error)
      throw error
    }
  }

  // 获取导出历史
  const getExportHistory = async () => {
    try {
      const response = await http.get('/export/history')
      return response.data
    } catch (error) {
      console.error('获取导出历史失败:', error)
      throw error
    }
  }

  // 解析导入文件预览
  const parseImportFile = async (file: File) => {
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await http.post('/import/preview', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      return response.data
    } catch (error) {
      console.error('解析导入文件失败:', error)
      throw error
    }
  }

  // 验证导入文件格式
  const validateImportFile = (file: File): { valid: boolean; message?: string } => {
    const allowedTypes = [
      'text/html',
      'application/json',
      'text/csv',
      'application/x-plist'
    ]
    
    const allowedExtensions = ['.html', '.json', '.csv', '.plist']
    
    // 检查文件大小 (最大 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return {
        valid: false,
        message: '文件大小不能超过 10MB'
      }
    }
    
    // 检查文件类型
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
    if (!allowedExtensions.includes(fileExtension)) {
      return {
        valid: false,
        message: '不支持的文件格式，请选择 .html, .json, .csv 或 .plist 文件'
      }
    }
    
    return { valid: true }
  }

  // 获取支持的导入格式
  const getSupportedImportFormats = () => {
    return [
      {
        name: 'Chrome/Edge 书签',
        extension: '.html',
        description: 'Chrome 或 Edge 浏览器导出的 HTML 格式书签文件'
      },
      {
        name: 'Firefox 书签',
        extension: '.json',
        description: 'Firefox 浏览器导出的 JSON 格式书签文件'
      },
      {
        name: 'Safari 书签',
        extension: '.plist',
        description: 'Safari 浏览器导出的 plist 格式书签文件'
      },
      {
        name: 'CSV 格式',
        extension: '.csv',
        description: '通用的 CSV 格式，包含标题、URL、描述等字段'
      },
      {
        name: '通用 JSON',
        extension: '.json',
        description: '标准的 JSON 格式，适用于其他书签管理工具'
      }
    ]
  }

  // 获取支持的导出格式
  const getSupportedExportFormats = () => {
    return [
      {
        value: 'html',
        label: 'HTML (Chrome/Edge)',
        description: '适用于 Chrome、Edge 等浏览器导入'
      },
      {
        value: 'json',
        label: 'JSON (Firefox)',
        description: '适用于 Firefox 浏览器导入'
      },
      {
        value: 'csv',
        label: 'CSV',
        description: '通用的表格格式，可用 Excel 打开'
      },
      {
        value: 'generic_json',
        label: '通用 JSON',
        description: '标准 JSON 格式，适用于其他工具'
      }
    ]
  }

  return {
    // 状态
    importing: readonly(importing),
    exporting: readonly(exporting),
    
    // 方法
    importBookmarks,
    exportBookmarks,
    cancelImport,
    getImportHistory,
    getExportHistory,
    parseImportFile,
    validateImportFile,
    getSupportedImportFormats,
    getSupportedExportFormats
  }
})
