<template>
  <el-form-item 
    :label="label" 
    :prop="prop"
    :required="required"
    :error="errorMessage"
    :label-width="labelWidth"
  >
    <el-select
      v-model="selectValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :clearable="clearable"
      :filterable="filterable"
      :multiple="multiple"
      :multiple-limit="multipleLimit"
      :size="size"
      :loading="loading"
      :remote="remote"
      :remote-method="remoteMethod"
      :reserve-keyword="reserveKeyword"
      :no-data-text="noDataText"
      :no-match-text="noMatchText"
      :loading-text="loadingText"
      @change="handleChange"
      @visible-change="handleVisibleChange"
      @remove-tag="handleRemoveTag"
      @clear="handleClear"
      @blur="handleBlur"
      @focus="handleFocus"
    >
      <el-option
        v-for="option in options"
        :key="getOptionKey(option)"
        :label="getOptionLabel(option)"
        :value="getOptionValue(option)"
        :disabled="getOptionDisabled(option)"
      >
        <slot name="option" :option="option">
          {{ getOptionLabel(option) }}
        </slot>
      </el-option>
      
      <template #empty v-if="$slots.empty">
        <slot name="empty" />
      </template>
    </el-select>
  </el-form-item>
</template>

<script setup lang="ts">
import type { ComponentSize } from 'element-plus'

interface Option {
  [key: string]: any
}

interface Props {
  modelValue: any
  label?: string
  prop?: string
  placeholder?: string
  disabled?: boolean
  clearable?: boolean
  filterable?: boolean
  multiple?: boolean
  multipleLimit?: number
  size?: ComponentSize
  loading?: boolean
  remote?: boolean
  remoteMethod?: (query: string) => void
  reserveKeyword?: boolean
  options: Option[]
  optionLabel?: string
  optionValue?: string
  optionKey?: string
  optionDisabled?: string
  noDataText?: string
  noMatchText?: string
  loadingText?: string
  required?: boolean
  labelWidth?: string
  validator?: (value: any) => string | null
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
  (e: 'visible-change', visible: boolean): void
  (e: 'remove-tag', value: any): void
  (e: 'clear'): void
  (e: 'blur', event: FocusEvent): void
  (e: 'focus', event: FocusEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  clearable: true,
  filterable: false,
  multiple: false,
  multipleLimit: 0,
  loading: false,
  remote: false,
  reserveKeyword: false,
  optionLabel: 'label',
  optionValue: 'value',
  optionKey: 'value',
  optionDisabled: 'disabled',
  noDataText: '无数据',
  noMatchText: '无匹配数据',
  loadingText: '加载中...',
  required: false
})

const emit = defineEmits<Emits>()

const errorMessage = ref<string>('')

const selectValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const getOptionLabel = (option: Option) => {
  return option[props.optionLabel] || option
}

const getOptionValue = (option: Option) => {
  return option[props.optionValue] !== undefined ? option[props.optionValue] : option
}

const getOptionKey = (option: Option) => {
  return option[props.optionKey] !== undefined ? option[props.optionKey] : option
}

const getOptionDisabled = (option: Option) => {
  return option[props.optionDisabled] || false
}

const validateValue = (value: any) => {
  if (props.validator) {
    const error = props.validator(value)
    errorMessage.value = error || ''
    return !error
  }
  return true
}

const handleChange = (value: any) => {
  validateValue(value)
  emit('change', value)
}

const handleVisibleChange = (visible: boolean) => {
  emit('visible-change', visible)
}

const handleRemoveTag = (value: any) => {
  emit('remove-tag', value)
}

const handleClear = () => {
  errorMessage.value = ''
  emit('clear')
}

const handleBlur = (event: FocusEvent) => {
  validateValue(selectValue.value)
  emit('blur', event)
}

const handleFocus = (event: FocusEvent) => {
  errorMessage.value = ''
  emit('focus', event)
}

// 暴露验证方法
defineExpose({
  validate: () => validateValue(selectValue.value),
  clearValidation: () => {
    errorMessage.value = ''
  }
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}

.el-select {
  width: 100%;
}
</style>
