<?php

namespace app\controller\api\v1;

use app\BaseController;
use app\service\StatsService;
use app\validate\StatsValidate;
use app\exception\BusinessException;
use think\Response;

/**
 * 统计分析控制器
 *
 * 提供书签使用统计、标签分析、访问记录等数据分析功能
 */
class Stats extends BaseController
{
    /**
     * 统计服务
     * @var StatsService
     */
    protected $statsService;

    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->statsService = new StatsService();
    }

    /**
     * 统计概览
     *
     * 获取用户书签管理系统的整体统计数据概览
     *
     * @route GET /api/v1/stats/overview
     * @middleware auth
     * @param int $days 统计天数，默认30天，最大365天
     * @return Response JSON响应，包含统计概览数据
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/stats/overview?days=30
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取统计概览成功",
     *   "data": {
     *     "total_bookmarks": 1250,
     *     "total_folders": 25,
     *     "total_tags": 85,
     *     "star_bookmarks": 156,
     *     "recent_added": 23,
     *     "recent_visited": 67,
     *     "growth_rate": 12.5,
     *     "most_used_tags": [...],
     *     "folder_distribution": [...],
     *     "daily_activity": [...]
     *   }
     * }
     */
    public function overview(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $days = (int)($this->request->get('days', 30));
            $days = min(max($days, 1), 365); // 限制在1-365天之间

            $overview = $this->statsService->getOverviewStats($userId, $days);

            return $this->success($overview, '获取统计概览成功');
        } catch (\Exception $e) {
            return $this->error('获取统计概览失败：' . $e->getMessage());
        }
    }

    /**
     * 标签统计
     *
     * 获取标签使用情况的详细统计数据
     *
     * @route GET /api/v1/stats/tags
     * @middleware auth
     * @param int $limit 返回数量限制，默认20，最大100
     * @param string $sort 排序方式：usage|name|created，默认usage
     * @param string $order 排序顺序：asc|desc，默认desc
     * @return Response JSON响应，包含标签统计数据
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/stats/tags?limit=20&sort=usage&order=desc
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取标签统计成功",
     *   "data": {
     *     "total_tags": 85,
     *     "active_tags": 67,
     *     "unused_tags": 18,
     *     "avg_usage": 8.5,
     *     "tags": [
     *       {
     *         "id": 1,
     *         "name": "技术",
     *         "usage_count": 45,
     *         "percentage": 15.2,
     *         "recent_usage": 8,
     *         "created_at": "2024-01-15 10:30:00"
     *       }
     *     ]
     *   }
     * }
     */
    public function tags(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $params = $this->request->get();
            $tagStats = $this->statsService->getTagStats($userId, $params);

            return $this->success($tagStats, '获取标签统计成功');
        } catch (\Exception $e) {
            return $this->error('获取标签统计失败：' . $e->getMessage());
        }
    }

    /**
     * 访问统计
     *
     * 获取书签访问记录和使用频率统计
     *
     * @route GET /api/v1/stats/visits
     * @middleware auth
     * @param int $days 统计天数，默认30天
     * @param string $type 统计类型：daily|weekly|monthly，默认daily
     * @param int $limit 返回数量限制，默认20
     * @return Response JSON响应，包含访问统计数据
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/stats/visits?days=30&type=daily&limit=20
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取访问统计成功",
     *   "data": {
     *     "total_visits": 1250,
     *     "unique_bookmarks": 456,
     *     "avg_daily_visits": 41.7,
     *     "most_visited": [...],
     *     "visit_trends": [...],
     *     "time_distribution": [...]
     *   }
     * }
     */
    public function visits(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $params = $this->request->get();
            $visitStats = $this->statsService->getVisitStats($userId, $params);

            return $this->success($visitStats, '获取访问统计成功');
        } catch (\Exception $e) {
            return $this->error('获取访问统计失败：' . $e->getMessage());
        }
    }

    /**
     * 生成报告
     *
     * 生成用户书签使用情况的详细报告
     *
     * @route POST /api/v1/stats/report
     * @middleware auth
     * @param string $type 报告类型：summary|detailed|export，默认summary
     * @param int $days 统计天数，默认30天
     * @param string $format 报告格式：json|pdf|excel，默认json
     * @param array $sections 包含的报告部分，可选
     * @return Response JSON响应，包含生成的报告或下载链接
     * @throws BusinessException 参数验证失败
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/stats/report
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "type": "detailed",
     *   "days": 30,
     *   "format": "json",
     *   "sections": ["overview", "tags", "visits", "trends"]
     * }
     * Response: {
     *   "code": 200,
     *   "message": "生成报告成功",
     *   "data": {
     *     "report_id": "report_20240630_123456",
     *     "type": "detailed",
     *     "format": "json",
     *     "generated_at": "2024-06-30 12:34:56",
     *     "download_url": "/api/v1/stats/download/report_20240630_123456",
     *     "expires_at": "2024-07-01 12:34:56",
     *     "summary": {
     *       "total_bookmarks": 1250,
     *       "report_period": "2024-06-01 to 2024-06-30",
     *       "sections_included": 4
     *     }
     *   }
     * }
     */
    public function report(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            // 获取参数
            $params = $this->request->post();
            $params['user_id'] = $userId;

            // 参数验证
            $this->validate($params, StatsValidate::class . '.report');

            // 生成报告
            $report = $this->statsService->generateReport($params);

            return $this->success($report, '生成报告成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('生成报告失败：' . $e->getMessage());
        }
    }
}
