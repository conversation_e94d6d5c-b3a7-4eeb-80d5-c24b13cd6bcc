<?php

namespace app\model;

use think\Model;

/**
 * 用户认证令牌模型
 */
class UserToken extends Model
{
    // 表名
    protected $name = 'user_tokens';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = 'created_at';

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'user_id' => 'integer',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
    ];

    // 隐藏字段
    protected $hidden = [
        'token'
    ];

    /**
     * 令牌类型常量
     */
    const TYPE_REFRESH = 'refresh';

    /**
     * 关联用户
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 创建刷新令牌记录
     * @param int $userId 用户ID
     * @param string $token 令牌值
     * @param int $expiresIn 过期时间（秒）
     * @param string $deviceInfo 设备信息
     * @return UserToken
     */
    public static function createRefreshToken(int $userId, string $token, int $expiresIn, string $deviceInfo = ''): UserToken
    {
        $model = new self();
        $model->user_id = $userId;
        $model->token_type = self::TYPE_REFRESH;
        $model->token = $token;
        $model->expires_at = date('Y-m-d H:i:s', time() + $expiresIn);
        $model->device_info = $deviceInfo;
        $model->save();

        return $model;
    }

    /**
     * 根据令牌查找记录
     * @param string $token 令牌值
     * @param string $type 令牌类型
     * @return UserToken|null
     */
    public static function findByToken(string $token, string $type = self::TYPE_REFRESH): ?UserToken
    {
        return self::where('token', $token)
            ->where('token_type', $type)
            ->where('expires_at', '>', date('Y-m-d H:i:s'))
            ->find();
    }

    /**
     * 检查令牌是否有效
     * @param string $token 令牌值
     * @param string $type 令牌类型
     * @return bool
     */
    public static function isValidToken(string $token, string $type = self::TYPE_REFRESH): bool
    {
        return self::findByToken($token, $type) !== null;
    }

    /**
     * 撤销令牌
     * @param string $token 令牌值
     * @param string $type 令牌类型
     * @return bool
     */
    public static function revokeToken(string $token, string $type = self::TYPE_REFRESH): bool
    {
        return self::where('token', $token)
            ->where('token_type', $type)
            ->delete() > 0;
    }

    /**
     * 撤销用户的所有令牌
     * @param int $userId 用户ID
     * @param string|null $type 令牌类型，null表示所有类型
     * @return int 撤销的数量
     */
    public static function revokeUserTokens(int $userId, ?string $type = null): int
    {
        $query = self::where('user_id', $userId);
        
        if ($type !== null) {
            $query->where('token_type', $type);
        }
        
        return $query->delete();
    }

    /**
     * 撤销用户的其他设备令牌（保留当前设备）
     * @param int $userId 用户ID
     * @param string $currentToken 当前令牌
     * @param string $type 令牌类型
     * @return int 撤销的数量
     */
    public static function revokeOtherDeviceTokens(int $userId, string $currentToken, string $type = self::TYPE_REFRESH): int
    {
        return self::where('user_id', $userId)
            ->where('token_type', $type)
            ->where('token', '<>', $currentToken)
            ->delete();
    }

    /**
     * 清理过期令牌
     * @param int|null $userId 用户ID，null表示清理所有用户
     * @return int 清理的数量
     */
    public static function cleanExpiredTokens(?int $userId = null): int
    {
        $query = self::where('expires_at', '<=', date('Y-m-d H:i:s'));
        
        if ($userId !== null) {
            $query->where('user_id', $userId);
        }
        
        return $query->delete();
    }

    /**
     * 获取用户的有效令牌数量
     * @param int $userId 用户ID
     * @param string $type 令牌类型
     * @return int
     */
    public static function getUserValidTokenCount(int $userId, string $type = self::TYPE_REFRESH): int
    {
        return self::where('user_id', $userId)
            ->where('token_type', $type)
            ->where('expires_at', '>', date('Y-m-d H:i:s'))
            ->count();
    }

    /**
     * 限制用户的令牌数量（删除最旧的令牌）
     * @param int $userId 用户ID
     * @param int $maxTokens 最大令牌数量
     * @param string $type 令牌类型
     * @return int 删除的数量
     */
    public static function limitUserTokens(int $userId, int $maxTokens = 5, string $type = self::TYPE_REFRESH): int
    {
        $tokens = self::where('user_id', $userId)
            ->where('token_type', $type)
            ->where('expires_at', '>', date('Y-m-d H:i:s'))
            ->order('created_at', 'desc')
            ->select();

        if ($tokens->count() <= $maxTokens) {
            return 0;
        }

        // 保留最新的令牌，删除多余的
        $tokensToDelete = $tokens->slice($maxTokens);
        $deleteCount = 0;

        foreach ($tokensToDelete as $token) {
            if ($token->delete()) {
                $deleteCount++;
            }
        }

        return $deleteCount;
    }

    /**
     * 检查令牌是否即将过期
     * @param int $threshold 阈值（秒），默认1小时
     * @return bool
     */
    public function isExpiringSoon(int $threshold = 3600): bool
    {
        // 确保expires_at是DateTime对象
        $expiresAt = $this->getExpiresAtTimestamp();
        $remainingTime = $expiresAt - time();
        return $remainingTime <= $threshold;
    }

    /**
     * 获取令牌剩余有效时间（秒）
     * @return int
     */
    public function getRemainingTime(): int
    {
        // 确保expires_at是DateTime对象
        $expiresAt = $this->getExpiresAtTimestamp();
        return max(0, $expiresAt - time());
    }

    /**
     * 获取expires_at的时间戳
     * @return int
     */
    private function getExpiresAtTimestamp(): int
    {
        $expiresAt = $this->expires_at;

        // 如果是字符串，转换为时间戳
        if (is_string($expiresAt)) {
            return strtotime($expiresAt);
        }

        // 如果是DateTime对象，获取时间戳
        if ($expiresAt instanceof \DateTime) {
            return $expiresAt->getTimestamp();
        }

        // 如果是Carbon对象（ThinkPHP可能使用）
        if (method_exists($expiresAt, 'getTimestamp')) {
            return $expiresAt->getTimestamp();
        }

        // 如果是数字，直接返回
        if (is_numeric($expiresAt)) {
            return (int)$expiresAt;
        }

        // 默认返回当前时间（表示已过期）
        return time() - 1;
    }

    /**
     * 转换为API输出格式
     * @return array
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'token_type' => $this->token_type,
            'device_info' => $this->device_info,
            'expires_at' => $this->formatDateTime($this->expires_at),
            'created_at' => $this->formatDateTime($this->created_at),
            'is_expiring_soon' => $this->isExpiringSoon(),
            'remaining_time' => $this->getRemainingTime(),
        ];
    }

    /**
     * 格式化日期时间
     * @param mixed $datetime 日期时间值
     * @return string
     */
    private function formatDateTime($datetime): string
    {
        if (empty($datetime)) {
            return '';
        }

        // 如果是字符串，直接返回
        if (is_string($datetime)) {
            return $datetime;
        }

        // 如果是DateTime对象，格式化输出
        if ($datetime instanceof \DateTime) {
            return $datetime->format('Y-m-d H:i:s');
        }

        // 如果有format方法（Carbon等）
        if (method_exists($datetime, 'format')) {
            return $datetime->format('Y-m-d H:i:s');
        }

        // 如果是时间戳
        if (is_numeric($datetime)) {
            return date('Y-m-d H:i:s', $datetime);
        }

        return (string)$datetime;
    }
}
