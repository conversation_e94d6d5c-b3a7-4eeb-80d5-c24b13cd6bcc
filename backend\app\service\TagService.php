<?php

namespace app\service;

use app\model\Tag;
use app\model\BookmarkTag;
use app\exception\BusinessException;
use think\facade\Db;

class TagService
{
    public function createTag(int $userId, array $data): Tag
    {
        if (Tag::nameExists($data['name'], $userId)) {
            throw BusinessException::tagNameExists();
        }

        $tag = new Tag();
        $tag->user_id = $userId;
        $tag->name = $data['name'];
        $tag->color = $data['color'] ?? null;
        $tag->usage_count = 0;

        if (!$tag->save()) {
            throw new BusinessException('创建标签失败');
        }

        return $tag;
    }

    public function updateTag(int $userId, int $tagId, array $data): Tag
    {
        $tag = Tag::where('id', $tagId)->where('user_id', $userId)->find();
        if (!$tag) {
            throw BusinessException::tagNotFound();
        }

        if (isset($data['name']) && $data['name'] !== $tag->name) {
            if (Tag::nameExists($data['name'], $userId, $tagId)) {
                throw BusinessException::tagNameExists();
            }
        }

        $allowedFields = ['name', 'color'];
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $tag->$field = $data[$field];
            }
        }

        if (!$tag->save()) {
            throw new BusinessException('更新标签失败');
        }

        return $tag;
    }

    public function deleteTag(int $userId, int $tagId): bool
    {
        $tag = Tag::where('id', $tagId)->where('user_id', $userId)->find();
        if (!$tag) {
            throw BusinessException::tagNotFound();
        }

        Db::startTrans();
        try {
            // 删除书签标签关联
            BookmarkTag::where('tag_id', $tagId)->delete();
            
            // 删除标签
            $tag->delete();

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw new BusinessException('删除标签失败：' . $e->getMessage());
        }
    }

    public function getTagList(int $userId, array $params = []): array
    {
        $query = Tag::where('user_id', $userId);

        if (!empty($params['search'])) {
            $query->where('name', 'like', '%' . $params['search'] . '%');
        }

        $orderBy = $params['order_by'] ?? 'usage_count';
        $orderDir = $params['order_dir'] ?? 'desc';
        $query->order($orderBy, $orderDir);

        $tags = $query->select();
        return $tags->map(function($tag) {
            return $tag->toApiArray();
        })->toArray();
    }

    public function getTagCloud(int $userId): array
    {
        $tags = Tag::where('user_id', $userId)
            ->where('usage_count', '>', 0)
            ->order('usage_count', 'desc')
            ->limit(50)
            ->select();

        $maxCount = $tags->max('usage_count') ?: 1;
        
        return $tags->map(function($tag) use ($maxCount) {
            $data = $tag->toApiArray();
            $data['weight'] = round(($tag->usage_count / $maxCount) * 100);
            return $data;
        })->toArray();
    }

    public function mergeTags(int $userId, array $sourceTagIds, int $targetTagId): bool
    {
        $targetTag = Tag::where('id', $targetTagId)->where('user_id', $userId)->find();
        if (!$targetTag) {
            throw BusinessException::tagNotFound();
        }

        Db::startTrans();
        try {
            foreach ($sourceTagIds as $sourceTagId) {
                if ($sourceTagId == $targetTagId) continue;

                $sourceTag = Tag::where('id', $sourceTagId)->where('user_id', $userId)->find();
                if (!$sourceTag) continue;

                // 将源标签的书签关联转移到目标标签
                $bookmarkTags = BookmarkTag::where('tag_id', $sourceTagId)->select();
                foreach ($bookmarkTags as $bookmarkTag) {
                    // 检查是否已存在关联
                    $exists = BookmarkTag::where('bookmark_id', $bookmarkTag->bookmark_id)
                        ->where('tag_id', $targetTagId)
                        ->find();
                    
                    if (!$exists) {
                        $bookmarkTag->tag_id = $targetTagId;
                        $bookmarkTag->save();
                        $targetTag->incrementUsage();
                    } else {
                        $bookmarkTag->delete();
                    }
                }

                // 删除源标签
                $sourceTag->delete();
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw new BusinessException('合并标签失败：' . $e->getMessage());
        }
    }
}
