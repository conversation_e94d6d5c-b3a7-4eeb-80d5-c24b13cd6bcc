<?php

namespace app\service;

use app\model\User;
use app\model\UserToken;
use app\common\JwtAuth;
use app\exception\BusinessException;
use think\facade\Db;

/**
 * 认证服务类
 */
class AuthService
{
    /**
     * 用户注册
     * @param array $data 注册数据
     * @return User
     * @throws BusinessException
     */
    public function register(array $data): User
    {
        // 检查邮箱是否已存在
        if (User::emailExists($data['email'])) {
            throw BusinessException::userEmailExists();
        }

        // 检查用户名是否已存在
        if (User::usernameExists($data['username'])) {
            throw BusinessException::userUsernameExists();
        }

        // 创建用户
        $user = new User();
        $user->email = $data['email'];
        $user->username = $data['username'];
        $user->password = $data['password']; // 模型会自动加密

        // 如果数据库表有nickname字段，则设置昵称
        if (isset($data['nickname']) && !empty($data['nickname'])) {
            try {
                $user->nickname = $data['nickname'];
            } catch (\Exception $e) {
                // 如果nickname字段不存在，忽略错误
            }
        }

        $user->avatar = $data['avatar'] ?? null;
        $user->status = 1; // 默认启用

        if (!$user->save()) {
            throw new BusinessException('用户注册失败');
        }

        // 重新加载用户数据，确保时间戳等字段正确
        $user->refresh();

        return $user;
    }

    /**
     * 用户登录
     * @param string $account 账号（邮箱或用户名）
     * @param string $password 密码
     * @param string $deviceInfo 设备信息
     * @return array 包含用户信息和令牌
     * @throws BusinessException
     */
    public function login(string $account, string $password, string $deviceInfo = ''): array
    {
        // 查找用户
        $user = User::findByAccount($account);
        if (!$user) {
            throw BusinessException::userLoginFailed();
        }

        // 检查用户状态
        if ($user->status !== 1) {
            throw BusinessException::userDisabled();
        }

        // 验证密码
        if (!$user->verifyPassword($password)) {
            throw BusinessException::userLoginFailed();
        }

        // 生成令牌
        $tokens = $this->generateTokens($user->id, $deviceInfo);

        // 更新最后登录信息
        $user->updateLastLogin();

        // 清理过期令牌
        $user->cleanExpiredTokens();

        // 限制令牌数量（最多5个设备同时登录）
        UserToken::limitUserTokens($user->id, 5);

        return [
            'user' => $user->toApiArray(true),
            'access_token' => $tokens['access_token'],
            'refresh_token' => $tokens['refresh_token'],
            'expires_in' => $tokens['expires_in']
        ];
    }

    /**
     * 刷新访问令牌
     * @param string $refreshToken 刷新令牌
     * @return array 新的令牌信息
     * @throws BusinessException
     */
    public function refreshToken(string $refreshToken): array
    {
        // 验证刷新令牌
        $payload = JwtAuth::validateRefreshToken($refreshToken);
        if (!$payload) {
            throw BusinessException::unauthorized('刷新令牌无效或已过期');
        }

        // 检查数据库中的令牌记录
        $tokenRecord = UserToken::findByToken($refreshToken);
        if (!$tokenRecord) {
            throw BusinessException::unauthorized('刷新令牌不存在或已被撤销');
        }

        // 检查用户是否存在且状态正常
        $user = User::find($tokenRecord->user_id);
        if (!$user || $user->status !== 1) {
            throw BusinessException::userDisabled();
        }

        // 生成新的访问令牌
        $accessToken = JwtAuth::generateAccessToken($user->id);

        // 如果刷新令牌即将过期，生成新的刷新令牌
        $newRefreshToken = null;
        if ($tokenRecord->isExpiringSoon(86400)) { // 24小时内过期
            $newRefreshToken = JwtAuth::generateRefreshToken($user->id, $tokenRecord->device_info);
            
            // 删除旧的刷新令牌
            $tokenRecord->delete();
            
            // 保存新的刷新令牌
            UserToken::createRefreshToken(
                $user->id,
                $newRefreshToken,
                (int)env('JWT_REFRESH_TTL', 604800),
                $tokenRecord->device_info
            );
        }

        return [
            'access_token' => $accessToken,
            'refresh_token' => $newRefreshToken ?: $refreshToken,
            'expires_in' => (int)env('JWT_TTL', 2592000),
            'token_type' => 'Bearer'
        ];
    }

    /**
     * 用户登出
     * @param int $userId 用户ID
     * @param string|null $refreshToken 刷新令牌
     * @param bool $allDevices 是否登出所有设备
     * @return bool
     */
    public function logout(int $userId, ?string $refreshToken = null, bool $allDevices = false): bool
    {
        if ($allDevices) {
            // 撤销用户的所有令牌
            UserToken::revokeUserTokens($userId);
        } elseif ($refreshToken) {
            // 撤销指定的刷新令牌
            UserToken::revokeToken($refreshToken);
        }

        return true;
    }

    /**
     * 生成访问令牌和刷新令牌
     * @param int $userId 用户ID
     * @param string $deviceInfo 设备信息
     * @return array
     */
    private function generateTokens(int $userId, string $deviceInfo = ''): array
    {
        // 生成访问令牌
        $accessToken = JwtAuth::generateAccessToken($userId);

        // 生成刷新令牌
        $refreshToken = JwtAuth::generateRefreshToken($userId, $deviceInfo);

        // 保存刷新令牌到数据库
        UserToken::createRefreshToken(
            $userId,
            $refreshToken,
            (int)env('JWT_REFRESH_TTL', 604800),
            $deviceInfo
        );

        return [
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken,
            'expires_in' => (int)env('JWT_TTL', 2592000),
            'token_type' => 'Bearer'
        ];
    }

    /**
     * 验证访问令牌
     * @param string $token 访问令牌
     * @return array|false 用户信息或false
     */
    public function validateAccessToken(string $token)
    {
        $payload = JwtAuth::validateAccessToken($token);
        if (!$payload) {
            return false;
        }

        // 检查用户是否存在且状态正常
        $user = User::find($payload['user_id']);
        if (!$user || $user->status !== 1) {
            return false;
        }

        return [
            'user_id' => $user->id,
            'user' => $user->toApiArray(),
            'payload' => $payload
        ];
    }

    /**
     * 修改密码
     * @param int $userId 用户ID
     * @param string $oldPassword 旧密码
     * @param string $newPassword 新密码
     * @param bool $logoutOtherDevices 是否登出其他设备
     * @return bool
     * @throws BusinessException
     */
    public function changePassword(int $userId, string $oldPassword, string $newPassword, bool $logoutOtherDevices = true): bool
    {
        $user = User::find($userId);
        if (!$user) {
            throw BusinessException::userNotFound();
        }

        // 验证旧密码
        if (!$user->verifyPassword($oldPassword)) {
            throw new BusinessException('原密码错误', 10004);
        }

        // 更新密码
        $user->password = $newPassword; // 模型会自动加密
        if (!$user->save()) {
            throw new BusinessException('密码修改失败');
        }

        // 登出其他设备
        if ($logoutOtherDevices) {
            UserToken::revokeUserTokens($userId);
        }

        return true;
    }

    /**
     * 获取用户的活跃设备列表
     * @param int $userId 用户ID
     * @return array
     */
    public function getUserDevices(int $userId): array
    {
        $tokens = UserToken::where('user_id', $userId)
            ->where('expires_at', '>', date('Y-m-d H:i:s'))
            ->order('created_at', 'desc')
            ->select();

        return $tokens->map(function ($token) {
            return $token->toApiArray();
        })->toArray();
    }

    /**
     * 撤销指定设备的令牌
     * @param int $userId 用户ID
     * @param int $tokenId 令牌ID
     * @return bool
     * @throws BusinessException
     */
    public function revokeDevice(int $userId, int $tokenId): bool
    {
        $token = UserToken::where('id', $tokenId)
            ->where('user_id', $userId)
            ->find();

        if (!$token) {
            throw BusinessException::notFound('设备令牌不存在');
        }

        return $token->delete();
    }
}

