# 书签管理系统 API 测试文档

## 基础信息

- **API Base URL**: `https://vscode.qidian.cc:8000/api/v1`
- **本地测试URL**: `http://localhost:8000/api/v1`
- **认证方式**: JWT Bearer <PERSON>
- **响应格式**: JSON

## 系统接口测试

### 1. 获取系统配置
```bash
curl -X GET "http://localhost:8000/api/v1/system/config"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "获取系统配置成功",
  "data": {
    "app_name": "Web端书签管理系统",
    "version": "1.0.0",
    "api_version": "v1",
    "framework": "ThinkPHP 8.1.2",
    "features": {
      "user_auth": true,
      "folder_management": true,
      "tag_management": true,
      "bookmark_management": true,
      "search": true
    }
  }
}
```

### 2. 获取API状态
```bash
curl -X GET "http://localhost:8000/api/v1/system/status"
```

## 用户认证接口测试

### 1. 用户注册
```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "123456",
    "password_confirmation": "123456"
  }'
```

### 2. 用户登录
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "account": "<EMAIL>",
    "login_password": "123456"
  }'
```

**成功响应**:
```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "testuser"
    },
    "tokens": {
      "access_token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
      "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
      "expires_in": 7200,
      "token_type": "Bearer"
    }
  }
}
```

### 3. 获取用户信息
```bash
curl -X GET "http://localhost:8000/api/v1/user/profile" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 文件夹管理接口测试

### 1. 获取文件夹列表
```bash
curl -X GET "http://localhost:8000/api/v1/folders?type=tree" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. 创建文件夹
```bash
curl -X POST "http://localhost:8000/api/v1/folders" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "工作相关",
    "icon": "📁",
    "color": "#3498db"
  }'
```

## 标签管理接口测试

### 1. 获取标签列表
```bash
curl -X GET "http://localhost:8000/api/v1/tags" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. 创建标签
```bash
curl -X POST "http://localhost:8000/api/v1/tags" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "前端开发",
    "color": "#e74c3c"
  }'
```

### 3. 获取标签云
```bash
curl -X GET "http://localhost:8000/api/v1/tags/cloud" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 书签管理接口测试

### 1. 获取书签列表
```bash
curl -X GET "http://localhost:8000/api/v1/bookmarks?page=1&limit=20" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. 创建书签
```bash
curl -X POST "http://localhost:8000/api/v1/bookmarks" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Vue.js 官方文档",
    "url": "https://vuejs.org/",
    "description": "Vue.js 渐进式JavaScript框架",
    "folder_id": 1,
    "tags": ["前端", "Vue", "JavaScript"],
    "is_star": 1
  }'
```

### 3. 访问书签
```bash
curl -X POST "http://localhost:8000/api/v1/bookmarks/1/visit" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 搜索接口测试

### 1. 全文搜索
```bash
curl -X GET "http://localhost:8000/api/v1/search?q=Vue&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. 高级筛选
```bash
curl -X POST "http://localhost:8000/api/v1/search/filter" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "folder_id": 1,
    "tags": ["前端", "Vue"],
    "is_star": 1,
    "date_from": "2024-01-01",
    "date_to": "2024-12-31"
  }'
```

## 错误响应格式

所有错误响应都遵循统一格式：

```json
{
  "code": 40001,
  "message": "请求参数错误",
  "data": null,
  "timestamp": 1751262330,
  "errors": {
    "email": ["邮箱地址格式不正确"]
  }
}
```

## 常见错误码

- `0`: 成功
- `40001`: 请求参数错误
- `40101`: 未认证或认证失效
- `40301`: 无权限访问
- `40401`: 资源不存在
- `50001`: 服务器内部错误

## 测试建议

1. 首先测试系统配置和状态接口
2. 注册测试用户并获取访问令牌
3. 按顺序测试各个模块的CRUD操作
4. 测试搜索和筛选功能
5. 测试错误处理和边界情况

## 注意事项

- 所有需要认证的接口都必须在请求头中包含 `Authorization: Bearer TOKEN`
- 请求体使用 JSON 格式，需要设置 `Content-Type: application/json`
- 分页参数：`page`（页码，从1开始）、`limit`（每页数量，默认20）
- 时间格式：`YYYY-MM-DD HH:mm:ss`
