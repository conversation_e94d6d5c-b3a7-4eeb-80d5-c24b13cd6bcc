/**
 * 数据验证工具
 */

// 常用正则表达式
export const REGEX = {
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  PHONE: /^1[3-9]\d{9}$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  USERNAME: /^[a-zA-Z0-9_-]{3,20}$/,
  CHINESE: /^[\u4e00-\u9fa5]+$/,
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
}

/**
 * 基础验证函数
 */
export const validate = {
  // 必填验证
  required(value: any, message = '此字段为必填项'): string | null {
    if (value === null || value === undefined || value === '') {
      return message
    }
    if (Array.isArray(value) && value.length === 0) {
      return message
    }
    return null
  },

  // 邮箱验证
  email(value: string, message = '请输入正确的邮箱格式'): string | null {
    if (!value) return null
    return REGEX.EMAIL.test(value) ? null : message
  },

  // 手机号验证
  phone(value: string, message = '请输入正确的手机号'): string | null {
    if (!value) return null
    return REGEX.PHONE.test(value) ? null : message
  },

  // URL验证
  url(value: string, message = '请输入正确的URL格式'): string | null {
    if (!value) return null
    return REGEX.URL.test(value) ? null : message
  },

  // 密码强度验证
  password(value: string, message = '密码必须包含大小写字母和数字，至少8位'): string | null {
    if (!value) return null
    return REGEX.PASSWORD.test(value) ? null : message
  },

  // 用户名验证
  username(value: string, message = '用户名只能包含字母、数字、下划线和短横线，3-20位'): string | null {
    if (!value) return null
    return REGEX.USERNAME.test(value) ? null : message
  },

  // 长度验证
  length(value: string, min: number, max?: number, message?: string): string | null {
    if (!value) return null
    
    const len = value.length
    if (max !== undefined) {
      if (len < min || len > max) {
        return message || `长度必须在${min}-${max}个字符之间`
      }
    } else {
      if (len < min) {
        return message || `长度不能少于${min}个字符`
      }
    }
    return null
  },

  // 最小长度验证
  minLength(value: string, min: number, message?: string): string | null {
    if (!value) return null
    return value.length >= min ? null : (message || `长度不能少于${min}个字符`)
  },

  // 最大长度验证
  maxLength(value: string, max: number, message?: string): string | null {
    if (!value) return null
    return value.length <= max ? null : (message || `长度不能超过${max}个字符`)
  },

  // 数字验证
  number(value: any, message = '请输入有效的数字'): string | null {
    if (value === '' || value === null || value === undefined) return null
    return !isNaN(Number(value)) ? null : message
  },

  // 整数验证
  integer(value: any, message = '请输入整数'): string | null {
    if (value === '' || value === null || value === undefined) return null
    return Number.isInteger(Number(value)) ? null : message
  },

  // 范围验证
  range(value: number, min: number, max: number, message?: string): string | null {
    if (value === null || value === undefined) return null
    if (value < min || value > max) {
      return message || `值必须在${min}-${max}之间`
    }
    return null
  },

  // 最小值验证
  min(value: number, min: number, message?: string): string | null {
    if (value === null || value === undefined) return null
    return value >= min ? null : (message || `值不能小于${min}`)
  },

  // 最大值验证
  max(value: number, max: number, message?: string): string | null {
    if (value === null || value === undefined) return null
    return value <= max ? null : (message || `值不能大于${max}`)
  },

  // 正则表达式验证
  pattern(value: string, pattern: RegExp, message = '格式不正确'): string | null {
    if (!value) return null
    return pattern.test(value) ? null : message
  },

  // 确认密码验证
  confirm(value: string, target: string, message = '两次输入的密码不一致'): string | null {
    if (!value) return null
    return value === target ? null : message
  },

  // 身份证验证
  idCard(value: string, message = '请输入正确的身份证号'): string | null {
    if (!value) return null
    return REGEX.ID_CARD.test(value) ? null : message
  },

  // 中文验证
  chinese(value: string, message = '只能输入中文'): string | null {
    if (!value) return null
    return REGEX.CHINESE.test(value) ? null : message
  }
}

/**
 * 表单验证器类
 */
export class FormValidator {
  private rules: Record<string, Array<(value: any) => string | null>> = {}

  // 添加验证规则
  addRule(field: string, validator: (value: any) => string | null): this {
    if (!this.rules[field]) {
      this.rules[field] = []
    }
    this.rules[field].push(validator)
    return this
  }

  // 验证单个字段
  validateField(field: string, value: any): string | null {
    const fieldRules = this.rules[field]
    if (!fieldRules) return null

    for (const rule of fieldRules) {
      const error = rule(value)
      if (error) return error
    }
    return null
  }

  // 验证整个表单
  validate(data: Record<string, any>): Record<string, string> {
    const errors: Record<string, string> = {}

    for (const field in this.rules) {
      const error = this.validateField(field, data[field])
      if (error) {
        errors[field] = error
      }
    }

    return errors
  }

  // 检查是否有错误
  isValid(data: Record<string, any>): boolean {
    const errors = this.validate(data)
    return Object.keys(errors).length === 0
  }
}

/**
 * 常用验证器实例
 */
export const validators = {
  // 登录表单验证器
  login: new FormValidator()
    .addRule('account', (value) => validate.required(value, '请输入邮箱或用户名'))
    .addRule('password', (value) => validate.required(value, '请输入密码'))
    .addRule('password', (value) => validate.minLength(value, 6, '密码长度不能少于6位')),

  // 注册表单验证器
  register: new FormValidator()
    .addRule('username', (value) => validate.required(value, '请输入用户名'))
    .addRule('username', (value) => validate.username(value))
    .addRule('email', (value) => validate.required(value, '请输入邮箱'))
    .addRule('email', (value) => validate.email(value))
    .addRule('password', (value) => validate.required(value, '请输入密码'))
    .addRule('password', (value) => validate.minLength(value, 6, '密码长度不能少于6位')),

  // 书签表单验证器
  bookmark: new FormValidator()
    .addRule('title', (value) => validate.required(value, '请输入书签标题'))
    .addRule('title', (value) => validate.maxLength(value, 200, '标题长度不能超过200个字符'))
    .addRule('url', (value) => validate.required(value, '请输入书签URL'))
    .addRule('url', (value) => validate.url(value))
    .addRule('description', (value) => validate.maxLength(value, 500, '描述长度不能超过500个字符'))
}
