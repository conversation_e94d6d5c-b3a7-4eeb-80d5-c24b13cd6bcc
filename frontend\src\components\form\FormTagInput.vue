<template>
  <el-form-item 
    :label="label" 
    :prop="prop"
    :required="required"
    :error="errorMessage"
    :label-width="labelWidth"
  >
    <div class="tag-input-container">
      <div class="tag-list" v-if="tags.length > 0">
        <el-tag
          v-for="(tag, index) in tags"
          :key="index"
          :closable="!disabled"
          :size="size"
          :type="tagType"
          :effect="tagEffect"
          @close="removeTag(index)"
          class="tag-item"
        >
          {{ tag }}
        </el-tag>
      </div>
      
      <div class="tag-input" v-if="!disabled">
        <el-input
          v-model="inputValue"
          :placeholder="placeholder"
          :size="size"
          :maxlength="maxTagLength"
          @keydown.enter.prevent="addTag"
          @keydown.space.prevent="addTag"
          @keydown.tab.prevent="addTag"
          @keydown.backspace="handleBackspace"
          @blur="addTag"
          ref="inputRef"
          class="tag-input-field"
        />
        <div class="tag-input-hint" v-if="showHint">
          {{ hint }}
        </div>
      </div>
    </div>
  </el-form-item>
</template>

<script setup lang="ts">
import type { ComponentSize } from 'element-plus'

interface Props {
  modelValue?: string[]
  label?: string
  prop?: string
  placeholder?: string
  disabled?: boolean
  size?: ComponentSize
  tagType?: 'success' | 'info' | 'warning' | 'danger'
  tagEffect?: 'dark' | 'light' | 'plain'
  maxTags?: number
  maxTagLength?: number
  allowDuplicates?: boolean
  separator?: string | RegExp
  hint?: string
  showHint?: boolean
  required?: boolean
  labelWidth?: string
  validator?: (value: string[]) => string | null
}

interface Emits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'change', value: string[]): void
  (e: 'add', tag: string): void
  (e: 'remove', tag: string, index: number): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '输入标签后按回车或空格添加',
  disabled: false,
  size: 'default',
  tagType: 'info',
  tagEffect: 'light',
  maxTags: 10,
  maxTagLength: 20,
  allowDuplicates: false,
  separator: ',',
  hint: '按回车、空格或逗号分隔多个标签',
  showHint: true,
  required: false
})

const emit = defineEmits<Emits>()

const inputValue = ref('')
const inputRef = ref()
const errorMessage = ref<string>('')

const tags = computed({
  get: () => props.modelValue || [],
  set: (value) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

const validateValue = (value: string[]) => {
  if (props.validator) {
    const error = props.validator(value)
    errorMessage.value = error || ''
    return !error
  }
  return true
}

const addTag = () => {
  const value = inputValue.value.trim()
  if (!value) return

  // 使用分隔符分割多个标签
  const newTags = value.split(props.separator).map(tag => tag.trim()).filter(Boolean)
  
  for (const tag of newTags) {
    if (tag.length > props.maxTagLength) {
      errorMessage.value = `标签长度不能超过${props.maxTagLength}个字符`
      return
    }

    if (tags.value.length >= props.maxTags) {
      errorMessage.value = `最多只能添加${props.maxTags}个标签`
      return
    }

    if (!props.allowDuplicates && tags.value.includes(tag)) {
      continue
    }

    const newTagList = [...tags.value, tag]
    tags.value = newTagList
    emit('add', tag)
  }

  inputValue.value = ''
  errorMessage.value = ''
  validateValue(tags.value)
}

const removeTag = (index: number) => {
  const tag = tags.value[index]
  const newTagList = tags.value.filter((_, i) => i !== index)
  tags.value = newTagList
  emit('remove', tag, index)
  validateValue(tags.value)
}

const handleBackspace = () => {
  if (inputValue.value === '' && tags.value.length > 0) {
    removeTag(tags.value.length - 1)
  }
}

const focus = () => {
  inputRef.value?.focus()
}

// 暴露方法
defineExpose({
  focus,
  validate: () => validateValue(tags.value),
  clearValidation: () => {
    errorMessage.value = ''
  }
})
</script>

<style scoped>
.tag-input-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
  min-height: 40px;
  cursor: text;
  transition: border-color 0.2s;
}

.tag-input-container:hover {
  border-color: #c0c4cc;
}

.tag-input-container:focus-within {
  border-color: #409eff;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 6px;
}

.tag-list:last-child {
  margin-bottom: 0;
}

.tag-item {
  margin: 0;
}

.tag-input {
  flex: 1;
}

.tag-input-field {
  border: none;
  box-shadow: none;
}

.tag-input-field :deep(.el-input__wrapper) {
  border: none;
  box-shadow: none;
  padding: 0;
}

.tag-input-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.2;
}

.el-form-item {
  margin-bottom: 20px;
}
</style>
