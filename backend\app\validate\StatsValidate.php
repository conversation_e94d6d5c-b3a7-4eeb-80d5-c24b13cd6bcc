<?php

namespace app\validate;

use think\Validate;

/**
 * 统计验证器
 */
class StatsValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'type' => 'in:summary,detailed,export',
        'days' => 'integer|between:1,365',
        'format' => 'in:json,pdf,excel',
        'sections' => 'array',
        'sections.*' => 'in:overview,tags,visits,trends,folders',
        'limit' => 'integer|between:1,100',
        'sort' => 'in:usage,name,created,visits',
        'order' => 'in:asc,desc',
    ];

    /**
     * 验证消息
     */
    protected $message = [
        'type.in' => '报告类型不支持，仅支持summary、detailed、export',
        'days.integer' => '统计天数必须是整数',
        'days.between' => '统计天数必须在1-365天之间',
        'format.in' => '报告格式不支持，仅支持json、pdf、excel',
        'sections.array' => '报告部分必须是数组格式',
        'sections.*.in' => '报告部分值无效',
        'limit.integer' => '限制数量必须是整数',
        'limit.between' => '限制数量必须在1-100之间',
        'sort.in' => '排序字段值无效',
        'order.in' => '排序顺序值无效，仅支持asc、desc',
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'report' => ['type', 'days', 'format', 'sections', 'sections.*'],
        'tags' => ['limit', 'sort', 'order'],
        'visits' => ['days', 'type', 'limit'],
    ];

    /**
     * 自定义验证报告部分
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkSections($value, $rule, $data)
    {
        if (!is_array($value)) {
            return '报告部分必须是数组格式';
        }

        if (count($value) > 10) {
            return '报告部分最多不能超过10个';
        }

        $allowedSections = ['overview', 'tags', 'visits', 'trends', 'folders'];
        foreach ($value as $section) {
            if (!in_array($section, $allowedSections)) {
                return '报告部分值无效：' . $section;
            }
        }

        return true;
    }
}
