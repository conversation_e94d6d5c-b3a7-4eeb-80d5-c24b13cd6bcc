<?php

namespace app\controller\api\v1;

use app\BaseController;
use app\model\Bookmark;
use think\Response;

/**
 * 搜索功能控制器
 *
 * 提供全文搜索和高级筛选功能
 */
class Search extends BaseController
{
    /**
     * 全文搜索书签
     *
     * 在书签的标题、描述、URL中进行关键词搜索，支持分页
     *
     * @route GET /api/v1/search
     * @middleware auth
     * @param string $q 搜索关键词，必填
     * @param int $page 页码，默认1
     * @param int $limit 每页数量，默认20，最大100
     * @param string $type 搜索类型：all|title|url|description，默认all
     * @return Response JSON响应，包含搜索结果和分页信息
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/search?q=GitHub&page=1&limit=20&type=all
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "搜索完成",
     *   "data": {
     *     "list": [
     *       {
     *         "id": 1,
     *         "title": "GitHub",
     *         "url": "https://github.com",
     *         "description": "全球最大的代码托管平台",
     *         "tags": [...]
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 5,
     *       "last_page": 1
     *     },
     *     "keyword": "GitHub"
     *   }
     * }
     */
    public function index(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $keyword = $this->request->get('q', '');
            $page = max(1, (int)$this->request->get('page', 1));
            $limit = max(1, min(100, (int)$this->request->get('limit', 20)));

            if (empty($keyword)) {
                return $this->error('搜索关键词不能为空');
            }

            $query = Bookmark::where('user_id', $userId)
                ->with('tags')
                ->where(function($q) use ($keyword) {
                    $q->where('title', 'like', '%' . $keyword . '%')
                      ->whereOr('description', 'like', '%' . $keyword . '%')
                      ->whereOr('url', 'like', '%' . $keyword . '%');
                });

            $total = $query->count();
            $bookmarks = $query->page($page, $limit)->select();

            $result = [
                'items' => $bookmarks->map(function($bookmark) {
                    return $bookmark->toApiArray();
                })->toArray(),
                'meta' => [
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit),
                    'keyword' => $keyword
                ]
            ];

            return $this->paginate($result['items'], $result['meta'], '搜索完成');
        } catch (\Exception $e) {
            return $this->error('搜索失败：' . $e->getMessage());
        }
    }

    /**
     * 高级筛选书签
     *
     * 使用多种条件组合筛选书签，支持文件夹、标签、星标、日期范围等筛选
     *
     * @route POST /api/v1/search/filter
     * @middleware auth
     * @param string $keyword 关键词，可选
     * @param array $folder_ids 文件夹ID列表，可选
     * @param array $tag_ids 标签ID列表，可选
     * @param int $is_star 是否星标筛选：0-否，1-是，可选
     * @param int $is_dead 是否死链筛选：0-否，1-是，可选
     * @param array $date_range 日期范围筛选，包含start_date和end_date，可选
     * @param array $visit_count_range 访问次数范围筛选，包含min和max，可选
     * @param string $sort 排序方式：created_at|updated_at|visit_count|title，默认created_at
     * @param string $order 排序顺序：asc|desc，默认desc
     * @param int $page 页码，默认1
     * @param int $per_page 每页数量，默认20，最大100
     * @return Response JSON响应，包含筛选结果和分页信息
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/search/filter
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "keyword": "GitHub",
     *   "folder_ids": [1, 2],
     *   "tag_ids": [1, 3],
     *   "is_star": 1,
     *   "date_range": {
     *     "start_date": "2024-01-01",
     *     "end_date": "2024-06-30"
     *   },
     *   "sort": "visit_count",
     *   "order": "desc",
     *   "page": 1,
     *   "per_page": 20
     * }
     * Response: {
     *   "code": 200,
     *   "message": "筛选完成",
     *   "data": {
     *     "list": [...],
     *     "pagination": {...},
     *     "filters": {
     *       "applied_filters": {...}
     *     }
     *   }
     * }
     */
    public function filter(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $filters = $this->request->post();

            $query = Bookmark::where('user_id', $userId)->with('tags');

            // 应用各种筛选条件
            if (!empty($filters['folder_id'])) {
                $query->where('folder_id', $filters['folder_id']);
            }

            if (isset($filters['is_star'])) {
                $query->where('is_star', $filters['is_star']);
            }

            if (!empty($filters['tags'])) {
                $query->whereHas('tags', function($q) use ($filters) {
                    $q->whereIn('name', $filters['tags']);
                });
            }

            if (!empty($filters['date_from'])) {
                $query->where('created_at', '>=', $filters['date_from']);
            }

            if (!empty($filters['date_to'])) {
                $query->where('created_at', '<=', $filters['date_to']);
            }

            $page = max(1, (int)($filters['page'] ?? 1));
            $limit = max(1, min(100, (int)($filters['limit'] ?? 20)));
            
            $total = $query->count();
            $bookmarks = $query->page($page, $limit)->select();

            $result = [
                'items' => $bookmarks->map(function($bookmark) {
                    return $bookmark->toApiArray();
                })->toArray(),
                'meta' => [
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ]
            ];

            return $this->paginate($result['items'], $result['meta'], '筛选完成');
        } catch (\Exception $e) {
            return $this->error('筛选失败：' . $e->getMessage());
        }
    }
}
