<?php

namespace app\model;

use think\Model;

class Bookmark extends Model
{
    protected $name = 'bookmarks';
    protected $pk = 'id';
    protected $autoWriteTimestamp = true;

    protected $type = [
        'id' => 'integer',
        'user_id' => 'integer',
        'folder_id' => 'integer',
        'sort_order' => 'integer',
        'is_star' => 'integer',
        'visit_count' => 'integer',
        'is_dead' => 'integer',
        'status_code' => 'integer',
        'response_time' => 'float',
        'last_visit_at' => 'datetime',
        'last_check_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function folder()
    {
        return $this->belongsTo(Folder::class, 'folder_id');
    }

    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'bookmark_tags', 'bookmark_id', 'tag_id');
    }

    public function incrementVisit(): bool
    {
        $this->visit_count++;
        $this->last_visit_at = date('Y-m-d H:i:s');
        return $this->save();
    }

    public function toApiArray(bool $withTags = true): array
    {
        $data = [
            'id' => $this->id,
            'title' => $this->title,
            'url' => $this->url,
            'description' => $this->description,
            'favicon' => $this->favicon,
            'folder_id' => $this->folder_id,
            'sort_order' => $this->sort_order,
            'is_star' => $this->is_star,
            'visit_count' => $this->visit_count,
            'is_dead' => $this->is_dead,
            'status_code' => $this->status_code,
            'response_time' => $this->response_time,
            'error_message' => $this->error_message,
            'last_visit_at' => $this->formatDateTime($this->last_visit_at),
            'last_check_at' => $this->formatDateTime($this->last_check_at),
            'created_at' => $this->formatDateTime($this->created_at),
            'updated_at' => $this->formatDateTime($this->updated_at),
        ];

        if ($withTags) {
            $data['tags'] = $this->tags->map(function($tag) {
                return $tag->toApiArray();
            })->toArray();
        }

        return $data;
    }

    /**
     * 安全地格式化日期时间
     * @param mixed $datetime
     * @return string|null
     */
    private function formatDateTime($datetime): ?string
    {
        if (!$datetime) {
            return null;
        }

        if (is_string($datetime)) {
            return $datetime;
        }

        if ($datetime instanceof \DateTime) {
            return $datetime->format('Y-m-d H:i:s');
        }

        return null;
    }
}
