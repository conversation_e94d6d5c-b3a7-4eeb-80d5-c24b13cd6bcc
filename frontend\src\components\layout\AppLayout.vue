<template>
  <el-container class="app-layout">
    <el-header class="app-header">
      <AppHeader @toggle-sidebar="toggleSidebar" />
    </el-header>
    <el-container>
      <el-aside
        class="app-sidebar"
        :width="sidebarWidth"
        :class="{ 'is-collapsed': isCollapsed }"
      >
        <AppSidebar :collapsed="isCollapsed" />
      </el-aside>
      <el-main class="app-main" :class="{ 'sidebar-collapsed': isCollapsed }">
        <div class="main-content">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import AppHeader from './AppHeader.vue'
import AppSidebar from './AppSidebar.vue'
import { useAppStore } from '@/stores/app'
import { useWindowSize } from '@vueuse/core'

const appStore = useAppStore()

const isCollapsed = computed(() => appStore.sidebarCollapsed)
const sidebarWidth = computed(() => isCollapsed.value ? '64px' : '240px')

const toggleSidebar = () => {
  appStore.toggleSidebar()
}

// 响应式处理
const { width } = useWindowSize()
const isMobile = computed(() => width.value < 768)

watch(isMobile, (mobile) => {
  if (mobile && !isCollapsed.value) {
    appStore.toggleSidebar()
  }
}, { immediate: true })

// 初始化应用设置
onMounted(() => {
  appStore.initApp()
})
</script>

<style scoped>
.app-layout {
  height: 100vh;
  overflow: hidden;
}

.app-header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  z-index: 1000;
}

.app-sidebar {
  background-color: #fff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s ease;
  overflow: hidden;
}

.app-sidebar.is-collapsed {
  width: 64px !important;
}

.app-main {
  background-color: #f5f5f5;
  padding: 0;
  transition: margin-left 0.3s ease;
  overflow-y: auto;
}

.main-content {
  padding: 20px;
  min-height: calc(100vh - 60px);
}

.app-main.sidebar-collapsed {
  margin-left: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .app-sidebar:not(.is-collapsed) {
    transform: translateX(0);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  }

  .app-main {
    margin-left: 0 !important;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 12px;
  }
}
</style>
