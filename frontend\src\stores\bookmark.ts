import { defineStore } from 'pinia'
import type { Bookmark, CreateBookmarkRequest, SearchParams } from '@/types/api'
import { http } from '@/utils/request'

export const useBookmarkStore = defineStore('bookmark', () => {
  // 状态
  const bookmarks = ref<Bookmark[]>([])
  const currentBookmark = ref<Bookmark | null>(null)
  const loading = ref(false)
  const searchLoading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 计算属性
  const starredBookmarks = computed(() =>
    (bookmarks.value || []).filter(bookmark => bookmark.is_star)
  )

  const recentBookmarks = computed(() =>
    (bookmarks.value || [])
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 10)
  )

  // 获取书签列表
  const fetchBookmarks = async (params?: SearchParams) => {
    try {
      loading.value = true
      const response = await http.get('/bookmarks', { params })

      // 适配后端返回的数据结构
      bookmarks.value = response.data.items || []
      total.value = response.data.meta?.total || 0
      currentPage.value = response.data.meta?.page || 1
      pageSize.value = response.data.meta?.limit || 20

      return response.data
    } catch (error) {
      console.error('获取书签列表失败:', error)
      // 确保在错误时也有默认值
      bookmarks.value = []
      total.value = 0
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取书签详情
  const fetchBookmark = async (id: number) => {
    try {
      const response = await http.get(`/bookmarks/${id}`)
      currentBookmark.value = response.data
      return response.data
    } catch (error) {
      console.error('获取书签详情失败:', error)
      throw error
    }
  }

  // 创建书签
  const createBookmark = async (data: CreateBookmarkRequest) => {
    try {
      const response = await http.post('/bookmarks', data)
      const newBookmark = response.data
      
      // 添加到列表开头
      if (bookmarks.value) {
        bookmarks.value.unshift(newBookmark)
      }
      total.value += 1
      
      return newBookmark
    } catch (error) {
      console.error('创建书签失败:', error)
      throw error
    }
  }

  // 更新书签
  const updateBookmark = async (id: number, data: Partial<CreateBookmarkRequest>) => {
    try {
      const response = await http.put(`/bookmarks/${id}`, data)
      const updatedBookmark = response.data
      
      // 更新列表中的书签
      if (bookmarks.value) {
        const index = bookmarks.value.findIndex(bookmark => bookmark.id === id)
        if (index !== -1) {
          bookmarks.value[index] = updatedBookmark
        }
      }
      
      // 更新当前书签
      if (currentBookmark.value?.id === id) {
        currentBookmark.value = updatedBookmark
      }
      
      return updatedBookmark
    } catch (error) {
      console.error('更新书签失败:', error)
      throw error
    }
  }

  // 删除书签
  const deleteBookmark = async (id: number) => {
    try {
      await http.delete(`/bookmarks/${id}`)
      
      // 从列表中移除
      if (bookmarks.value) {
        bookmarks.value = bookmarks.value.filter(bookmark => bookmark.id !== id)
      }
      total.value -= 1
      
      // 清除当前书签
      if (currentBookmark.value?.id === id) {
        currentBookmark.value = null
      }
    } catch (error) {
      console.error('删除书签失败:', error)
      throw error
    }
  }

  // 批量操作书签
  const batchOperation = async (bookmarkIds: number[], action: string, data?: any) => {
    try {
      await http.post('/bookmarks/batch', {
        bookmark_ids: bookmarkIds,
        action,
        ...data
      })
      
      // 重新获取书签列表
      await fetchBookmarks()
    } catch (error) {
      console.error('批量操作失败:', error)
      throw error
    }
  }

  // 切换收藏状态
  const toggleStar = async (id: number) => {
    if (!bookmarks.value) return
    const bookmark = bookmarks.value.find(b => b.id === id)
    if (!bookmark) return
    
    try {
      await updateBookmark(id, { is_star: !bookmark.is_star })
    } catch (error) {
      console.error('切换收藏状态失败:', error)
      throw error
    }
  }

  // 访问书签
  const visitBookmark = async (id: number) => {
    try {
      await http.post(`/bookmarks/${id}/visit`)
      
      // 更新访问次数
      if (bookmarks.value) {
        const bookmark = bookmarks.value.find(b => b.id === id)
        if (bookmark) {
          bookmark.visit_count += 1
        }
      }
    } catch (error) {
      console.error('记录访问失败:', error)
    }
  }

  // 搜索书签
  const searchBookmarks = async (params: SearchParams) => {
    try {
      searchLoading.value = true
      return await fetchBookmarks(params)
    } catch (error) {
      console.error('搜索书签失败:', error)
      throw error
    } finally {
      searchLoading.value = false
    }
  }

  // 清空状态
  const clearBookmarks = () => {
    bookmarks.value = []
    currentBookmark.value = null
    total.value = 0
    currentPage.value = 1
  }

  return {
    // 状态
    bookmarks: readonly(bookmarks),
    currentBookmark: readonly(currentBookmark),
    loading: readonly(loading),
    searchLoading: readonly(searchLoading),
    total: readonly(total),
    currentPage: readonly(currentPage),
    pageSize: readonly(pageSize),
    
    // 计算属性
    starredBookmarks,
    recentBookmarks,
    
    // 方法
    fetchBookmarks,
    fetchBookmark,
    createBookmark,
    updateBookmark,
    deleteBookmark,
    batchOperation,
    toggleStar,
    visitBookmark,
    searchBookmarks,
    clearBookmarks
  }
})
