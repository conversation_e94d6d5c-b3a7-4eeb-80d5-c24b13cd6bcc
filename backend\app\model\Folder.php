<?php

namespace app\model;

use think\Model;

/**
 * 文件夹模型
 */
class Folder extends Model
{
    // 表名
    protected $table = 'folders';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'user_id' => 'integer',
        'parent_id' => 'integer',
        'sort_order' => 'integer',
        'level' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // JSON字段
    protected $json = [];

    /**
     * 最大层级深度
     */
    const MAX_LEVEL = 10;

    /**
     * 关联用户
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联父文件夹
     * @return \think\model\relation\BelongsTo
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * 关联子文件夹
     * @return \think\model\relation\HasMany
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id')->order('sort_order', 'asc');
    }

    /**
     * 关联书签
     * @return \think\model\relation\HasMany
     */
    public function bookmarks()
    {
        return $this->hasMany(Bookmark::class, 'folder_id');
    }

    /**
     * 获取所有子文件夹（递归）
     * @return \think\model\relation\HasMany
     */
    public function allChildren()
    {
        return $this->hasMany(self::class, 'parent_id')->with('allChildren');
    }

    /**
     * 路径修改器 - 自动生成路径
     * @param string $value
     * @return string
     */
    public function setPathAttr(string $value): string
    {
        return rtrim($value, '/') . '/';
    }

    /**
     * 获取文件夹的完整路径名称
     * @return string
     */
    public function getFullPathAttr(): string
    {
        if (!$this->parent_id) {
            return $this->name;
        }

        $pathParts = [$this->name];
        $parent = $this->parent;
        
        while ($parent) {
            array_unshift($pathParts, $parent->name);
            $parent = $parent->parent;
        }

        return implode(' / ', $pathParts);
    }

    /**
     * 获取面包屑导航
     * @return array
     */
    public function getBreadcrumbs(): array
    {
        $breadcrumbs = [];
        $current = $this;

        while ($current) {
            array_unshift($breadcrumbs, [
                'id' => $current->id,
                'name' => $current->name,
                'icon' => $current->icon,
                'color' => $current->color,
            ]);
            $current = $current->parent;
        }

        return $breadcrumbs;
    }

    /**
     * 检查是否为指定文件夹的子文件夹
     * @param int $folderId 文件夹ID
     * @return bool
     */
    public function isChildOf(int $folderId): bool
    {
        if ($this->parent_id === $folderId) {
            return true;
        }

        if ($this->parent_id && $this->parent) {
            return $this->parent->isChildOf($folderId);
        }

        return false;
    }

    /**
     * 检查是否可以移动到指定文件夹
     * @param int|null $targetParentId 目标父文件夹ID
     * @return bool
     */
    public function canMoveTo(?int $targetParentId): bool
    {
        // 不能移动到自己
        if ($targetParentId === $this->id) {
            return false;
        }

        // 不能移动到自己的子文件夹
        if ($targetParentId && $this->isParentOf($targetParentId)) {
            return false;
        }

        // 检查层级限制
        if ($targetParentId) {
            $targetParent = self::find($targetParentId);
            if ($targetParent && $targetParent->level >= self::MAX_LEVEL) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查是否为指定文件夹的父文件夹
     * @param int $folderId 文件夹ID
     * @return bool
     */
    public function isParentOf(int $folderId): bool
    {
        $folder = self::find($folderId);
        return $folder ? $folder->isChildOf($this->id) : false;
    }

    /**
     * 更新路径信息
     * @return bool
     */
    public function updatePath(): bool
    {
        $pathParts = [];
        $level = 1;
        $current = $this;

        // 构建路径
        while ($current->parent_id) {
            $current = $current->parent;
            if ($current) {
                array_unshift($pathParts, $current->id);
                $level++;
            }
        }

        array_push($pathParts, $this->id);

        $this->path = '/' . implode('/', $pathParts) . '/';
        $this->level = $level;

        return $this->save();
    }

    /**
     * 更新所有子文件夹的路径
     * @return int 更新的数量
     */
    public function updateChildrenPaths(): int
    {
        $count = 0;
        $children = $this->children;

        foreach ($children as $child) {
            if ($child->updatePath()) {
                $count++;
                $count += $child->updateChildrenPaths();
            }
        }

        return $count;
    }

    /**
     * 获取文件夹统计信息
     * @return array
     */
    public function getStats(): array
    {
        return [
            'bookmark_count' => $this->bookmarks()->count(),
            'subfolder_count' => $this->children()->count(),
            'total_bookmarks' => $this->getTotalBookmarkCount(),
        ];
    }

    /**
     * 获取文件夹直接包含的书签数量
     * @return int
     */
    public function getBookmarkCount(): int
    {
        return $this->bookmarks()->count();
    }

    /**
     * 获取子文件夹数量
     * @return int
     */
    public function getSubfolderCount(): int
    {
        return $this->children()->count();
    }

    /**
     * 获取包含子文件夹的总书签数量
     * @return int
     */
    public function getTotalBookmarkCount(): int
    {
        $count = $this->bookmarks()->count();

        foreach ($this->children as $child) {
            $count += $child->getTotalBookmarkCount();
        }

        return $count;
    }

    /**
     * 获取用户的根文件夹列表
     * @param int $userId 用户ID
     * @return \think\Collection
     */
    public static function getRootFolders(int $userId)
    {
        return self::where('user_id', $userId)
            ->where('parent_id', null)
            ->order('sort_order', 'asc')
            ->select();
    }

    /**
     * 获取用户的文件夹树
     * @param int $userId 用户ID
     * @param int|null $parentId 父文件夹ID
     * @return array
     */
    public static function getFolderTree(int $userId, ?int $parentId = null): array
    {
        $folders = self::where('user_id', $userId)
            ->where('parent_id', $parentId)
            ->order('sort_order', 'asc')
            ->select();

        $tree = [];
        foreach ($folders as $folder) {
            $folderData = $folder->toApiArray();
            $folderData['children'] = self::getFolderTree($userId, $folder->id);
            $tree[] = $folderData;
        }

        return $tree;
    }

    /**
     * 检查文件夹名称在同级是否唯一
     * @param string $name 文件夹名称
     * @param int $userId 用户ID
     * @param int|null $parentId 父文件夹ID
     * @param int|null $excludeId 排除的文件夹ID
     * @return bool
     */
    public static function isNameUniqueInLevel(string $name, int $userId, ?int $parentId = null, ?int $excludeId = null): bool
    {
        $query = self::where('user_id', $userId)
            ->where('name', $name)
            ->where('parent_id', $parentId);

        if ($excludeId) {
            $query->where('id', '<>', $excludeId);
        }

        return $query->count() === 0;
    }

    /**
     * 获取下一个排序值
     * @param int $userId 用户ID
     * @param int|null $parentId 父文件夹ID
     * @return int
     */
    public static function getNextSortOrder(int $userId, ?int $parentId = null): int
    {
        $maxSort = self::where('user_id', $userId)
            ->where('parent_id', $parentId)
            ->max('sort_order');

        return ($maxSort ?? 0) + 1;
    }

    /**
     * 转换为API输出格式
     * @param bool $withStats 是否包含统计信息
     * @return array
     */
    public function toApiArray(bool $withStats = false): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'icon' => $this->icon,
            'color' => $this->color,
            'parent_id' => $this->parent_id,
            'sort_order' => $this->sort_order,
            'level' => $this->level,
            'path' => $this->path,
            'full_path' => $this->full_path,
            'created_at' => $this->formatDateTime($this->created_at),
            'updated_at' => $this->formatDateTime($this->updated_at),
        ];

        if ($withStats) {
            $data['stats'] = $this->getStats();
        }

        return $data;
    }

    /**
     * 安全地格式化日期时间
     * @param mixed $datetime
     * @return string|null
     */
    private function formatDateTime($datetime): ?string
    {
        if (!$datetime) {
            return null;
        }

        if (is_string($datetime)) {
            return $datetime;
        }

        if ($datetime instanceof \DateTime) {
            return $datetime->format('Y-m-d H:i:s');
        }

        return null;
    }
}
