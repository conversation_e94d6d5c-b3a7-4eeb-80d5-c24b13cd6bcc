# Web端书签管理系统 - 数据库设计文档

## 1. 数据库基本信息

- **数据库类型**: MySQL 8.0+
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB
- **时区**: Asia/Shanghai

## 2. 命名规范

- **表名**: 小写字母，单词间用下划线分隔，使用复数形式
- **字段名**: 小写字母，单词间用下划线分隔
- **索引名**: idx_表名_字段名
- **外键名**: fk_表名_关联表名_字段名
- **主键**: 统一使用 `id` 作为主键名
- **时间字段**: `created_at`（创建时间）、`updated_at`（更新时间）、`deleted_at`（软删除时间）

## 3. 数据表设计

### 3.1 用户表 (users)

用户基础信息表，存储系统用户的账户信息。

```sql
CREATE TABLE `users` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `email` VARCHAR(100) NOT NULL COMMENT '邮箱地址',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL COMMENT '密码（bcrypt加密）',
    `avatar` VARCHAR(255) NULL DEFAULT NULL COMMENT '头像URL',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    `last_login_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(45) NULL DEFAULT NULL COMMENT '最后登录IP',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_email` (`email`),
    UNIQUE KEY `uk_username` (`username`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

### 3.2 用户认证令牌表 (user_tokens)

存储用户的认证令牌信息，支持 Refresh Token 机制。

```sql
CREATE TABLE `user_tokens` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '令牌ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `token_type` VARCHAR(20) NOT NULL DEFAULT 'refresh' COMMENT '令牌类型：refresh',
    `token` VARCHAR(500) NOT NULL COMMENT '令牌值',
    `expires_at` TIMESTAMP NOT NULL COMMENT '过期时间',
    `device_info` VARCHAR(255) NULL DEFAULT NULL COMMENT '设备信息',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_token` (`token`(191)),
    KEY `idx_expires_at` (`expires_at`),
    CONSTRAINT `fk_user_tokens_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户认证令牌表';
```

### 3.3 文件夹表 (folders)

书签文件夹表，支持无限层级的树形结构。

```sql
CREATE TABLE `folders` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '文件夹ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `parent_id` BIGINT UNSIGNED NULL DEFAULT NULL COMMENT '父文件夹ID',
    `name` VARCHAR(100) NOT NULL COMMENT '文件夹名称',
    `icon` VARCHAR(50) NULL DEFAULT NULL COMMENT '图标（emoji或预设图标代码）',
    `color` VARCHAR(7) NULL DEFAULT NULL COMMENT '颜色代码（#开头的16进制）',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    `path` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '文件夹路径（如：/1/2/3/）',
    `level` TINYINT NOT NULL DEFAULT 1 COMMENT '层级深度',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_path` (`path`(191)),
    KEY `idx_sort_order` (`sort_order`),
    CONSTRAINT `fk_folders_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_folders_parent` FOREIGN KEY (`parent_id`) REFERENCES `folders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件夹表';
```

### 3.4 书签表 (bookmarks)

核心书签数据表，存储用户收藏的网页信息。

```sql
CREATE TABLE `bookmarks` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '书签ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `folder_id` BIGINT UNSIGNED NULL DEFAULT NULL COMMENT '所属文件夹ID',
    `title` VARCHAR(255) NOT NULL COMMENT '标题',
    `url` VARCHAR(2000) NOT NULL COMMENT 'URL地址',
    `description` TEXT NULL DEFAULT NULL COMMENT '描述',
    `favicon` VARCHAR(500) NULL DEFAULT NULL COMMENT '网站图标URL',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    `is_star` TINYINT NOT NULL DEFAULT 0 COMMENT '是否星标：0-否，1-是',
    `visit_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '访问次数',
    `last_visit_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后访问时间',
    `is_dead` TINYINT NOT NULL DEFAULT 0 COMMENT '是否死链：0-正常，1-死链',
    `last_check_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后检测时间',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_folder_id` (`folder_id`),
    KEY `idx_url` (`url`(191)),
    KEY `idx_is_star` (`is_star`),
    KEY `idx_visit_count` (`visit_count`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_sort_order` (`sort_order`),
    CONSTRAINT `fk_bookmarks_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_bookmarks_folders` FOREIGN KEY (`folder_id`) REFERENCES `folders` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='书签表';
```

### 3.5 标签表 (tags)

标签表，采用扁平化结构设计。

```sql
CREATE TABLE `tags` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `name` VARCHAR(50) NOT NULL COMMENT '标签名称',
    `color` VARCHAR(7) NULL DEFAULT NULL COMMENT '标签颜色（#开头的16进制）',
    `usage_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用次数',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_tag` (`user_id`, `name`),
    KEY `idx_usage_count` (`usage_count`),
    CONSTRAINT `fk_tags_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';
```

### 3.6 书签标签关联表 (bookmark_tags)

书签与标签的多对多关联表。

```sql
CREATE TABLE `bookmark_tags` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `bookmark_id` BIGINT UNSIGNED NOT NULL COMMENT '书签ID',
    `tag_id` BIGINT UNSIGNED NOT NULL COMMENT '标签ID',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_bookmark_tag` (`bookmark_id`, `tag_id`),
    KEY `idx_tag_id` (`tag_id`),
    CONSTRAINT `fk_bookmark_tags_bookmarks` FOREIGN KEY (`bookmark_id`) REFERENCES `bookmarks` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_bookmark_tags_tags` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='书签标签关联表';
```

### 3.7 AI解析结果表 (ai_parse_results)

存储AI解析书签后生成的摘要、关键词等信息。

```sql
CREATE TABLE `ai_parse_results` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '解析结果ID',
    `bookmark_id` BIGINT UNSIGNED NOT NULL COMMENT '书签ID',
    `summary` TEXT NULL DEFAULT NULL COMMENT 'AI生成的摘要',
    `keywords` JSON NULL DEFAULT NULL COMMENT 'AI提取的关键词列表',
    `content_type` VARCHAR(50) NULL DEFAULT NULL COMMENT '内容类型：article/video/code/news等',
    `parse_status` TINYINT NOT NULL DEFAULT 0 COMMENT '解析状态：0-待解析，1-解析中，2-成功，3-失败',
    `error_message` TEXT NULL DEFAULT NULL COMMENT '解析失败原因',
    `parsed_at` TIMESTAMP NULL DEFAULT NULL COMMENT '解析完成时间',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_bookmark_id` (`bookmark_id`),
    KEY `idx_parse_status` (`parse_status`),
    KEY `idx_content_type` (`content_type`),
    CONSTRAINT `fk_ai_parse_bookmarks` FOREIGN KEY (`bookmark_id`) REFERENCES `bookmarks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI解析结果表';
```

### 3.8 智能文件夹表 (smart_folders)

保存用户自定义的搜索条件组合，实现动态文件夹功能。

```sql
CREATE TABLE `smart_folders` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '智能文件夹ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `name` VARCHAR(100) NOT NULL COMMENT '文件夹名称',
    `icon` VARCHAR(50) NULL DEFAULT NULL COMMENT '图标',
    `conditions` JSON NOT NULL COMMENT '搜索条件（最多10个）',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_sort_order` (`sort_order`),
    CONSTRAINT `fk_smart_folders_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='智能文件夹表';
```

条件JSON结构示例：
```json
{
    "conditions": [
        {
            "field": "title",
            "operator": "contains",
            "value": "JavaScript"
        },
        {
            "field": "tags",
            "operator": "includes",
            "value": ["前端", "开发"]
        },
        {
            "field": "created_at",
            "operator": "between",
            "value": ["2024-01-01", "2024-12-31"]
        }
    ],
    "logic": "AND"
}
```

### 3.9 回收站表 (trash)

存储已删除的书签和文件夹，保留365天。

```sql
CREATE TABLE `trash` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '回收站ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `item_type` VARCHAR(20) NOT NULL COMMENT '项目类型：bookmark/folder',
    `item_id` BIGINT UNSIGNED NOT NULL COMMENT '原始项目ID',
    `item_data` JSON NOT NULL COMMENT '项目完整数据（JSON格式）',
    `deleted_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '删除时间',
    `expire_at` TIMESTAMP NOT NULL DEFAULT (CURRENT_TIMESTAMP + INTERVAL 365 DAY) COMMENT '过期时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_item_type_id` (`item_type`, `item_id`),
    KEY `idx_expire_at` (`expire_at`),
    CONSTRAINT `fk_trash_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回收站表';
```

### 3.10 操作日志表 (operation_logs)

记录用户的重要操作行为，用于审计和问题追踪。

```sql
CREATE TABLE `operation_logs` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `operation` VARCHAR(50) NOT NULL COMMENT '操作类型',
    `module` VARCHAR(30) NOT NULL COMMENT '模块名称',
    `content` JSON NULL DEFAULT NULL COMMENT '操作详情',
    `ip` VARCHAR(45) NOT NULL COMMENT 'IP地址',
    `user_agent` VARCHAR(255) NULL DEFAULT NULL COMMENT '用户代理',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_operation` (`operation`),
    KEY `idx_created_at` (`created_at`),
    CONSTRAINT `fk_logs_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
```

### 3.11 系统配置表 (system_configs)

存储系统级配置参数。

```sql
CREATE TABLE `system_configs` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `value` TEXT NULL DEFAULT NULL COMMENT '配置值',
    `type` VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '值类型：string/number/boolean/json',
    `description` VARCHAR(255) NULL DEFAULT NULL COMMENT '配置说明',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';
```

### 3.12 队列任务表 (queue_jobs)

ThinkPHP Queue 使用的任务表（如果使用数据库驱动）。

```sql
CREATE TABLE `queue_jobs` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `queue` VARCHAR(255) NOT NULL,
    `payload` LONGTEXT NOT NULL,
    `attempts` TINYINT UNSIGNED NOT NULL,
    `reserved_at` INT UNSIGNED DEFAULT NULL,
    `available_at` INT UNSIGNED NOT NULL,
    `created_at` INT UNSIGNED NOT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_queue_reserved_at` (`queue`, `reserved_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='队列任务表';
```

## 4. 索引设计说明

### 4.1 索引设计原则

1. **主键索引**: 所有表都使用自增 BIGINT 作为主键
2. **外键索引**: 所有外键字段自动创建索引
3. **唯一索引**: 用于保证数据唯一性（如邮箱、用户名）
4. **普通索引**: 基于查询频率和性能需求创建
5. **复合索引**: 用于优化多条件查询

### 4.2 核心查询优化

- `bookmarks` 表的 `user_id + folder_id` 用于文件夹内书签查询
- `bookmarks` 表的 `user_id + created_at` 用于时间范围查询
- `tags` 表的 `user_id + name` 确保用户标签唯一性
- `bookmark_tags` 的复合唯一索引防止重复关联

## 5. 数据关系说明

### 5.1 一对多关系

- 用户 → 文件夹 (1:N)
- 用户 → 书签 (1:N)
- 用户 → 标签 (1:N)
- 文件夹 → 书签 (1:N)
- 文件夹 → 子文件夹 (1:N, 自关联)

### 5.2 多对多关系

- 书签 ↔ 标签 (M:N, 通过 bookmark_tags 关联)

### 5.3 一对一关系

- 书签 → AI解析结果 (1:1)

## 6. 数据完整性约束

### 6.1 外键约束

- 所有外键都设置了 `ON DELETE CASCADE` 或 `ON DELETE SET NULL`
- 用户删除时，相关数据自动级联删除
- 文件夹删除时，书签的 folder_id 设为 NULL

### 6.2 数据验证

- 必填字段使用 `NOT NULL` 约束
- 字符串长度根据实际需求限制
- 使用合适的数据类型（如 URL 使用 VARCHAR(2000)）

## 7. 性能优化考虑

### 7.1 分区策略（预留）

当数据量达到千万级别时，可考虑按用户ID进行分区：

```sql
-- 示例：按用户ID范围分区
ALTER TABLE bookmarks 
PARTITION BY RANGE (user_id) (
    PARTITION p0 VALUES LESS THAN (10000),
    PARTITION p1 VALUES LESS THAN (20000),
    PARTITION p2 VALUES LESS THAN (MAXVALUE)
);
```

### 7.2 归档策略

- 超过2年未访问的书签可移至归档表
- 回收站数据365天后自动清理
- 操作日志保留90天

### 7.3 缓存优化

- 热点数据（如标签、文件夹结构）缓存到 Redis
- 统计数据定期计算并缓存
- 用户会话信息存储在 Redis

## 8. 备份与恢复

### 8.1 备份策略

- **全量备份**: 每周日凌晨2点
- **增量备份**: 每天凌晨3点
- **备份保留**: 保留最近30天的备份

### 8.2 恢复测试

- 每月进行一次恢复演练
- 记录恢复时间和数据完整性

## 9. 监控指标

- 慢查询监控（超过200ms）
- 表空间增长监控
- 连接数监控
- 死锁监控

## 10. 数据迁移

### 10.1 初始化脚本

提供完整的建表脚本和初始数据脚本。

### 10.2 版本升级

使用迁移工具（如 Phinx）管理数据库版本变更。 