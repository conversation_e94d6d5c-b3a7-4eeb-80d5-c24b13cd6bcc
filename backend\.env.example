# 应用配置
APP_DEBUG = true
APP_TRACE = false

# 数据库配置
DATABASE_TYPE = mysql
DATABASE_HOSTNAME = 127.0.0.1
DATABASE_DATABASE = bookmark_system
DATABASE_USERNAME = root
DATABASE_PASSWORD = 
DATABASE_HOSTPORT = 3306
DATABASE_CHARSET = utf8mb4
DATABASE_PREFIX = 

# 缓存配置
CACHE_DRIVER = file

# 会话配置
SESSION_TYPE = file

# JWT配置
JWT_SECRET = your_jwt_secret_key_here_change_in_production
JWT_TTL = 2592000
JWT_REFRESH_TTL = 604800

# 文件上传配置
UPLOAD_PATH = uploads/
MAX_FILE_SIZE = 10485760

# 邮件配置（可选）
MAIL_TYPE = smtp
MAIL_HOST = 
MAIL_PORT = 587
MAIL_USERNAME = 
MAIL_PASSWORD = 
MAIL_FROM = 

# Redis配置（可选）
REDIS_HOST = 127.0.0.1
REDIS_PORT = 6379
REDIS_PASSWORD = 
REDIS_SELECT = 0

# 日志配置
LOG_LEVEL = debug
LOG_PATH = runtime/log/
