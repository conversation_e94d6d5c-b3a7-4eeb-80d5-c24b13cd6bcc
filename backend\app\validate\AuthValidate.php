<?php

namespace app\validate;

use think\Validate;

/**
 * 认证相关验证器
 */
class AuthValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        // 注册验证规则
        'email' => 'email|max:100',
        'username' => 'alphaNum|length:3,50',
        'password' => 'length:6,32',
        'password_confirm' => 'confirm:password',
        'password_confirmation' => 'confirm:password', // 兼容两种字段名
        'nickname' => 'max:50', // 添加昵称字段验证
        'avatar' => 'url|max:255',

        // 登录验证规则（支持两种字段名格式）
        'account' => 'max:100', // 登录时使用
        'login_password' => 'max:32', // 登录时使用
        'device_info' => 'max:255',

        // 刷新令牌验证规则
        'refresh_token' => '',

        // 修改密码验证规则
        'old_password' => 'max:32',
        'new_password' => 'length:6,32',
        'new_password_confirmation' => 'confirm:new_password',
        'logout_other_devices' => 'boolean',
    ];

    /**
     * 验证消息
     */
    protected $message = [
        // 注册消息
        'email.require' => '邮箱地址不能为空',
        'email.email' => '邮箱地址格式不正确',
        'email.max' => '邮箱地址长度不能超过100个字符',
        'username.require' => '用户名不能为空',
        'username.alphaNum' => '用户名只能包含字母和数字',
        'username.length' => '用户名长度必须在3-50个字符之间',
        'password.require' => '密码不能为空',
        'password.length' => '密码长度必须在6-32个字符之间',
        'password_confirm.require' => '确认密码不能为空',
        'password_confirm.confirm' => '两次输入的密码不一致',
        'password_confirmation.require' => '确认密码不能为空',
        'password_confirmation.confirm' => '两次输入的密码不一致',
        'nickname.max' => '昵称长度不能超过50个字符',
        'avatar.url' => '头像必须是有效的URL地址',
        'avatar.max' => '头像URL长度不能超过255个字符',

        // 登录消息
        'account.require' => '账号不能为空',
        'account.max' => '账号长度不能超过100个字符',
        'login_password.require' => '密码不能为空',
        'login_password.max' => '密码长度不能超过32个字符',
        'email.email' => '邮箱格式不正确',
        'email.max' => '邮箱长度不能超过100个字符',
        'password.max' => '密码长度不能超过32个字符',
        'device_info.max' => '设备信息长度不能超过255个字符',

        // 刷新令牌消息
        'refresh_token.require' => '刷新令牌不能为空',

        // 修改密码消息
        'old_password.require' => '原密码不能为空',
        'old_password.max' => '原密码长度不能超过32个字符',
        'new_password.require' => '新密码不能为空',
        'new_password.length' => '新密码长度必须在6-32个字符之间',
        'new_password_confirmation.require' => '确认新密码不能为空',
        'new_password_confirmation.confirm' => '两次输入的新密码不一致',
        'logout_other_devices.boolean' => '登出其他设备参数必须是布尔值',
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'register' => ['email', 'username', 'password', 'nickname', 'avatar'],
        'login' => ['account', 'login_password', 'device_info'], // 原始格式
        'login_simple' => ['email', 'password', 'device_info'], // 简化格式
        'refresh' => ['refresh_token'],
        'change_password' => ['old_password', 'new_password', 'new_password_confirmation', 'logout_other_devices'],
    ];

    /**
     * 注册场景的额外验证
     */
    protected function sceneRegister()
    {
        return $this->append('email', 'require')
                    ->append('username', 'require')
                    ->append('password', 'require|checkPasswordConfirm');
    }

    /**
     * 登录场景的额外验证
     */
    protected function sceneLogin()
    {
        return $this->append('account', 'require')
                    ->append('login_password', 'require');
    }

    /**
     * 简化登录场景的额外验证
     */
    protected function sceneLoginSimple()
    {
        return $this->append('email', 'require')
                    ->append('password', 'require');
    }

    /**
     * 刷新令牌场景的额外验证
     */
    protected function sceneRefresh()
    {
        return $this->append('refresh_token', 'require');
    }

    /**
     * 修改密码场景的额外验证
     */
    protected function sceneChangePassword()
    {
        return $this->append('old_password', 'require')
                    ->append('new_password', 'require')
                    ->append('new_password_confirmation', 'require');
    }

    /**
     * 自定义验证规则：检查密码确认
     * @param string $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkPasswordConfirm($value, $rule, $data)
    {
        // 检查是否存在确认密码字段（支持两种字段名）
        $confirmPassword = $data['password_confirm'] ?? $data['password_confirmation'] ?? null;

        if (empty($confirmPassword)) {
            return '请输入确认密码';
        }

        if ($value !== $confirmPassword) {
            return '两次输入的密码不一致';
        }

        return true;
    }

    /**
     * 自定义验证规则：检查用户名格式
     * @param string $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkUsername($value, $rule, $data)
    {
        // 用户名不能以数字开头
        if (is_numeric(substr($value, 0, 1))) {
            return '用户名不能以数字开头';
        }

        // 用户名不能包含特殊字符
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $value)) {
            return '用户名只能包含字母、数字和下划线';
        }

        return true;
    }

    /**
     * 自定义验证规则：检查密码强度
     * @param string $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkPasswordStrength($value, $rule, $data)
    {
        // 密码必须包含字母和数字
        if (!preg_match('/^(?=.*[A-Za-z])(?=.*\d)/', $value)) {
            return '密码必须包含字母和数字';
        }

        // 检查常见弱密码
        $weakPasswords = [
            '123456', 'password', '123456789', '12345678',
            'qwerty', 'abc123', '111111', '1234567890'
        ];

        if (in_array(strtolower($value), $weakPasswords)) {
            return '密码过于简单，请使用更复杂的密码';
        }

        return true;
    }

    /**
     * 自定义验证规则：检查邮箱域名
     * @param string $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkEmailDomain($value, $rule, $data)
    {
        // 禁止的邮箱域名列表
        $blockedDomains = [
            '10minutemail.com',
            'tempmail.org',
            'guerrillamail.com'
        ];

        $domain = substr(strrchr($value, '@'), 1);
        if (in_array(strtolower($domain), $blockedDomains)) {
            return '不支持临时邮箱注册';
        }

        return true;
    }
}
