<?php

namespace app\service;

use app\model\Bookmark;
use app\model\Folder;
use app\exception\BusinessException;
use think\facade\Filesystem;

/**
 * 导出服务类
 */
class ExportService
{
    /**
     * 导出书签
     * @param array $params 导出参数
     * @return array 导出结果
     * @throws BusinessException
     */
    public function exportBookmarks(array $params): array
    {
        $userId = $params['user_id'];
        $format = $params['format'] ?? 'html';
        $folderIds = $params['folder_ids'] ?? [];
        $includeFolders = $params['include_folders'] ?? true;
        $includeTags = $params['include_tags'] ?? true;
        $encoding = $params['encoding'] ?? 'utf-8';

        // 验证文件夹权限
        if (!empty($folderIds)) {
            $validFolders = Folder::where('user_id', $userId)
                ->whereIn('id', $folderIds)
                ->column('id');
            
            if (count($validFolders) !== count($folderIds)) {
                throw new BusinessException('部分文件夹不存在或无权限访问', 30004);
            }
        }

        // 获取书签数据
        $bookmarks = $this->getBookmarksForExport($userId, $folderIds, $includeTags);
        
        if (empty($bookmarks)) {
            throw new BusinessException('没有可导出的书签', 30005);
        }

        // 生成导出文件
        $jobId = 'export_' . date('Ymd_His');
        $fileName = $this->generateFileName($format);
        $filePath = $this->generateExportFile($bookmarks, $format, $encoding, $includeFolders, $includeTags);

        return [
            'job_id' => $jobId,
            'download_url' => '/api/v1/export/download/' . $jobId,
            'file_name' => $fileName,
            'file_size' => filesize($filePath),
            'total_bookmarks' => count($bookmarks),
            'expires_at' => date('Y-m-d H:i:s', time() + 3600) // 1小时后过期
        ];
    }

    /**
     * 获取要导出的书签数据
     * @param int $userId 用户ID
     * @param array $folderIds 文件夹ID列表
     * @param bool $includeTags 是否包含标签
     * @return array 书签数据
     */
    private function getBookmarksForExport(int $userId, array $folderIds, bool $includeTags): array
    {
        $query = Bookmark::where('user_id', $userId);

        if (!empty($folderIds)) {
            $query->whereIn('folder_id', $folderIds);
        }

        if ($includeTags) {
            $query->with('tags');
        }

        $query->with('folder')->order('folder_id', 'asc')->order('sort_order', 'asc');

        $bookmarks = $query->select();

        return $bookmarks->map(function($bookmark) use ($includeTags) {
            $data = [
                'id' => $bookmark->id,
                'title' => $bookmark->title,
                'url' => $bookmark->url,
                'description' => $bookmark->description,
                'favicon' => $bookmark->favicon,
                'is_star' => $bookmark->is_star,
                'folder_name' => $bookmark->folder ? $bookmark->folder->name : '',
                'folder_path' => $bookmark->folder ? $bookmark->folder->getPath() : '',
                'created_at' => $bookmark->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $bookmark->updated_at->format('Y-m-d H:i:s'),
            ];

            if ($includeTags && $bookmark->tags) {
                $data['tags'] = $bookmark->tags->column('name');
            }

            return $data;
        })->toArray();
    }

    /**
     * 生成导出文件
     * @param array $bookmarks 书签数据
     * @param string $format 格式
     * @param string $encoding 编码
     * @param bool $includeFolders 是否包含文件夹
     * @param bool $includeTags 是否包含标签
     * @return string 文件路径
     */
    private function generateExportFile(array $bookmarks, string $format, string $encoding, bool $includeFolders, bool $includeTags): string
    {
        $content = '';
        
        switch ($format) {
            case 'html':
                $content = $this->generateHtmlContent($bookmarks, $includeFolders);
                break;
            case 'json':
                $content = $this->generateJsonContent($bookmarks);
                break;
            case 'csv':
                $content = $this->generateCsvContent($bookmarks, $includeTags);
                break;
        }

        // 转换编码
        if ($encoding === 'gbk') {
            $content = mb_convert_encoding($content, 'GBK', 'UTF-8');
        }

        // 保存文件
        $fileName = $this->generateFileName($format);
        $filePath = runtime_path('export') . DIRECTORY_SEPARATOR . $fileName;
        
        // 确保目录存在
        $dir = dirname($filePath);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        file_put_contents($filePath, $content);

        return $filePath;
    }

    /**
     * 生成HTML格式内容
     * @param array $bookmarks 书签数据
     * @param bool $includeFolders 是否包含文件夹
     * @return string HTML内容
     */
    private function generateHtmlContent(array $bookmarks, bool $includeFolders): string
    {
        $html = '<!DOCTYPE NETSCAPE-Bookmark-file-1>' . PHP_EOL;
        $html .= '<!-- This is an automatically generated file.' . PHP_EOL;
        $html .= '     It will be read and overwritten.' . PHP_EOL;
        $html .= '     DO NOT EDIT! -->' . PHP_EOL;
        $html .= '<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">' . PHP_EOL;
        $html .= '<TITLE>Bookmarks</TITLE>' . PHP_EOL;
        $html .= '<H1>Bookmarks</H1>' . PHP_EOL;
        $html .= '<DL><p>' . PHP_EOL;

        $currentFolder = '';
        foreach ($bookmarks as $bookmark) {
            // 处理文件夹分组
            if ($includeFolders && $bookmark['folder_name'] !== $currentFolder) {
                if ($currentFolder !== '') {
                    $html .= '</DL><p>' . PHP_EOL;
                }
                $currentFolder = $bookmark['folder_name'];
                if ($currentFolder) {
                    $html .= '<DT><H3>' . htmlspecialchars($currentFolder) . '</H3>' . PHP_EOL;
                    $html .= '<DL><p>' . PHP_EOL;
                }
            }

            // 添加书签
            $html .= '<DT><A HREF="' . htmlspecialchars($bookmark['url']) . '"';
            if ($bookmark['created_at']) {
                $html .= ' ADD_DATE="' . strtotime($bookmark['created_at']) . '"';
            }
            if ($bookmark['favicon']) {
                $html .= ' ICON="' . htmlspecialchars($bookmark['favicon']) . '"';
            }
            $html .= '>' . htmlspecialchars($bookmark['title']) . '</A>' . PHP_EOL;
            
            if ($bookmark['description']) {
                $html .= '<DD>' . htmlspecialchars($bookmark['description']) . PHP_EOL;
            }
        }

        if ($includeFolders && $currentFolder !== '') {
            $html .= '</DL><p>' . PHP_EOL;
        }
        
        $html .= '</DL><p>' . PHP_EOL;

        return $html;
    }

    /**
     * 生成JSON格式内容
     * @param array $bookmarks 书签数据
     * @return string JSON内容
     */
    private function generateJsonContent(array $bookmarks): string
    {
        $data = [
            'export_info' => [
                'version' => '1.0',
                'export_time' => date('Y-m-d H:i:s'),
                'total_count' => count($bookmarks)
            ],
            'bookmarks' => $bookmarks
        ];

        return json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * 生成CSV格式内容
     * @param array $bookmarks 书签数据
     * @param bool $includeTags 是否包含标签
     * @return string CSV内容
     */
    private function generateCsvContent(array $bookmarks, bool $includeTags): string
    {
        $csv = '';
        
        // CSV头部
        $headers = ['标题', 'URL', '描述', '文件夹', '星标', '创建时间'];
        if ($includeTags) {
            $headers[] = '标签';
        }
        $csv .= implode(',', array_map(function($header) {
            return '"' . str_replace('"', '""', $header) . '"';
        }, $headers)) . PHP_EOL;

        // CSV数据
        foreach ($bookmarks as $bookmark) {
            $row = [
                $bookmark['title'],
                $bookmark['url'],
                $bookmark['description'],
                $bookmark['folder_name'],
                $bookmark['is_star'] ? '是' : '否',
                $bookmark['created_at']
            ];

            if ($includeTags) {
                $row[] = isset($bookmark['tags']) ? implode(';', $bookmark['tags']) : '';
            }

            $csv .= implode(',', array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row)) . PHP_EOL;
        }

        return $csv;
    }

    /**
     * 生成文件名
     * @param string $format 格式
     * @return string 文件名
     */
    private function generateFileName(string $format): string
    {
        return 'bookmarks_' . date('Ymd_His') . '.' . $format;
    }
}
