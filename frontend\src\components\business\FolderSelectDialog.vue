<template>
  <el-dialog
    v-model="visible"
    title="移动到文件夹"
    width="500px"
    :before-close="handleClose"
  >
    <div class="folder-select-content">
      <el-alert
        title="选择目标文件夹"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px;"
      >
        <template #default>
          选择要移动到的文件夹，如果不选择则移动到根目录
        </template>
      </el-alert>
      
      <el-tree
        ref="treeRef"
        :data="folderTree"
        :props="{ label: 'name', children: 'children' }"
        node-key="id"
        :default-expand-all="true"
        :expand-on-click-node="false"
        :check-on-click-node="true"
        :highlight-current="true"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <div class="folder-node">
            <el-icon class="folder-icon">
              <Folder />
            </el-icon>
            <span class="folder-name">{{ data.name }}</span>
            <span class="bookmark-count">({{ data.bookmarkCount || 0 }})</span>
          </div>
        </template>
      </el-tree>
      
      <div class="selected-folder" v-if="selectedFolder">
        <el-tag type="success" size="large">
          <el-icon><Folder /></el-icon>
          {{ selectedFolder.name }}
        </el-tag>
      </div>
      
      <div class="no-folder-option">
        <el-button 
          @click="selectRootFolder"
          :type="selectedFolder === null ? 'primary' : 'default'"
        >
          <el-icon><House /></el-icon>
          移动到根目录
        </el-button>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="moving">
          确定移动
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Folder, House } from '@element-plus/icons-vue'
import type { Bookmark, Folder as FolderType } from '@/types/api'
import { useFolderStore } from '@/stores/folder'
import { useBookmarkStore } from '@/stores/bookmark'

interface Props {
  modelValue: boolean
  bookmark?: Bookmark
  bookmarks?: Bookmark[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const folderStore = useFolderStore()
const bookmarkStore = useBookmarkStore()

const treeRef = ref()
const moving = ref(false)
const selectedFolder = ref<FolderType | null>(null)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const folderTree = computed(() => folderStore.folderTree)

const handleNodeClick = (folder: FolderType) => {
  selectedFolder.value = folder
  treeRef.value?.setCurrentKey(folder.id)
}

const selectRootFolder = () => {
  selectedFolder.value = null
  treeRef.value?.setCurrentKey(null)
}

const handleConfirm = async () => {
  if (!props.bookmark && !props.bookmarks?.length) {
    ElMessage.warning('没有要移动的书签')
    return
  }
  
  moving.value = true
  
  try {
    const targetFolderId = selectedFolder.value?.id || undefined

    if (props.bookmark) {
      // 移动单个书签
      await bookmarkStore.updateBookmark(props.bookmark.id, {
        folder_id: targetFolderId
      })
      ElMessage.success('书签移动成功')
    } else if (props.bookmarks?.length) {
      // 批量移动书签
      const bookmarkIds = props.bookmarks.map(b => b.id)
      await bookmarkStore.batchOperation(bookmarkIds, 'move', {
        folder_id: targetFolderId
      })
      ElMessage.success(`成功移动 ${props.bookmarks.length} 个书签`)
    }
    
    emit('success')
    handleClose()
  } catch (error) {
    console.error('移动书签失败:', error)
    ElMessage.error('移动失败，请稍后重试')
  } finally {
    moving.value = false
  }
}

const handleClose = () => {
  visible.value = false
  selectedFolder.value = null
}

// 监听对话框显示
watch(visible, (show) => {
  if (show) {
    // 获取文件夹列表
    if (folderTree.value.length === 0) {
      folderStore.fetchFolders()
    }
    
    // 设置当前文件夹
    if (props.bookmark?.folder_id) {
      const currentFolder = folderStore.getFolderById(props.bookmark.folder_id)
      if (currentFolder) {
        selectedFolder.value = currentFolder
        nextTick(() => {
          treeRef.value?.setCurrentKey(currentFolder.id)
        })
      }
    }
  }
})
</script>

<style scoped>
.folder-select-content {
  max-height: 400px;
  overflow-y: auto;
}

.folder-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.folder-icon {
  color: #409eff;
  font-size: 16px;
}

.folder-name {
  flex: 1;
  font-size: 14px;
  color: #303133;
}

.bookmark-count {
  font-size: 12px;
  color: #909399;
}

.selected-folder {
  margin: 16px 0;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #409eff;
  border-radius: 6px;
  text-align: center;
}

.no-folder-option {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-tree-node__content) {
  height: 36px;
  padding: 0 8px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #e6f7ff;
  color: #409eff;
}
</style>
