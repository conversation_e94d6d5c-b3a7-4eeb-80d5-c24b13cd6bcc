<?php

namespace app\service;

use app\model\SmartFolder;
use app\model\Bookmark;
use app\exception\BusinessException;
use think\facade\Db;

/**
 * 智能文件夹服务类
 */
class SmartFolderService
{
    /**
     * 创建智能文件夹
     * @param int $userId 用户ID
     * @param array $data 智能文件夹数据
     * @return SmartFolder
     * @throws BusinessException
     */
    public function createSmartFolder(int $userId, array $data): SmartFolder
    {
        // 检查名称是否重复
        if (SmartFolder::nameExists($data['name'], $userId)) {
            throw new BusinessException('智能文件夹名称已存在', 20001);
        }

        // 验证条件格式
        if (!SmartFolder::validateConditions($data['conditions'] ?? [])) {
            throw new BusinessException('智能文件夹条件格式无效', 20002);
        }

        $smartFolder = new SmartFolder();
        $smartFolder->user_id = $userId;
        $smartFolder->name = $data['name'];
        $smartFolder->icon = $data['icon'] ?? null;
        $smartFolder->conditions = $data['conditions'] ?? [];
        $smartFolder->sort_order = SmartFolder::getNextSortOrder($userId);

        if (!$smartFolder->save()) {
            throw new BusinessException('创建智能文件夹失败');
        }

        return $smartFolder;
    }

    /**
     * 更新智能文件夹
     * @param int $userId 用户ID
     * @param int $smartFolderId 智能文件夹ID
     * @param array $data 更新数据
     * @return SmartFolder
     * @throws BusinessException
     */
    public function updateSmartFolder(int $userId, int $smartFolderId, array $data): SmartFolder
    {
        $smartFolder = SmartFolder::where('id', $smartFolderId)
            ->where('user_id', $userId)
            ->find();

        if (!$smartFolder) {
            throw new BusinessException('智能文件夹不存在', 20003);
        }

        // 检查名称是否重复（排除自己）
        if (isset($data['name']) && $data['name'] !== $smartFolder->name) {
            if (SmartFolder::nameExists($data['name'], $userId, $smartFolderId)) {
                throw new BusinessException('智能文件夹名称已存在', 20001);
            }
        }

        // 验证条件格式
        if (isset($data['conditions']) && !SmartFolder::validateConditions($data['conditions'])) {
            throw new BusinessException('智能文件夹条件格式无效', 20002);
        }

        // 更新字段
        $allowedFields = ['name', 'icon', 'conditions'];
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $smartFolder->$field = $data[$field];
            }
        }

        if (!$smartFolder->save()) {
            throw new BusinessException('更新智能文件夹失败');
        }

        return $smartFolder;
    }

    /**
     * 删除智能文件夹
     * @param int $userId 用户ID
     * @param int $smartFolderId 智能文件夹ID
     * @return bool
     * @throws BusinessException
     */
    public function deleteSmartFolder(int $userId, int $smartFolderId): bool
    {
        $smartFolder = SmartFolder::where('id', $smartFolderId)
            ->where('user_id', $userId)
            ->find();

        if (!$smartFolder) {
            throw new BusinessException('智能文件夹不存在', 20003);
        }

        return $smartFolder->delete();
    }

    /**
     * 获取智能文件夹列表
     * @param int $userId 用户ID
     * @param array $params 查询参数
     * @return array
     */
    public function getSmartFolderList(int $userId, array $params = []): array
    {
        $query = SmartFolder::where('user_id', $userId);

        // 搜索
        if (!empty($params['search'])) {
            $query->where('name', 'like', '%' . $params['search'] . '%');
        }

        // 排序
        $orderBy = $params['order_by'] ?? 'sort_order';
        $orderDir = $params['order_dir'] ?? 'asc';
        $query->order($orderBy, $orderDir);

        $smartFolders = $query->select();

        return $smartFolders->map(function($smartFolder) {
            return $smartFolder->toApiArray(true); // 包含书签数量
        })->toArray();
    }

    /**
     * 获取智能文件夹详情
     * @param int $userId 用户ID
     * @param int $smartFolderId 智能文件夹ID
     * @return SmartFolder
     * @throws BusinessException
     */
    public function getSmartFolder(int $userId, int $smartFolderId): SmartFolder
    {
        $smartFolder = SmartFolder::where('id', $smartFolderId)
            ->where('user_id', $userId)
            ->find();

        if (!$smartFolder) {
            throw new BusinessException('智能文件夹不存在', 20003);
        }

        return $smartFolder;
    }

    /**
     * 获取智能文件夹中的书签
     * @param int $userId 用户ID
     * @param int $smartFolderId 智能文件夹ID
     * @param array $params 查询参数
     * @return array
     * @throws BusinessException
     */
    public function getSmartFolderBookmarks(int $userId, int $smartFolderId, array $params = []): array
    {
        $smartFolder = $this->getSmartFolder($userId, $smartFolderId);
        
        $query = $smartFolder->getMatchingBookmarks()->with('tags');

        // 额外的搜索条件
        if (!empty($params['search'])) {
            $query->where(function($q) use ($params) {
                $q->where('title', 'like', '%' . $params['search'] . '%')
                  ->whereOr('description', 'like', '%' . $params['search'] . '%')
                  ->whereOr('url', 'like', '%' . $params['search'] . '%');
            });
        }

        // 排序
        $orderBy = $params['order_by'] ?? 'created_at';
        $orderDir = $params['order_dir'] ?? 'desc';
        $query->order($orderBy, $orderDir);

        // 分页
        $page = max(1, (int)($params['page'] ?? 1));
        $limit = max(1, min(100, (int)($params['limit'] ?? 20)));
        
        $total = $query->count();
        $bookmarks = $query->page($page, $limit)->select();

        return [
            'smart_folder' => $smartFolder->toApiArray(),
            'items' => $bookmarks->map(function($bookmark) {
                return $bookmark->toApiArray();
            })->toArray(),
            'meta' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]
        ];
    }

    /**
     * 批量排序智能文件夹
     * @param int $userId 用户ID
     * @param array $sortData 排序数据 [['id' => 1, 'sort_order' => 1], ...]
     * @return bool
     * @throws BusinessException
     */
    public function sortSmartFolders(int $userId, array $sortData): bool
    {
        Db::startTrans();
        try {
            foreach ($sortData as $item) {
                if (!isset($item['id']) || !isset($item['sort_order'])) {
                    continue;
                }

                SmartFolder::where('id', $item['id'])
                    ->where('user_id', $userId)
                    ->update(['sort_order' => $item['sort_order']]);
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw new BusinessException('智能文件夹排序失败：' . $e->getMessage());
        }
    }

    /**
     * 获取智能文件夹的条件选项
     * @return array
     */
    public function getConditionOptions(): array
    {
        return [
            'fields' => [
                ['value' => 'title', 'label' => '标题'],
                ['value' => 'url', 'label' => 'URL'],
                ['value' => 'description', 'label' => '描述'],
                ['value' => 'folder_id', 'label' => '文件夹'],
                ['value' => 'is_star', 'label' => '星标'],
                ['value' => 'tags', 'label' => '标签'],
                ['value' => 'created_at', 'label' => '创建时间'],
            ],
            'operators' => [
                ['value' => 'contains', 'label' => '包含'],
                ['value' => 'equals', 'label' => '等于'],
                ['value' => 'after', 'label' => '晚于'],
                ['value' => 'before', 'label' => '早于'],
            ]
        ];
    }
}
