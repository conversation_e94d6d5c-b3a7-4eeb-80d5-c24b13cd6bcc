<?php

namespace app\service;

use app\model\Bookmark;
use app\model\Folder;
use app\model\Tag;
use app\model\BookmarkTag;
use app\exception\BusinessException;
use think\facade\Db;
use think\file\UploadedFile;

/**
 * 导入服务类
 */
class ImportService
{
    /**
     * 导入书签
     * @param UploadedFile $file 上传的文件
     * @param array $params 导入参数
     * @return array 导入结果
     * @throws BusinessException
     */
    public function importBookmarks(UploadedFile $file, array $params): array
    {
        $userId = $params['user_id'];
        $targetFolderId = $params['folder_id'] ?? null;
        $mergeDuplicates = $params['merge_duplicates'] ?? true;
        $createFolders = $params['create_folders'] ?? true;

        // 验证目标文件夹
        if ($targetFolderId) {
            $targetFolder = Folder::where('id', $targetFolderId)
                ->where('user_id', $userId)
                ->find();
            if (!$targetFolder) {
                throw new BusinessException('目标文件夹不存在', 30001);
            }
        }

        // 读取文件内容
        $content = file_get_contents($file->getPathname());
        $extension = strtolower($file->getOriginalExtension());

        // 解析书签数据
        if (in_array($extension, ['html', 'htm'])) {
            $bookmarksData = $this->parseHtmlBookmarks($content);
        } elseif ($extension === 'json') {
            $bookmarksData = $this->parseJsonBookmarks($content);
        } else {
            throw new BusinessException('不支持的文件格式', 30002);
        }

        // 执行导入
        return $this->processImport($userId, $bookmarksData, $targetFolderId, $mergeDuplicates, $createFolders);
    }

    /**
     * 解析HTML格式的书签文件
     * @param string $content HTML内容
     * @return array 解析后的书签数据
     */
    private function parseHtmlBookmarks(string $content): array
    {
        $bookmarks = [];
        $folders = [];
        
        // 简单的HTML解析（实际项目中建议使用专业的HTML解析库）
        preg_match_all('/<DT><A HREF="([^"]+)"[^>]*>([^<]+)<\/A>/i', $content, $matches, PREG_SET_ORDER);
        
        foreach ($matches as $match) {
            $bookmarks[] = [
                'title' => trim($match[2]),
                'url' => trim($match[1]),
                'description' => '',
                'folder_path' => '', // 简化处理，不解析文件夹结构
                'tags' => []
            ];
        }

        return [
            'bookmarks' => $bookmarks,
            'folders' => $folders
        ];
    }

    /**
     * 解析JSON格式的书签文件
     * @param string $content JSON内容
     * @return array 解析后的书签数据
     */
    private function parseJsonBookmarks(string $content): array
    {
        $data = json_decode($content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new BusinessException('JSON格式错误', 30003);
        }

        $bookmarks = [];
        $folders = [];

        // 处理标准的书签JSON格式
        if (isset($data['bookmarks'])) {
            foreach ($data['bookmarks'] as $item) {
                $bookmarks[] = [
                    'title' => $item['title'] ?? '',
                    'url' => $item['url'] ?? '',
                    'description' => $item['description'] ?? '',
                    'folder_path' => $item['folder'] ?? '',
                    'tags' => $item['tags'] ?? []
                ];
            }
        }

        return [
            'bookmarks' => $bookmarks,
            'folders' => $folders
        ];
    }

    /**
     * 处理导入过程
     * @param int $userId 用户ID
     * @param array $data 书签数据
     * @param int|null $targetFolderId 目标文件夹ID
     * @param bool $mergeDuplicates 是否合并重复
     * @param bool $createFolders 是否创建文件夹
     * @return array 导入结果
     */
    private function processImport(int $userId, array $data, ?int $targetFolderId, bool $mergeDuplicates, bool $createFolders): array
    {
        $result = [
            'job_id' => 'import_' . date('Ymd_His'),
            'total_count' => count($data['bookmarks']),
            'imported_count' => 0,
            'skipped_count' => 0,
            'folders_created' => 0,
            'tags_created' => 0,
            'status' => 'processing',
            'errors' => []
        ];

        Db::startTrans();
        try {
            $folderMap = []; // 文件夹路径映射
            $tagMap = []; // 标签映射

            foreach ($data['bookmarks'] as $bookmarkData) {
                try {
                    // 处理文件夹
                    $folderId = $targetFolderId;
                    if ($createFolders && !empty($bookmarkData['folder_path'])) {
                        $folderId = $this->createOrGetFolder($userId, $bookmarkData['folder_path'], $folderMap, $result);
                    }

                    // 检查重复
                    if ($mergeDuplicates) {
                        $existing = Bookmark::where('user_id', $userId)
                            ->where('url', $bookmarkData['url'])
                            ->find();
                        if ($existing) {
                            $result['skipped_count']++;
                            continue;
                        }
                    }

                    // 创建书签
                    $bookmark = new Bookmark();
                    $bookmark->user_id = $userId;
                    $bookmark->folder_id = $folderId;
                    $bookmark->title = $bookmarkData['title'];
                    $bookmark->url = $bookmarkData['url'];
                    $bookmark->description = $bookmarkData['description'];
                    $bookmark->sort_order = Bookmark::getNextSortOrder($userId, $folderId);

                    if ($bookmark->save()) {
                        // 处理标签
                        if (!empty($bookmarkData['tags'])) {
                            $this->processBookmarkTags($bookmark->id, $bookmarkData['tags'], $userId, $tagMap, $result);
                        }
                        $result['imported_count']++;
                    } else {
                        $result['errors'][] = "导入书签失败: {$bookmarkData['title']}";
                    }

                } catch (\Exception $e) {
                    $result['errors'][] = "处理书签时出错: {$bookmarkData['title']} - {$e->getMessage()}";
                }
            }

            $result['status'] = 'completed';
            Db::commit();

        } catch (\Exception $e) {
            Db::rollback();
            $result['status'] = 'failed';
            $result['errors'][] = '导入过程中发生错误: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * 创建或获取文件夹
     * @param int $userId 用户ID
     * @param string $folderPath 文件夹路径
     * @param array &$folderMap 文件夹映射
     * @param array &$result 结果统计
     * @return int|null 文件夹ID
     */
    private function createOrGetFolder(int $userId, string $folderPath, array &$folderMap, array &$result): ?int
    {
        if (isset($folderMap[$folderPath])) {
            return $folderMap[$folderPath];
        }

        // 简化处理，只创建一级文件夹
        $folderName = trim($folderPath);
        if (empty($folderName)) {
            return null;
        }

        // 检查是否已存在
        $folder = Folder::where('user_id', $userId)
            ->where('name', $folderName)
            ->where('parent_id', null)
            ->find();

        if (!$folder) {
            // 创建新文件夹
            $folder = new Folder();
            $folder->user_id = $userId;
            $folder->name = $folderName;
            $folder->parent_id = null;
            $folder->sort_order = Folder::getNextSortOrder($userId, null);

            if ($folder->save()) {
                $result['folders_created']++;
            }
        }

        $folderMap[$folderPath] = $folder->id;
        return $folder->id;
    }

    /**
     * 处理书签标签
     * @param int $bookmarkId 书签ID
     * @param array $tags 标签列表
     * @param int $userId 用户ID
     * @param array &$tagMap 标签映射
     * @param array &$result 结果统计
     */
    private function processBookmarkTags(int $bookmarkId, array $tags, int $userId, array &$tagMap, array &$result): void
    {
        foreach ($tags as $tagName) {
            $tagName = trim($tagName);
            if (empty($tagName)) {
                continue;
            }

            // 获取或创建标签
            if (!isset($tagMap[$tagName])) {
                $tag = Tag::where('user_id', $userId)
                    ->where('name', $tagName)
                    ->find();

                if (!$tag) {
                    $tag = new Tag();
                    $tag->user_id = $userId;
                    $tag->name = $tagName;
                    $tag->usage_count = 0;
                    
                    if ($tag->save()) {
                        $result['tags_created']++;
                    }
                }

                $tagMap[$tagName] = $tag->id;
            }

            // 创建书签标签关联
            $bookmarkTag = new BookmarkTag();
            $bookmarkTag->bookmark_id = $bookmarkId;
            $bookmarkTag->tag_id = $tagMap[$tagName];
            $bookmarkTag->save();

            // 更新标签使用次数
            Tag::where('id', $tagMap[$tagName])->inc('usage_count');
        }
    }
}
