# Web端书签管理系统 - 后端API

基于 ThinkPHP 8.1.2 开发的现代化书签管理系统后端API。

## 🚀 快速开始

### 环境要求
- PHP 8.0+
- MySQL 8.0+
- Composer 2.0+
- Redis（可选）

### 安装依赖
```bash
composer install
```

### 环境配置
复制并编辑 `.env` 文件配置数据库等信息：
```bash
cp .env.example .env
```

### 启动开发
```bash
php -S localhost:8000 -t public
```

### 访问地址
- 本地访问: http://localhost:8000
- 外部访问: https://vscode.qidian.cc

## 📖 功能说明

- ✅ **用户认证**：注册、登录、JWT令牌管理
- ✅ **文件夹管理**：无限层级文件夹，支持拖拽排序
- ✅ **书签管理**：CRUD操作、批量操作、标签系统
- ✅ **搜索功能**：全文搜索、高级筛选、智能文件夹
- ✅ **导入导出**：支持浏览器书签导入/导出
- 🚧 **AI解析**：智能提取网页信息和标签
- 🚧 **死链检测**：定期检查书签有效性
- 🚧 **统计分析**：使用统计和数据可视化

## 🛠️ 技术栈

- **后端框架**：ThinkPHP 8.1.2
- **数据库**：MySQL 8.0
- **缓存**：Redis / File
- **认证**：JWT
- **文档**：完整的API文档

## 📋 API 文档

- **OpenAPI 规范文档**: [api-openapi.json](../docs/api-openapi.json)
- **API 使用说明**: [API使用说明.md](../docs/API使用说明.md)
- **详细API文档**: [API.md](../docs/1.4API文档.md)

### 主要接口
- `POST /auth/login` - 用户登录
- `POST /auth/register` - 用户注册
- `GET /bookmarks` - 获取书签列表
- `POST /bookmarks` - 创建书签
- `GET /folders` - 获取文件夹树
- `POST /folders` - 创建文件夹
- `GET /tags` - 获取标签列表
- `GET /search` - 全文搜索

## 🗄️ 数据库

### 数据库初始化
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE bookmark_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入数据库结构
mysql -u root -p bookmark_system < ../Docs/1.8数据库脚本.sql
```

### 主要数据表
- `users` - 用户表
- `folders` - 文件夹表
- `bookmarks` - 书签表
- `tags` - 标签表
- `bookmark_tags` - 书签标签关联表

## 🔧 开发指南

### 目录结构
```
backend/
├── app/                    # 应用目录
│   ├── controller/        # 控制器
│   ├── model/            # 模型
│   ├── service/          # 服务层
│   ├── validate/         # 验证器
│   └── middleware/       # 中间件
├── config/               # 配置文件
├── public/              # 入口文件
├── route/               # 路由定义
└── runtime/             # 运行时文件
```

### 开发规范
- 遵循 PSR-2 代码规范
- 使用 MVC + Service 架构
- 统一的错误码和响应格式
- 完整的参数验证和异常处理

## 🚀 部署

### 生产环境配置
1. 修改 `.env` 文件中的配置
2. 设置 `APP_DEBUG = false`
3. 配置 Web 服务器指向 `public` 目录
4. 设置适当的文件权限

### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/backend/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## 📝 更新日志

### v1.0.0 (2024-06-28)
- ✅ ThinkPHP 8.1.2 框架安装完成
- ✅ 基础项目结构搭建
- ✅ 数据库配置完成
- ✅ 环境配置文件创建
- 🚧 开始开发核心功能模块
