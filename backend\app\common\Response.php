<?php

namespace app\common;

use think\Response as ThinkResponse;

/**
 * 统一响应格式工具类
 */
class Response
{
    /**
     * 成功响应
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @param int $code 业务状态码
     * @param int $httpCode HTTP状态码
     * @return \think\Response
     */
    public static function success($data = null, string $message = '操作成功', int $code = 0, int $httpCode = 200): ThinkResponse
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ], $httpCode);
    }

    /**
     * 失败响应
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $errors 详细错误信息
     * @param int $httpCode HTTP状态码
     * @return \think\Response
     */
    public static function error(string $message = '操作失败', int $code = 40001, $errors = null, int $httpCode = 400): ThinkResponse
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'data' => null,
            'timestamp' => time()
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        return json($response, $httpCode);
    }

    /**
     * 分页响应
     * @param array $items 数据列表
     * @param array $meta 分页信息
     * @param string $message 响应消息
     * @return \think\Response
     */
    public static function paginate(array $items, array $meta, string $message = '获取成功'): ThinkResponse
    {
        return self::success([
            'items' => $items,
            'meta' => $meta
        ], $message);
    }

    /**
     * 创建成功响应
     * @param mixed $data 创建的数据
     * @param string $message 响应消息
     * @return \think\Response
     */
    public static function created($data = null, string $message = '创建成功'): ThinkResponse
    {
        return self::success($data, $message, 0, 201);
    }

    /**
     * 无内容响应
     * @param string $message 响应消息
     * @return \think\Response
     */
    public static function noContent(string $message = '操作成功'): ThinkResponse
    {
        return self::success(null, $message, 0, 204);
    }

    /**
     * 未认证响应
     * @param string $message 错误消息
     * @return \think\Response
     */
    public static function unauthorized(string $message = '未认证或认证失效'): ThinkResponse
    {
        return self::error($message, 40101, null, 401);
    }

    /**
     * 无权限响应
     * @param string $message 错误消息
     * @return \think\Response
     */
    public static function forbidden(string $message = '无权限访问'): ThinkResponse
    {
        return self::error($message, 40301, null, 403);
    }

    /**
     * 资源不存在响应
     * @param string $message 错误消息
     * @return \think\Response
     */
    public static function notFound(string $message = '资源不存在'): ThinkResponse
    {
        return self::error($message, 40401, null, 404);
    }

    /**
     * 参数验证失败响应
     * @param mixed $errors 验证错误详情
     * @param string $message 错误消息
     * @return \think\Response
     */
    public static function validateFailed($errors, string $message = '请求参数验证失败'): ThinkResponse
    {
        return self::error($message, 40001, $errors, 422);
    }

    /**
     * 请求过于频繁响应
     * @param string $message 错误消息
     * @return \think\Response
     */
    public static function tooManyRequests(string $message = '请求过于频繁'): ThinkResponse
    {
        return self::error($message, 42901, null, 429);
    }

    /**
     * 服务器内部错误响应
     * @param string $message 错误消息
     * @return \think\Response
     */
    public static function serverError(string $message = '服务器内部错误'): ThinkResponse
    {
        return self::error($message, 50001, null, 500);
    }
}
