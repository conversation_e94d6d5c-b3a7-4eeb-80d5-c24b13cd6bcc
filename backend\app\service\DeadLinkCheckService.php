<?php

namespace app\service;

use app\model\Bookmark;
use app\exception\BusinessException;
use think\facade\Log;

/**
 * 死链检测服务类
 * 
 * 提供书签URL的死链检测功能，包括单个检测和批量检测
 */
class DeadLinkCheckService
{
    /**
     * 检测配置
     */
    private const DEFAULT_TIMEOUT = 30;           // 默认超时时间（秒）
    private const DEFAULT_CONNECT_TIMEOUT = 10;  // 默认连接超时时间（秒）
    private const MAX_REDIRECTS = 5;              // 最大重定向次数
    private const USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    
    /**
     * 检测单个书签的死链状态
     * 
     * @param int $userId 用户ID
     * @param int $bookmarkId 书签ID
     * @return array 检测结果
     * @throws BusinessException
     */
    public function checkBookmark(int $userId, int $bookmarkId): array
    {
        // 查找书签
        $bookmark = Bookmark::where('id', $bookmarkId)
            ->where('user_id', $userId)
            ->find();

        if (!$bookmark) {
            throw BusinessException::bookmarkNotFound();
        }

        // 执行检测
        $checkResult = $this->performUrlCheck($bookmark->url);
        
        // 更新书签状态
        $this->updateBookmarkStatus($bookmark, $checkResult);
        
        // 记录日志
        Log::info('书签死链检测完成', [
            'user_id' => $userId,
            'bookmark_id' => $bookmarkId,
            'url' => $bookmark->url,
            'is_dead' => $checkResult['is_dead'],
            'status_code' => $checkResult['status_code'],
            'response_time' => $checkResult['response_time']
        ]);
        
        return [
            'bookmark_id' => $bookmarkId,
            'url' => $bookmark->url,
            'is_dead' => $checkResult['is_dead'],
            'status_code' => $checkResult['status_code'],
            'response_time' => $checkResult['response_time'],
            'error_message' => $checkResult['error_message'],
            'last_check_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 批量检测用户的所有书签
     * 
     * @param int $userId 用户ID
     * @param array $options 检测选项
     * @return array 批量检测结果
     */
    public function batchCheckBookmarks(int $userId, array $options = []): array
    {
        $limit = $options['limit'] ?? 100;  // 每次检测的数量限制
        $onlyDeadLinks = $options['only_dead_links'] ?? false;  // 是否只检测已标记为死链的书签
        
        // 构建查询条件
        $query = Bookmark::where('user_id', $userId);
        
        if ($onlyDeadLinks) {
            $query->where('is_dead', 1);
        }
        
        // 优先检测长时间未检测的书签
        $bookmarks = $query->order('last_check_at', 'asc')
            ->limit($limit)
            ->select();
        
        $results = [
            'total_checked' => 0,
            'dead_links' => 0,
            'alive_links' => 0,
            'errors' => 0,
            'details' => []
        ];
        
        foreach ($bookmarks as $bookmark) {
            try {
                $checkResult = $this->performUrlCheck($bookmark->url);
                $this->updateBookmarkStatus($bookmark, $checkResult);
                
                $results['total_checked']++;
                
                if ($checkResult['is_dead']) {
                    $results['dead_links']++;
                } else {
                    $results['alive_links']++;
                }
                
                $results['details'][] = [
                    'bookmark_id' => $bookmark->id,
                    'url' => $bookmark->url,
                    'title' => $bookmark->title,
                    'is_dead' => $checkResult['is_dead'],
                    'status_code' => $checkResult['status_code'],
                    'response_time' => $checkResult['response_time'],
                    'error_message' => $checkResult['error_message']
                ];
                
                // 避免请求过于频繁
                usleep(500000); // 0.5秒延迟
                
            } catch (\Exception $e) {
                $results['errors']++;
                $results['details'][] = [
                    'bookmark_id' => $bookmark->id,
                    'url' => $bookmark->url,
                    'title' => $bookmark->title,
                    'is_dead' => 1,
                    'status_code' => null,
                    'response_time' => null,
                    'error_message' => $e->getMessage()
                ];
                
                Log::error('批量死链检测失败', [
                    'user_id' => $userId,
                    'bookmark_id' => $bookmark->id,
                    'url' => $bookmark->url,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        Log::info('批量死链检测完成', [
            'user_id' => $userId,
            'total_checked' => $results['total_checked'],
            'dead_links' => $results['dead_links'],
            'alive_links' => $results['alive_links'],
            'errors' => $results['errors']
        ]);
        
        return $results;
    }
    
    /**
     * 执行URL检测
     *
     * @param string $url 要检测的URL
     * @return array 检测结果
     */
    public function performUrlCheck(string $url): array
    {
        $startTime = microtime(true);
        $result = [
            'is_dead' => 0,
            'status_code' => null,
            'response_time' => null,
            'error_message' => null
        ];
        
        try {
            $ch = curl_init();
            
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => self::MAX_REDIRECTS,
                CURLOPT_TIMEOUT => self::DEFAULT_TIMEOUT,
                CURLOPT_CONNECTTIMEOUT => self::DEFAULT_CONNECT_TIMEOUT,
                CURLOPT_USERAGENT => self::USER_AGENT,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_NOBODY => true,  // 只获取头部信息，不下载内容
                CURLOPT_HTTPHEADER => [
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                    'Cache-Control: no-cache',
                ],
            ]);
            
            curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            
            curl_close($ch);
            
            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2); // 转换为毫秒
            
            $result['response_time'] = $responseTime;
            
            if ($error) {
                $result['is_dead'] = 1;
                $result['error_message'] = $error;
            } elseif ($httpCode >= 400) {
                $result['is_dead'] = 1;
                $result['status_code'] = $httpCode;
                $result['error_message'] = "HTTP错误：{$httpCode}";
            } else {
                $result['is_dead'] = 0;
                $result['status_code'] = $httpCode;
            }
            
        } catch (\Exception $e) {
            $endTime = microtime(true);
            $result['response_time'] = round(($endTime - $startTime) * 1000, 2);
            $result['is_dead'] = 1;
            $result['error_message'] = $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * 更新书签的检测状态
     * 
     * @param Bookmark $bookmark 书签模型
     * @param array $checkResult 检测结果
     */
    private function updateBookmarkStatus(Bookmark $bookmark, array $checkResult): void
    {
        $bookmark->is_dead = $checkResult['is_dead'];
        $bookmark->status_code = $checkResult['status_code'];
        $bookmark->response_time = $checkResult['response_time'];
        $bookmark->error_message = $checkResult['error_message'];
        $bookmark->last_check_at = date('Y-m-d H:i:s');
        $bookmark->save();
    }
    
    /**
     * 获取用户的死链统计信息
     * 
     * @param int $userId 用户ID
     * @return array 统计信息
     */
    public function getDeadLinkStats(int $userId): array
    {
        $totalBookmarks = Bookmark::where('user_id', $userId)->count();
        $deadLinks = Bookmark::where('user_id', $userId)->where('is_dead', 1)->count();
        $checkedBookmarks = Bookmark::where('user_id', $userId)
            ->where('last_check_at', '<>', null)
            ->count();
        $uncheckedBookmarks = $totalBookmarks - $checkedBookmarks;
        
        return [
            'total_bookmarks' => $totalBookmarks,
            'dead_links' => $deadLinks,
            'alive_links' => $totalBookmarks - $deadLinks,
            'checked_bookmarks' => $checkedBookmarks,
            'unchecked_bookmarks' => $uncheckedBookmarks,
            'dead_link_rate' => $totalBookmarks > 0 ? round(($deadLinks / $totalBookmarks) * 100, 2) : 0
        ];
    }
    
    /**
     * 清理死链（将死链移动到回收站）
     * 
     * @param int $userId 用户ID
     * @param array $options 清理选项
     * @return array 清理结果
     */
    public function cleanDeadLinks(int $userId, array $options = []): array
    {
        $daysOld = $options['days_old'] ?? 30;  // 死链存在天数
        $autoConfirm = $options['auto_confirm'] ?? false;  // 是否自动确认清理
        
        // 查找长期存在的死链
        $deadLinks = Bookmark::where('user_id', $userId)
            ->where('is_dead', 1)
            ->where('last_check_at', '<', date('Y-m-d H:i:s', strtotime("-{$daysOld} days")))
            ->select();
        
        $cleanedCount = 0;
        
        if ($autoConfirm && !empty($deadLinks)) {
            foreach ($deadLinks as $bookmark) {
                // 这里可以集成回收站功能
                $bookmark->delete();
                $cleanedCount++;
            }
        }
        
        return [
            'found_dead_links' => count($deadLinks),
            'cleaned_count' => $cleanedCount,
            'dead_links' => $deadLinks->map(function($bookmark) {
                return [
                    'id' => $bookmark->id,
                    'title' => $bookmark->title,
                    'url' => $bookmark->url,
                    'last_check_at' => $bookmark->last_check_at,
                    'error_message' => $bookmark->error_message
                ];
            })->toArray()
        ];
    }
}
