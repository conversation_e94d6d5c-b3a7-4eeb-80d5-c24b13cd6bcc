<?php

namespace app\model;

use think\Model;

class Tag extends Model
{
    protected $name = 'tags';
    protected $pk = 'id';
    protected $autoWriteTimestamp = true;

    protected $type = [
        'id' => 'integer',
        'user_id' => 'integer',
        'usage_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function bookmarks()
    {
        return $this->belongsToMany(Bookmark::class, 'bookmark_tags', 'tag_id', 'bookmark_id');
    }

    public static function nameExists(string $name, int $userId, ?int $excludeId = null): bool
    {
        $query = self::where('user_id', $userId)->where('name', $name);
        if ($excludeId) {
            $query->where('id', '<>', $excludeId);
        }
        return $query->count() > 0;
    }

    public function incrementUsage(): bool
    {
        $this->usage_count++;
        return $this->save();
    }

    public function decrementUsage(): bool
    {
        if ($this->usage_count > 0) {
            $this->usage_count--;
            return $this->save();
        }
        return true;
    }

    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'color' => $this->color,
            'usage_count' => $this->usage_count,
            'created_at' => $this->formatDateTime($this->created_at),
            'updated_at' => $this->formatDateTime($this->updated_at),
        ];
    }

    /**
     * 安全地格式化日期时间
     * @param mixed $datetime
     * @return string|null
     */
    private function formatDateTime($datetime): ?string
    {
        if (!$datetime) {
            return null;
        }

        if (is_string($datetime)) {
            return $datetime;
        }

        if ($datetime instanceof \DateTime) {
            return $datetime->format('Y-m-d H:i:s');
        }

        return null;
    }
}
