{"openapi": "3.1.0", "info": {"title": "Web端书签管理系统 API", "description": "基于ThinkPHP 8.0开发的书签管理系统API文档\n\n## 功能特性\n- 🔐 **用户认证**：JWT令牌认证，支持注册、登录、令牌刷新\n- 📁 **文件夹管理**：支持多层级文件夹结构，拖拽排序\n- 🏷️ **标签管理**：灵活的标签系统，支持标签云和合并\n- 🔖 **书签管理**：完整的CRUD操作，批量处理，AI解析\n- 🔍 **搜索功能**：全文搜索和高级筛选\n- 🧠 **智能文件夹**：基于条件的动态书签分组\n- 📤 **导入导出**：支持HTML、JSON、CSV格式\n- 📊 **统计分析**：使用情况统计和数据分析\n- 🗑️ **回收站**：软删除和恢复机制\n- ⚙️ **系统管理**：系统配置和状态监控\n\n## 认证方式\n使用JWT Bearer Token认证：\n```http\nAuthorization: Bearer <your-jwt-token>\n```\n\n## 错误码说明\n- 200: 成功\n- 201: 创建成功\n- 400: 请求参数错误\n- 401: 未认证\n- 403: 权限不足\n- 404: 资源不存在\n- 422: 验证失败\n- 500: 服务器错误", "version": "1.0.0", "contact": {"name": "API Support", "url": "https://vscode.qidian.cc", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "https://vscode.qidian.cc/api/v1", "description": "生产环境"}, {"url": "http://localhost:8000/api/v1", "description": "开发环境"}], "security": [{"BearerAuth": []}], "tags": [{"name": "用户认证", "description": "用户注册、登录、令牌管理"}, {"name": "用户管理", "description": "用户信息管理和偏好设置"}, {"name": "系统管理", "description": "系统配置和状态监控"}, {"name": "文件夹管理", "description": "文件夹的增删改查和层级管理"}, {"name": "标签管理", "description": "标签的增删改查和标签云"}, {"name": "书签管理", "description": "书签的完整生命周期管理"}, {"name": "搜索功能", "description": "全文搜索和高级筛选"}, {"name": "智能文件夹", "description": "基于条件的智能分组"}, {"name": "导入导出", "description": "数据导入导出和任务管理"}, {"name": "统计分析", "description": "使用统计和数据分析"}, {"name": "回收站", "description": "已删除项目的管理"}], "paths": {"/auth/register": {"post": {"tags": ["用户认证"], "summary": "用户注册", "description": "创建新用户账户，支持邮箱和用户名注册", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string", "minLength": 3, "maxLength": 50, "examples": ["testuser"]}, "email": {"type": "string", "format": "email", "examples": ["<EMAIL>"]}, "password": {"type": "string", "minLength": 6, "maxLength": 50, "examples": ["123456"]}, "password_confirm": {"type": "string", "examples": ["123456"]}, "nickname": {"type": "string", "maxLength": 50, "examples": ["测试用户"]}}, "required": ["username", "email", "password", "password_confirm"]}}}}, "responses": {"201": {"description": "注册成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [201]}, "message": {"type": "string", "examples": ["注册成功"]}, "data": {"$ref": "#/components/schemas/User"}}}}}}, "422": {"description": "验证失败", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/login": {"post": {"tags": ["用户认证"], "summary": "用户登录", "description": "用户登录验证，支持邮箱或用户名登录，返回JWT令牌", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"type": "object", "properties": {"account": {"type": "string", "examples": ["<EMAIL>"]}, "login_password": {"type": "string", "examples": ["123456"]}}, "required": ["account", "login_password"]}, {"type": "object", "properties": {"email": {"type": "string", "format": "email", "examples": ["<EMAIL>"]}, "password": {"type": "string", "examples": ["123456"]}}, "required": ["email", "password"]}]}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["登录成功"]}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "token": {"type": "string", "examples": ["eyJ0eXAi..."]}, "refresh_token": {"type": "string", "examples": ["eyJ0eXAi..."]}, "expires_in": {"type": "integer", "examples": [7200]}}}}}}}}}}}, "/auth/refresh": {"post": {"tags": ["用户认证"], "summary": "刷新令牌", "description": "使用refresh_token刷新access_token", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"refresh_token": {"type": "string", "examples": ["eyJ0eXAi..."]}}, "required": ["refresh_token"]}}}}, "responses": {"200": {"description": "刷新令牌成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["令牌刷新成功"]}, "data": {"type": "object", "properties": {"token": {"type": "string", "examples": ["eyJ0eXAi..."]}, "refresh_token": {"type": "string", "examples": ["eyJ0eXAi..."]}, "expires_in": {"type": "integer", "examples": [7200]}}}}}}}}}}}, "/auth/logout": {"post": {"tags": ["用户认证"], "summary": "用户登出", "description": "用户登出，使当前令牌失效", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"refresh_token": {"type": "string", "examples": ["eyJ0eXAi..."]}, "all_devices": {"type": "boolean", "examples": [false]}}}}}}, "responses": {"200": {"description": "登出成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["登出成功"]}, "data": {"type": "null"}}}}}}}}}, "/user/profile": {"get": {"tags": ["用户管理"], "summary": "获取用户信息", "description": "获取当前登录用户的详细信息", "responses": {"200": {"description": "获取用户信息成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取用户信息成功"]}, "data": {"$ref": "#/components/schemas/User"}}}}}}}}, "put": {"tags": ["用户管理"], "summary": "更新用户信息", "description": "更新当前用户的个人信息", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"nickname": {"type": "string", "maxLength": 50, "examples": ["新昵称"]}, "email": {"type": "string", "format": "email", "examples": ["<EMAIL>"]}, "avatar": {"type": "string", "format": "uri", "examples": ["https://example.com/avatar.jpg"]}}}}}}, "responses": {"200": {"description": "更新用户信息成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["更新用户信息成功"]}, "data": {"$ref": "#/components/schemas/User"}}}}}}}}}, "/user/change-password": {"post": {"tags": ["用户管理"], "summary": "修改密码", "description": "修改当前用户的登录密码", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"old_password": {"type": "string", "examples": ["123456"]}, "new_password": {"type": "string", "minLength": 6, "examples": ["newpass123"]}, "new_password_confirmation": {"type": "string", "examples": ["newpass123"]}}, "required": ["old_password", "new_password", "new_password_confirmation"]}}}}, "responses": {"200": {"description": "修改密码成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["修改密码成功"]}, "data": {"type": "null"}}}}}}}}}, "/user/preferences": {"get": {"tags": ["用户管理"], "summary": "获取用户偏好设置", "description": "获取当前用户的个性化偏好设置", "responses": {"200": {"description": "获取用户偏好设置成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取用户偏好设置成功"]}, "data": {"type": "object", "properties": {"theme": {"type": "string", "enum": ["light", "dark", "auto"], "examples": ["dark"]}, "language": {"type": "string", "examples": ["zh-CN"]}, "items_per_page": {"type": "integer", "examples": [20]}, "auto_backup": {"type": "boolean", "examples": [true]}, "email_notifications": {"type": "boolean", "examples": [true]}}}}}}}}}}, "put": {"tags": ["用户管理"], "summary": "更新用户偏好设置", "description": "更新当前用户的个性化偏好设置", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"theme": {"type": "string", "enum": ["light", "dark", "auto"], "examples": ["light"]}, "language": {"type": "string", "examples": ["en-US"]}, "items_per_page": {"type": "integer", "minimum": 10, "maximum": 100, "examples": [50]}, "auto_backup": {"type": "boolean", "examples": [false]}, "email_notifications": {"type": "boolean", "examples": [true]}}}}}}, "responses": {"200": {"description": "更新用户偏好设置成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["更新用户偏好设置成功"]}, "data": {"type": "null"}}}}}}}}}, "/system/config": {"get": {"tags": ["系统管理"], "summary": "获取系统配置", "description": "获取系统的公开配置信息", "security": [], "responses": {"200": {"description": "获取系统配置成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取系统配置成功"]}, "data": {"type": "object", "properties": {"app_name": {"type": "string", "examples": ["Web端书签管理系统"]}, "version": {"type": "string", "examples": ["1.0.0"]}, "api_version": {"type": "string", "examples": ["v1"]}, "framework": {"type": "string", "examples": ["ThinkPHP 8.1.2"]}, "features": {"type": "object", "properties": {"user_auth": {"type": "boolean", "examples": [true]}, "folder_management": {"type": "boolean", "examples": [true]}, "ai_parsing": {"type": "boolean", "examples": [false]}}}}}}}}}}}}}, "/system/status": {"get": {"tags": ["系统管理"], "summary": "获取系统状态", "description": "获取系统运行状态信息", "security": [], "responses": {"200": {"description": "获取系统状态成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["API运行正常"]}, "data": {"type": "object", "properties": {"status": {"type": "string", "examples": ["ok"]}, "timestamp": {"type": "integer", "examples": [1719734400]}, "datetime": {"type": "string", "examples": ["2024-06-30 12:00:00"]}, "uptime": {"type": "integer", "examples": [86400]}, "database": {"type": "object", "properties": {"status": {"type": "string", "examples": ["connected"]}, "message": {"type": "string", "examples": ["数据库连接正常"]}}}}}}}}}}}}}, "/folders": {"get": {"tags": ["文件夹管理"], "summary": "获取文件夹列表", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 20}}, {"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取文件夹列表成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取列表成功"]}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/Folder"}}, "pagination": {"type": "object", "properties": {"current_page": {"type": "integer", "examples": [1]}, "per_page": {"type": "integer", "examples": [20]}, "total": {"type": "integer", "examples": [100]}, "last_page": {"type": "integer", "examples": [5]}}}}}}}}}}}}, "post": {"tags": ["文件夹管理"], "summary": "创建文件夹", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Folder"}}}}, "responses": {"201": {"description": "创建文件夹成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [201]}, "message": {"type": "string", "examples": ["创建文件夹成功"]}, "data": {"$ref": "#/components/schemas/Folder"}}}}}}}}}, "/folders/{id}": {"get": {"tags": ["文件夹管理"], "summary": "获取文件夹详情", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "获取文件夹详情成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取文件夹详情成功"]}, "data": {"$ref": "#/components/schemas/Folder"}}}}}}}}, "put": {"tags": ["文件夹管理"], "summary": "更新文件夹", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Folder"}}}}, "responses": {"200": {"description": "更新文件夹成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["更新文件夹成功"]}, "data": {"$ref": "#/components/schemas/Folder"}}}}}}}}, "delete": {"tags": ["文件夹管理"], "summary": "删除文件夹", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除文件夹成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["删除文件夹成功"]}, "data": {"type": "null"}}}}}}}}}, "/folders/{id}/move": {"patch": {"tags": ["文件夹管理"], "summary": "移动文件夹", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"parent_id": {"type": ["integer", "null"], "examples": [2]}}}}}}, "responses": {"200": {"description": "移动文件夹成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["移动文件夹成功"]}, "data": {"$ref": "#/components/schemas/Folder"}}}}}}}}}, "/folders/sort": {"patch": {"tags": ["文件夹管理"], "summary": "批量排序文件夹", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"folders": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "sort_order": {"type": "integer"}}, "required": ["id", "sort_order"]}}}, "required": ["folders"]}}}}, "responses": {"200": {"description": "排序成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["排序成功"]}, "data": {"type": "null"}}}}}}}}}, "/tags": {"get": {"tags": ["标签管理"], "summary": "获取标签列表", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 20}}, {"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取标签列表成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取列表成功"]}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "pagination": {"type": "object", "properties": {"current_page": {"type": "integer", "examples": [1]}, "per_page": {"type": "integer", "examples": [20]}, "total": {"type": "integer", "examples": [100]}, "last_page": {"type": "integer", "examples": [5]}}}}}}}}}}}}, "post": {"tags": ["标签管理"], "summary": "创建标签", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tag"}}}}, "responses": {"201": {"description": "创建标签成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [201]}, "message": {"type": "string", "examples": ["创建标签成功"]}, "data": {"$ref": "#/components/schemas/Tag"}}}}}}}}}, "/tags/{id}": {"get": {"tags": ["标签管理"], "summary": "获取标签详情", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "获取标签详情成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取标签详情成功"]}, "data": {"$ref": "#/components/schemas/Tag"}}}}}}}}, "put": {"tags": ["标签管理"], "summary": "更新标签", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tag"}}}}, "responses": {"200": {"description": "更新标签成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["更新标签成功"]}, "data": {"$ref": "#/components/schemas/Tag"}}}}}}}}, "delete": {"tags": ["标签管理"], "summary": "删除标签", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除标签成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["删除标签成功"]}, "data": {"type": "null"}}}}}}}}}, "/tags/merge": {"post": {"tags": ["标签管理"], "summary": "合并标签", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"source_tag_ids": {"type": "array", "items": {"type": "integer"}}, "target_tag_id": {"type": "integer"}}, "required": ["source_tag_ids", "target_tag_id"]}}}}, "responses": {"200": {"description": "合并标签成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["合并标签成功"]}, "data": {"type": "null"}}}}}}}}}, "/tags/cloud": {"get": {"tags": ["标签管理"], "summary": "获取标签云", "parameters": [{"name": "limit", "in": "query", "schema": {"type": "integer", "default": 50}}, {"name": "min_count", "in": "query", "schema": {"type": "integer", "default": 1}}], "responses": {"200": {"description": "获取标签云成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取标签云成功"]}, "data": {"type": "array", "items": {"allOf": [{"$ref": "#/components/schemas/Tag"}, {"type": "object", "properties": {"weight": {"type": "number", "examples": [0.8]}}}]}}}}}}}}}}, "/bookmarks": {"get": {"tags": ["书签管理"], "summary": "获取书签列表", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 20}}, {"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取书签列表成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取列表成功"]}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/Bookmark"}}, "pagination": {"type": "object", "properties": {"current_page": {"type": "integer", "examples": [1]}, "per_page": {"type": "integer", "examples": [20]}, "total": {"type": "integer", "examples": [100]}, "last_page": {"type": "integer", "examples": [5]}}}}}}}}}}}}, "post": {"tags": ["书签管理"], "summary": "创建书签", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Bookmark"}}}}, "responses": {"201": {"description": "创建书签成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [201]}, "message": {"type": "string", "examples": ["创建书签成功"]}, "data": {"$ref": "#/components/schemas/Bookmark"}}}}}}}}}, "/bookmarks/{id}": {"get": {"tags": ["书签管理"], "summary": "获取书签详情", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "获取书签详情成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取书签详情成功"]}, "data": {"$ref": "#/components/schemas/Bookmark"}}}}}}}}, "put": {"tags": ["书签管理"], "summary": "更新书签", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Bookmark"}}}}, "responses": {"200": {"description": "更新书签成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["更新书签成功"]}, "data": {"$ref": "#/components/schemas/Bookmark"}}}}}}}}, "delete": {"tags": ["书签管理"], "summary": "删除书签", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除书签成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["删除书签成功"]}, "data": {"type": "null"}}}}}}}}}, "/bookmarks/batch": {"post": {"tags": ["书签管理"], "summary": "批量操作书签", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"bookmark_ids": {"type": "array", "items": {"type": "integer"}}, "action": {"type": "string", "enum": ["delete", "move", "star", "unstar", "add_tags", "remove_tags"]}, "folder_id": {"type": "integer"}, "tags": {"type": "array", "items": {"type": "string"}}}, "required": ["bookmark_ids", "action"]}}}}, "responses": {"200": {"description": "批量操作成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["批量操作成功"]}, "data": {"type": "object", "properties": {"success_count": {"type": "integer"}, "failed_count": {"type": "integer"}, "failed_ids": {"type": "array", "items": {"type": "integer"}}}}}}}}}}}}, "/bookmarks/parse": {"post": {"tags": ["书签管理"], "summary": "AI解析URL", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"url": {"type": "string", "format": "uri"}}, "required": ["url"]}}}}, "responses": {"200": {"description": "AI解析成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["AI解析成功"]}, "data": {"type": "object", "properties": {"title": {"type": "string"}, "summary": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}}}}}}}}}}}, "/bookmarks/batch-check": {"post": {"tags": ["书签管理"], "summary": "批量检测死链", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"limit": {"type": "integer", "default": 100}, "only_dead_links": {"type": "boolean", "default": false}}}}}}, "responses": {"200": {"description": "批量检测完成", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["批量检测完成"]}, "data": {"type": "object", "properties": {"total_checked": {"type": "integer"}, "dead_links": {"type": "integer"}, "alive_links": {"type": "integer"}}}}}}}}}}}, "/bookmarks/dead-link-stats": {"get": {"tags": ["书签管理"], "summary": "获取死链统计", "responses": {"200": {"description": "获取死链统计成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取死链统计成功"]}, "data": {"type": "object", "properties": {"total_bookmarks": {"type": "integer"}, "dead_links": {"type": "integer"}, "alive_links": {"type": "integer"}, "dead_link_rate": {"type": "number"}}}}}}}}}}}, "/bookmarks/{id}/visit": {"post": {"tags": ["书签管理"], "summary": "记录书签访问", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "访问记录成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["访问记录成功"]}, "data": {"type": "null"}}}}}}}}}, "/bookmarks/{id}/parse": {"post": {"tags": ["书签管理"], "summary": "AI解析书签", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "AI解析成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["AI解析成功"]}, "data": {"type": "object", "properties": {"title": {"type": "string"}, "summary": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}}}}}}}}}}}, "/bookmarks/{id}/check": {"post": {"tags": ["书签管理"], "summary": "检查书签死链", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "死链检测完成", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["死链检测完成"]}, "data": {"type": "object", "properties": {"bookmark_id": {"type": "integer"}, "url": {"type": "string"}, "is_dead": {"type": "boolean"}, "status_code": {"type": "integer"}, "response_time": {"type": "number"}}}}}}}}}}}, "/search": {"get": {"tags": ["搜索功能"], "summary": "全文搜索", "parameters": [{"name": "q", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 20}}, {"name": "type", "in": "query", "schema": {"type": "string", "enum": ["all", "title", "url", "description"], "default": "all"}}], "responses": {"200": {"description": "搜索完成", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["搜索完成"]}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/Bookmark"}}, "pagination": {"type": "object", "properties": {"current_page": {"type": "integer", "examples": [1]}, "per_page": {"type": "integer", "examples": [20]}, "total": {"type": "integer", "examples": [100]}, "last_page": {"type": "integer", "examples": [5]}}}}}}}}}}}}}, "/search/filter": {"post": {"tags": ["搜索功能"], "summary": "高级筛选", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"keyword": {"type": "string"}, "folder_ids": {"type": "array", "items": {"type": "integer"}}, "tag_ids": {"type": "array", "items": {"type": "integer"}}, "is_star": {"type": "boolean"}, "is_dead": {"type": "boolean"}, "date_range": {"type": "object", "properties": {"start_date": {"type": "string", "format": "date"}, "end_date": {"type": "string", "format": "date"}}}, "sort": {"type": "string", "enum": ["created_at", "updated_at", "visit_count", "title"], "default": "created_at"}, "order": {"type": "string", "enum": ["asc", "desc"], "default": "desc"}, "page": {"type": "integer", "default": 1}, "per_page": {"type": "integer", "default": 20}}}}}}, "responses": {"200": {"description": "筛选完成", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["筛选完成"]}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/Bookmark"}}, "pagination": {"type": "object", "properties": {"current_page": {"type": "integer", "examples": [1]}, "per_page": {"type": "integer", "examples": [20]}, "total": {"type": "integer", "examples": [100]}, "last_page": {"type": "integer", "examples": [5]}}}}}}}}}}}}}, "/smart-folders": {"get": {"tags": ["智能文件夹"], "summary": "获取智能文件夹列表", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 20}}, {"name": "search", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取智能文件夹列表成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取列表成功"]}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/SmartFolder"}}, "pagination": {"type": "object", "properties": {"current_page": {"type": "integer", "examples": [1]}, "per_page": {"type": "integer", "examples": [20]}, "total": {"type": "integer", "examples": [100]}, "last_page": {"type": "integer", "examples": [5]}}}}}}}}}}}}, "post": {"tags": ["智能文件夹"], "summary": "创建智能文件夹", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartFolder"}}}}, "responses": {"201": {"description": "创建智能文件夹成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [201]}, "message": {"type": "string", "examples": ["创建智能文件夹成功"]}, "data": {"$ref": "#/components/schemas/SmartFolder"}}}}}}}}}, "/smart-folders/{id}": {"get": {"tags": ["智能文件夹"], "summary": "获取智能文件夹详情", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "获取智能文件夹详情成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取智能文件夹详情成功"]}, "data": {"$ref": "#/components/schemas/SmartFolder"}}}}}}}}, "put": {"tags": ["智能文件夹"], "summary": "更新智能文件夹", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartFolder"}}}}, "responses": {"200": {"description": "更新智能文件夹成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["更新智能文件夹成功"]}, "data": {"$ref": "#/components/schemas/SmartFolder"}}}}}}}}, "delete": {"tags": ["智能文件夹"], "summary": "删除智能文件夹", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除智能文件夹成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["删除智能文件夹成功"]}, "data": {"type": "null"}}}}}}}}}, "/smart-folders/{id}/bookmarks": {"get": {"tags": ["智能文件夹"], "summary": "获取智能文件夹内容", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "获取智能文件夹内容成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取智能文件夹内容成功"]}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/Bookmark"}}, "pagination": {"type": "object", "properties": {"current_page": {"type": "integer", "examples": [1]}, "per_page": {"type": "integer", "examples": [20]}, "total": {"type": "integer", "examples": [100]}, "last_page": {"type": "integer", "examples": [5]}}}}}}}}}}}}}, "/import": {"post": {"tags": ["导入导出"], "summary": "导入书签", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "folder_id": {"type": "integer"}, "merge_duplicates": {"type": "boolean", "default": true}, "create_folders": {"type": "boolean", "default": true}}, "required": ["file"]}}}}, "responses": {"200": {"description": "导入书签成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["导入书签成功"]}, "data": {"type": "object", "properties": {"job_id": {"type": "string"}, "total_count": {"type": "integer"}, "imported_count": {"type": "integer"}, "skipped_count": {"type": "integer"}}}}}}}}}}}, "/export": {"post": {"tags": ["导入导出"], "summary": "导出书签", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"format": {"type": "string", "enum": ["html", "json", "csv"], "default": "html"}, "folder_ids": {"type": "array", "items": {"type": "integer"}}, "include_folders": {"type": "boolean", "default": true}, "include_tags": {"type": "boolean", "default": true}}}}}}, "responses": {"200": {"description": "导出书签成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["导出书签成功"]}, "data": {"type": "object", "properties": {"job_id": {"type": "string"}, "download_url": {"type": "string"}, "file_name": {"type": "string"}, "expires_at": {"type": "string"}}}}}}}}}}}, "/jobs/{id}": {"get": {"tags": ["导入导出"], "summary": "获取任务状态", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "获取任务状态成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取任务状态成功"]}, "data": {"type": "object", "properties": {"job_id": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "processing", "completed", "failed"]}, "progress": {"type": "integer"}, "result": {"type": "object"}}}}}}}}}}}, "/stats/overview": {"get": {"tags": ["统计分析"], "summary": "统计概览", "parameters": [{"name": "days", "in": "query", "schema": {"type": "integer", "default": 30}}], "responses": {"200": {"description": "获取统计概览成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取统计概览成功"]}, "data": {"type": "object", "properties": {"total_bookmarks": {"type": "integer"}, "total_folders": {"type": "integer"}, "total_tags": {"type": "integer"}, "star_bookmarks": {"type": "integer"}, "recent_added": {"type": "integer"}, "growth_rate": {"type": "number"}}}}}}}}}}}, "/stats/tags": {"get": {"tags": ["统计分析"], "summary": "标签统计", "parameters": [{"name": "limit", "in": "query", "schema": {"type": "integer", "default": 20}}, {"name": "sort", "in": "query", "schema": {"type": "string", "enum": ["usage", "name", "created"], "default": "usage"}}], "responses": {"200": {"description": "获取标签统计成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取标签统计成功"]}, "data": {"type": "object", "properties": {"total_tags": {"type": "integer"}, "active_tags": {"type": "integer"}, "unused_tags": {"type": "integer"}, "tags": {"type": "array", "items": {"allOf": [{"$ref": "#/components/schemas/Tag"}, {"type": "object", "properties": {"usage_count": {"type": "integer"}, "percentage": {"type": "number"}}}]}}}}}}}}}}}}, "/stats/visits": {"get": {"tags": ["统计分析"], "summary": "访问统计", "parameters": [{"name": "days", "in": "query", "schema": {"type": "integer", "default": 30}}, {"name": "type", "in": "query", "schema": {"type": "string", "enum": ["daily", "weekly", "monthly"], "default": "daily"}}], "responses": {"200": {"description": "获取访问统计成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取访问统计成功"]}, "data": {"type": "object", "properties": {"total_visits": {"type": "integer"}, "unique_bookmarks": {"type": "integer"}, "avg_daily_visits": {"type": "number"}, "most_visited": {"type": "array", "items": {"$ref": "#/components/schemas/Bookmark"}}}}}}}}}}}}, "/stats/report": {"post": {"tags": ["统计分析"], "summary": "生成报告", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "enum": ["summary", "detailed", "export"], "default": "summary"}, "days": {"type": "integer", "default": 30}, "format": {"type": "string", "enum": ["json", "pdf", "excel"], "default": "json"}, "sections": {"type": "array", "items": {"type": "string"}}}}}}}, "responses": {"200": {"description": "生成报告成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["生成报告成功"]}, "data": {"type": "object", "properties": {"report_id": {"type": "string"}, "download_url": {"type": "string"}, "expires_at": {"type": "string"}}}}}}}}}}}, "/trash": {"get": {"tags": ["回收站"], "summary": "获取回收站列表", "parameters": [{"name": "type", "in": "query", "schema": {"type": "string", "enum": ["bookmark", "folder", "all"], "default": "all"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "获取回收站列表成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["获取回收站列表成功"]}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "string"}, "title": {"type": "string"}, "deleted_at": {"type": "string"}, "auto_delete_at": {"type": "string"}}}}, "pagination": {"type": "object", "properties": {"current_page": {"type": "integer", "examples": [1]}, "per_page": {"type": "integer", "examples": [20]}, "total": {"type": "integer", "examples": [100]}, "last_page": {"type": "integer", "examples": [5]}}}}}}}}}}}}}, "/trash/{id}/restore": {"post": {"tags": ["回收站"], "summary": "恢复项目", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "enum": ["bookmark", "folder"]}}, "required": ["type"]}}}}, "responses": {"200": {"description": "恢复项目成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["恢复项目成功"]}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "type": {"type": "string"}, "title": {"type": "string"}, "restored_at": {"type": "string"}}}}}}}}}}}, "/trash/{id}": {"delete": {"tags": ["回收站"], "summary": "永久删除", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}, {"name": "type", "in": "query", "required": true, "schema": {"type": "string", "enum": ["bookmark", "folder"]}}], "responses": {"200": {"description": "永久删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["永久删除成功"]}, "data": {"type": "null"}}}}}}}}}, "/trash/clear": {"delete": {"tags": ["回收站"], "summary": "清空回收站", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"type": {"type": "string", "enum": ["bookmark", "folder", "all"], "default": "all"}, "confirm": {"type": "boolean"}}, "required": ["confirm"]}}}}, "responses": {"200": {"description": "清空回收站成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "examples": [200]}, "message": {"type": "string", "examples": ["清空回收站成功"]}, "data": {"type": "object", "properties": {"deleted_bookmarks": {"type": "integer"}, "deleted_folders": {"type": "integer"}, "total_deleted": {"type": "integer"}}}}}}}}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT访问令牌，格式：Bearer <token>"}}, "schemas": {"User": {"type": "object", "properties": {"id": {"type": "integer", "examples": [1]}, "username": {"type": "string", "examples": ["admin"]}, "email": {"type": "string", "format": "email", "examples": ["<EMAIL>"]}, "nickname": {"type": "string", "examples": ["管理员"]}, "avatar": {"type": "string", "format": "uri", "examples": ["https://example.com/avatar.jpg"]}, "status": {"type": "integer", "examples": [1]}, "created_at": {"type": "string", "format": "date-time", "examples": ["2024-06-30T12:00:00Z"]}, "updated_at": {"type": "string", "format": "date-time", "examples": ["2024-06-30T12:00:00Z"]}}}, "Folder": {"type": "object", "properties": {"id": {"type": "integer", "examples": [1]}, "name": {"type": "string", "examples": ["工作相关"]}, "parent_id": {"type": ["integer", "null"], "examples": [null]}, "icon": {"type": "string", "examples": ["folder"]}, "color": {"type": "string", "examples": ["#1890ff"]}, "sort_order": {"type": "integer", "examples": [1]}, "bookmark_count": {"type": "integer", "examples": [15]}, "created_at": {"type": "string", "examples": ["2024-06-30 12:00:00"]}, "updated_at": {"type": "string", "examples": ["2024-06-30 12:00:00"]}}}, "Tag": {"type": "object", "properties": {"id": {"type": "integer", "examples": [1]}, "name": {"type": "string", "examples": ["技术"]}, "color": {"type": "string", "examples": ["#1890ff"]}, "bookmark_count": {"type": "integer", "examples": [25]}, "created_at": {"type": "string", "examples": ["2024-06-30 12:00:00"]}, "updated_at": {"type": "string", "examples": ["2024-06-30 12:00:00"]}}}, "Bookmark": {"type": "object", "properties": {"id": {"type": "integer", "examples": [1]}, "title": {"type": "string", "examples": ["GitHub"]}, "url": {"type": "string", "format": "uri", "examples": ["https://github.com"]}, "description": {"type": "string", "examples": ["全球最大的代码托管平台"]}, "folder_id": {"type": ["integer", "null"], "examples": [1]}, "is_star": {"type": "boolean", "examples": [true]}, "visit_count": {"type": "integer", "examples": [10]}, "is_dead": {"type": "boolean", "examples": [false]}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "created_at": {"type": "string", "examples": ["2024-06-30 12:00:00"]}, "updated_at": {"type": "string", "examples": ["2024-06-30 12:00:00"]}}}, "Error": {"type": "object", "properties": {"code": {"type": "integer", "examples": [400]}, "message": {"type": "string", "examples": ["请求参数错误"]}, "errors": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}