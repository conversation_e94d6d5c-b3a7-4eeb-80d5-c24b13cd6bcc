<?php

namespace app\model;

use think\Model;

class BookmarkTag extends Model
{
    protected $name = 'bookmark_tags';
    protected $pk = 'id';
    protected $autoWriteTimestamp = 'created_at';

    protected $type = [
        'id' => 'integer',
        'bookmark_id' => 'integer',
        'tag_id' => 'integer',
        'created_at' => 'datetime',
    ];

    public function bookmark()
    {
        return $this->belongsTo(Bookmark::class, 'bookmark_id');
    }

    public function tag()
    {
        return $this->belongsTo(Tag::class, 'tag_id');
    }
}
