<template>
  <div class="bookmark-list">
    <!-- 工具栏 -->
    <div class="list-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button 
            :type="viewMode === 'grid' ? 'primary' : 'default'"
            :icon="Grid"
            @click="setViewMode('grid')"
          >
            卡片视图
          </el-button>
          <el-button 
            :type="viewMode === 'list' ? 'primary' : 'default'"
            :icon="List"
            @click="setViewMode('list')"
          >
            列表视图
          </el-button>
        </el-button-group>
        
        <el-select v-model="sortBy" placeholder="排序方式" style="width: 120px; margin-left: 12px;">
          <el-option label="创建时间" value="created_at" />
          <el-option label="更新时间" value="updated_at" />
          <el-option label="访问次数" value="visit_count" />
          <el-option label="标题" value="title" />
        </el-select>
        
        <el-button 
          :icon="sortOrder === 'asc' ? SortUp : SortDown"
          @click="toggleSortOrder"
          text
        />
      </div>
      
      <div class="toolbar-right">
        <el-button 
          type="primary" 
          :icon="Plus"
          @click="showAddDialog"
        >
          添加书签
        </el-button>
        
        <el-dropdown @command="handleBatchAction" v-if="selectedBookmarks.length > 0">
          <el-button :icon="MoreFilled">
            批量操作 ({{ selectedBookmarks.length }})
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="star">
                <el-icon><Star /></el-icon>
                批量收藏
              </el-dropdown-item>
              <el-dropdown-item command="unstar">
                <el-icon><StarFilled /></el-icon>
                取消收藏
              </el-dropdown-item>
              <el-dropdown-item command="move">
                <el-icon><FolderOpened /></el-icon>
                移动到文件夹
              </el-dropdown-item>
              <el-dropdown-item command="delete" divided>
                <el-icon><Delete /></el-icon>
                批量删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    
    <!-- 空状态 -->
    <AppEmpty
      v-else-if="!bookmarks || bookmarks.length === 0"
      type="bookmark"
      title="暂无书签"
      description="还没有添加任何书签，点击上方按钮开始添加吧"
    >
      <template #actions>
        <el-button type="primary" @click="showAddDialog">
          添加第一个书签
        </el-button>
      </template>
    </AppEmpty>
    
    <!-- 卡片视图 -->
    <div v-else-if="viewMode === 'grid'" class="grid-view">
      <div class="bookmark-grid">
        <div 
          v-for="bookmark in sortedBookmarks" 
          :key="bookmark.id"
          class="bookmark-grid-item"
        >
          <el-checkbox
            :model-value="selectedBookmarks.includes(bookmark.id)"
            @update:model-value="(checked: boolean) => toggleBookmarkSelection(bookmark.id, checked)"
            class="bookmark-checkbox"
          />
          <BookmarkCard 
            :bookmark="bookmark"
            @edit="handleEdit"
            @move="handleMove"
            @delete="handleDelete"
          />
        </div>
      </div>
    </div>
    
    <!-- 列表视图 -->
    <div v-else class="list-view">
      <DataTable
        :data="sortedBookmarks"
        :loading="loading"
        show-header
        show-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        @page-change="handlePageChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="书签" min-width="300">
          <template #default="{ row }">
            <div class="bookmark-info">
              <div class="bookmark-favicon">
                <img 
                  :src="formatUrl.getFavicon(row.url)" 
                  :alt="row.title"
                  @error="($event.target as HTMLImageElement).style.display='none'"
                />
              </div>
              <div class="bookmark-content">
                <div class="bookmark-title">{{ row.title }}</div>
                <div class="bookmark-url">{{ formatUrl.getDomain(row.url) }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="标签" width="200">
          <template #default="{ row }">
            <div class="bookmark-tags">
              <el-tag
                v-for="tag in row.tags?.slice(0, 3)"
                :key="tag.id"
                size="small"
                class="tag-item"
              >
                {{ tag.name }}
              </el-tag>
              <el-tag v-if="row.tags?.length > 3" size="small" type="info">
                +{{ row.tags.length - 3 }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="访问次数" width="100" align="center">
          <template #default="{ row }">
            <span class="visit-count">{{ row.visit_count }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="150">
          <template #default="{ row }">
            <span class="created-time">{{ formatDate.friendly(row.created_at) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <div class="table-actions">
              <el-button 
                :icon="row.is_star ? StarFilled : Star"
                @click="toggleStar(row)"
                text
                :class="{ 'is-starred': row.is_star }"
              />
              <el-dropdown @command="(cmd) => handleRowAction(cmd, row)">
                <el-button :icon="MoreFilled" text />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="move">移动</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </DataTable>
    </div>
    
    <!-- 分页 -->
    <AppPagination
      v-if="viewMode === 'grid' && total > pageSize"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      @change="handlePageChange"
    />
  </div>
</template>

<script setup lang="ts">
import { 
  Grid, 
  List, 
  Plus, 
  MoreFilled, 
  ArrowDown, 
  Star, 
  StarFilled, 
  FolderOpened, 
  Delete,
  SortUp,
  SortDown
} from '@element-plus/icons-vue'
import type { Bookmark } from '@/types/api'
import { formatUrl, formatDate } from '@/utils/format'
import { useBookmarkStore } from '@/stores/bookmark'
import BookmarkCard from './BookmarkCard.vue'
import DataTable from '@/components/common/DataTable.vue'
import AppPagination from '@/components/common/AppPagination.vue'
import AppEmpty from '@/components/common/AppEmpty.vue'

interface Props {
  folderId?: number
  tagId?: number
  starred?: boolean
  search?: string
}

interface Emits {
  (e: 'add'): void
  (e: 'edit', bookmark: Bookmark): void
  (e: 'move', bookmark: Bookmark): void
  (e: 'delete', bookmark: Bookmark): void
}

const props = withDefaults(defineProps<Props>(), {
  starred: false
})

const emit = defineEmits<Emits>()

const bookmarkStore = useBookmarkStore()

// 视图状态
const viewMode = ref<'grid' | 'list'>('grid')
const sortBy = ref('created_at')
const sortOrder = ref<'asc' | 'desc'>('desc')
const selectedBookmarks = ref<number[]>([])

// 分页状态
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const loading = computed(() => bookmarkStore.loading)
const bookmarks = computed(() => bookmarkStore.bookmarks)
const total = computed(() => bookmarkStore.total)

const sortedBookmarks = computed(() => {
  if (!bookmarks.value || !Array.isArray(bookmarks.value)) {
    return []
  }

  const list = [...bookmarks.value]
  return list.sort((a, b) => {
    const aVal = a[sortBy.value as keyof Bookmark]
    const bVal = b[sortBy.value as keyof Bookmark]

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1
    } else {
      return aVal < bVal ? 1 : -1
    }
  })
})

// 方法
const setViewMode = (mode: 'grid' | 'list') => {
  viewMode.value = mode
  localStorage.setItem('bookmark-view-mode', mode)
}

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
}

const showAddDialog = () => {
  emit('add')
}

const handleEdit = (bookmark: Bookmark) => {
  emit('edit', bookmark)
}

const handleMove = (bookmark: Bookmark) => {
  emit('move', bookmark)
}

const handleDelete = (bookmark: Bookmark) => {
  emit('delete', bookmark)
}

const toggleStar = async (bookmark: Bookmark) => {
  try {
    await bookmarkStore.toggleStar(bookmark.id)
  } catch (error) {
    console.error('切换收藏状态失败:', error)
  }
}

const handlePageChange = (page: number, size: number) => {
  currentPage.value = page
  pageSize.value = size
  fetchBookmarks()
}

const handleSelectionChange = (selection: Bookmark[]) => {
  selectedBookmarks.value = selection.map(item => item.id)
}

const toggleBookmarkSelection = (bookmarkId: number, checked: boolean) => {
  if (checked) {
    if (!selectedBookmarks.value.includes(bookmarkId)) {
      selectedBookmarks.value.push(bookmarkId)
    }
  } else {
    const index = selectedBookmarks.value.indexOf(bookmarkId)
    if (index > -1) {
      selectedBookmarks.value.splice(index, 1)
    }
  }
}

const handleBatchAction = async (command: string) => {
  if (selectedBookmarks.value.length === 0) return
  
  try {
    await bookmarkStore.batchOperation(selectedBookmarks.value, command)
    selectedBookmarks.value = []
    ElMessage.success('批量操作成功')
  } catch (error) {
    console.error('批量操作失败:', error)
    ElMessage.error('批量操作失败')
  }
}

const handleRowAction = (command: string, bookmark: Bookmark) => {
  switch (command) {
    case 'edit':
      handleEdit(bookmark)
      break
    case 'move':
      handleMove(bookmark)
      break
    case 'delete':
      handleDelete(bookmark)
      break
  }
}

const fetchBookmarks = async () => {
  try {
    const params = {
      page: currentPage.value,
      per_page: pageSize.value,
      folder_id: props.folderId,
      tag_id: props.tagId,
      is_star: props.starred ? 1 : undefined,
      search: props.search
    }
    await bookmarkStore.fetchBookmarks(params)
  } catch (error) {
    console.error('获取书签列表失败:', error)
  }
}

// 初始化
onMounted(() => {
  const savedViewMode = localStorage.getItem('bookmark-view-mode') as 'grid' | 'list'
  if (savedViewMode) {
    viewMode.value = savedViewMode
  }
  fetchBookmarks()
})

// 监听props变化
watch([() => props.folderId, () => props.tagId, () => props.starred, () => props.search], () => {
  currentPage.value = 1
  fetchBookmarks()
}, { deep: true })
</script>

<style scoped>
.bookmark-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.loading-container {
  padding: 20px;
}

.grid-view {
  flex: 1;
  overflow-y: auto;
}

.bookmark-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  padding: 0 4px;
}

.bookmark-grid-item {
  position: relative;
}

.bookmark-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 2px;
}

.list-view {
  flex: 1;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.bookmark-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bookmark-favicon img {
  width: 20px;
  height: 20px;
  border-radius: 4px;
}

.bookmark-content {
  flex: 1;
  min-width: 0;
}

.bookmark-title {
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-url {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.bookmark-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-item {
  font-size: 12px;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.table-actions .is-starred {
  color: #f56c6c;
}

@media (max-width: 768px) {
  .bookmark-grid {
    grid-template-columns: 1fr;
  }
  
  .list-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
}
</style>
