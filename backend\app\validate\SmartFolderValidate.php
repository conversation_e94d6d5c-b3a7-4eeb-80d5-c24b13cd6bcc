<?php

namespace app\validate;

use think\Validate;

/**
 * 智能文件夹验证器
 */
class SmartFolderValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'name' => 'require|length:1,100',
        'icon' => 'length:0,50',
        'conditions' => 'require|array',
        'conditions.*' => 'array',
        'conditions.*.field' => 'require|in:title,url,description,folder_id,is_star,tags,created_at',
        'conditions.*.operator' => 'require|in:contains,equals,after,before',
        'conditions.*.value' => 'require',
    ];

    /**
     * 验证消息
     */
    protected $message = [
        'name.require' => '智能文件夹名称不能为空',
        'name.length' => '智能文件夹名称长度必须在1-100个字符之间',
        'icon.length' => '图标长度不能超过50个字符',
        'conditions.require' => '搜索条件不能为空',
        'conditions.array' => '搜索条件必须是数组格式',
        'conditions.*.array' => '每个搜索条件必须是数组格式',
        'conditions.*.field.require' => '搜索字段不能为空',
        'conditions.*.field.in' => '搜索字段值无效',
        'conditions.*.operator.require' => '操作符不能为空',
        'conditions.*.operator.in' => '操作符值无效',
        'conditions.*.value.require' => '搜索值不能为空',
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'create' => ['name', 'icon', 'conditions', 'conditions.*', 'conditions.*.field', 'conditions.*.operator', 'conditions.*.value'],
        'update' => ['name', 'icon', 'conditions', 'conditions.*', 'conditions.*.field', 'conditions.*.operator', 'conditions.*.value'],
    ];

    /**
     * 自定义验证条件数量
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkConditionsCount($value, $rule, $data)
    {
        if (!is_array($value)) {
            return '搜索条件必须是数组格式';
        }

        if (count($value) > 10) {
            return '搜索条件最多不能超过10个';
        }

        if (count($value) < 1) {
            return '至少需要设置一个搜索条件';
        }

        return true;
    }

    /**
     * 自定义验证条件格式
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkConditionFormat($value, $rule, $data)
    {
        if (!is_array($value)) {
            return '搜索条件必须是数组格式';
        }

        $allowedFields = ['title', 'url', 'description', 'folder_id', 'is_star', 'tags', 'created_at'];
        $allowedOperators = ['contains', 'equals', 'after', 'before'];

        foreach ($value as $condition) {
            if (!is_array($condition)) {
                return '每个搜索条件必须是数组格式';
            }

            if (!isset($condition['field']) || !isset($condition['operator']) || !isset($condition['value'])) {
                return '搜索条件必须包含field、operator、value字段';
            }

            if (!in_array($condition['field'], $allowedFields)) {
                return '搜索字段值无效：' . $condition['field'];
            }

            if (!in_array($condition['operator'], $allowedOperators)) {
                return '操作符值无效：' . $condition['operator'];
            }

            // 验证特定字段的值格式
            if ($condition['field'] === 'folder_id' && !is_numeric($condition['value'])) {
                return '文件夹ID必须是数字';
            }

            if ($condition['field'] === 'is_star' && !in_array($condition['value'], [0, 1, '0', '1', true, false])) {
                return '星标值必须是布尔类型';
            }

            if (in_array($condition['field'], ['created_at']) && in_array($condition['operator'], ['after', 'before'])) {
                if (!strtotime($condition['value'])) {
                    return '时间格式无效：' . $condition['value'];
                }
            }
        }

        return true;
    }
}
