# 📚 书签管理系统 API 测试指南

## 🚀 快速开始

### 1. 启动后端服务器

在项目根目录下执行以下命令启动后端服务器：

```bash
cd backend
php think run -H 0.0.0.0 -p 8000
```

服务器启动后会显示：
```
ThinkPHP Development server is started On <http://0.0.0.0:8000/>
```

### 2. 打开API测试页面

在浏览器中打开 `test_api.html` 文件，或者通过以下URL访问：
- 本地文件：`file:///path/to/your/project/test_api.html`
- 如果有Web服务器：`http://localhost/your-project/test_api.html`

## 🔧 功能说明

### 系统配置
- **API基础URL配置**：默认为 `http://localhost:8000`，可以根据实际情况修改
- **服务器状态检测**：自动检测后端服务器是否正常运行
- **Token状态显示**：实时显示当前登录状态

### 用户注册
- 支持自动生成测试用户数据
- 实时表单验证
- 注册成功后自动保存Token

### 用户登录
- 支持使用测试账号快速登录
- 登录成功后自动保存Token
- Token会持久化保存在浏览器本地存储中

### AI解析URL
- 需要先登录获取Token
- 支持解析任意URL的标题、摘要和标签
- 美化显示解析结果

## 🎯 测试流程

### 标准测试流程
1. **检查服务器状态** - 确保后端服务正常运行
2. **注册新用户** - 点击"生成测试用户"然后"注册用户"
3. **登录系统** - 使用注册的账号登录，或使用"使用测试账号"
4. **测试AI解析** - 输入URL进行AI解析测试

### 快捷键支持
- `Ctrl + Enter`：在相应输入框中快速执行操作
  - URL输入框：执行AI解析
  - 登录表单：执行登录
  - 注册表单：执行注册

## 🐛 常见问题

### 1. 服务器连接失败
**现象**：状态栏显示"服务器状态: 离线"

**解决方案**：
- 检查后端服务器是否启动
- 确认API基础URL配置是否正确
- 检查防火墙设置
- 确认端口8000是否被占用

### 2. 注册失败：用户名已存在
**现象**：注册时提示用户名已存在

**解决方案**：
- 点击"生成测试用户"按钮生成新的用户名
- 或手动修改用户名

### 3. 登录失败：账号不存在
**现象**：登录时提示账号不存在

**解决方案**：
- 先进行用户注册
- 确认用户名和密码输入正确

### 4. AI解析失败：Token无效
**现象**：AI解析时提示Token无效或过期

**解决方案**：
- 重新登录获取新的Token
- 检查Token是否正确保存

### 5. CORS跨域问题
**现象**：浏览器控制台显示CORS错误

**解决方案**：
- 确保后端已配置CORS中间件
- 检查请求头设置是否正确

## 📊 API接口说明

### 认证接口
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录

### 系统接口
- `GET /api/v1/system/status` - 系统状态检查

### 书签接口
- `POST /api/v1/bookmarks/parse` - AI解析URL

## 🔍 调试技巧

### 1. 查看网络请求
- 打开浏览器开发者工具（F12）
- 切换到"Network"标签
- 执行API请求，查看请求和响应详情

### 2. 查看控制台日志
- 在开发者工具的"Console"标签中查看错误信息
- 所有Token操作都会在控制台输出日志

### 3. 检查本地存储
- 在开发者工具的"Application"标签中
- 查看"Local Storage"中的配置信息

## 📝 测试数据

### 默认测试账号
- 用户名：`test`
- 密码：`123456`

### 测试URL示例
- `https://www.faxianai.com/` - AI相关网站
- `https://github.com` - 技术网站
- `https://www.baidu.com` - 搜索引擎

## 🚀 高级功能

### 自动保存配置
- API基础URL配置会自动保存
- Token会持久化保存
- 最后注册的用户信息会被记住

### 表单验证
- 实时验证用户输入
- 友好的错误提示
- 防止无效数据提交

### 响应美化
- JSON响应格式化显示
- 成功/失败状态区分
- AI解析结果特殊展示

---

## 📞 技术支持

如果在使用过程中遇到问题，请：
1. 查看浏览器控制台错误信息
2. 检查后端服务器日志
3. 确认API接口是否正常工作
4. 参考本文档的常见问题部分

---

**最后更新时间**：2025-07-27
**版本**：v1.0.0
