<template>
  <div class="bookmark-card" @click="visitBookmark">
    <div class="bookmark-header">
      <div class="bookmark-favicon">
        <img 
          :src="faviconUrl" 
          :alt="bookmark.title"
          @error="handleFaviconError"
          v-if="showFavicon"
        />
        <el-icon v-else><Link /></el-icon>
      </div>
      <div class="bookmark-actions">
        <el-button 
          :icon="bookmark.is_star ? StarFilled : Star"
          @click.stop="toggleStar"
          text
          :class="{ 'is-starred': bookmark.is_star }"
        />
        <el-dropdown @command="handleAction" trigger="click" @click.stop>
          <el-button :icon="MoreFilled" text />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="edit">
                <el-icon><Edit /></el-icon>
                编辑
              </el-dropdown-item>
              <el-dropdown-item command="move">
                <el-icon><FolderOpened /></el-icon>
                移动到文件夹
              </el-dropdown-item>
              <el-dropdown-item command="copy">
                <el-icon><CopyDocument /></el-icon>
                复制链接
              </el-dropdown-item>
              <el-dropdown-item command="delete" divided>
                <el-icon><Delete /></el-icon>
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <div class="bookmark-content">
      <h3 class="bookmark-title" :title="bookmark.title">
        {{ bookmark.title }}
      </h3>
      <p class="bookmark-url" :title="bookmark.url">
        {{ formatUrl.getDomain(bookmark.url) }}
      </p>
      <p class="bookmark-description" v-if="bookmark.description">
        {{ bookmark.description }}
      </p>
    </div>
    
    <div class="bookmark-footer">
      <div class="bookmark-tags">
        <el-tag
          v-for="tag in bookmark.tags"
          :key="tag.id"
          size="small"
          class="bookmark-tag"
        >
          {{ tag.name }}
        </el-tag>
      </div>
      <div class="bookmark-meta">
        <span class="visit-count">
          <el-icon><View /></el-icon>
          {{ bookmark.visit_count }}
        </span>
        <span class="created-time">
          {{ formatDate.friendly(bookmark.created_at) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  Link, 
  Star, 
  StarFilled, 
  MoreFilled, 
  Edit, 
  FolderOpened, 
  CopyDocument, 
  Delete,
  View
} from '@element-plus/icons-vue'
import type { Bookmark } from '@/types/api'
import { formatUrl, formatDate } from '@/utils/format'
import { useBookmarkStore } from '@/stores/bookmark'

interface Props {
  bookmark: Bookmark
}

interface Emits {
  (e: 'edit', bookmark: Bookmark): void
  (e: 'move', bookmark: Bookmark): void
  (e: 'delete', bookmark: Bookmark): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const bookmarkStore = useBookmarkStore()

const showFavicon = ref(true)
const faviconUrl = computed(() => formatUrl.getFavicon(props.bookmark.url))

const handleFaviconError = () => {
  showFavicon.value = false
}

const visitBookmark = async () => {
  try {
    await bookmarkStore.visitBookmark(props.bookmark.id)
    window.open(props.bookmark.url, '_blank')
  } catch (error) {
    console.error('访问书签失败:', error)
  }
}

const toggleStar = async () => {
  try {
    await bookmarkStore.toggleStar(props.bookmark.id)
  } catch (error) {
    console.error('切换收藏状态失败:', error)
  }
}

const handleAction = (command: string) => {
  switch (command) {
    case 'edit':
      emit('edit', props.bookmark)
      break
    case 'move':
      emit('move', props.bookmark)
      break
    case 'copy':
      navigator.clipboard.writeText(props.bookmark.url)
      ElMessage.success('链接已复制到剪贴板')
      break
    case 'delete':
      emit('delete', props.bookmark)
      break
  }
}
</script>

<style scoped>
.bookmark-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.bookmark-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
}

.bookmark-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.bookmark-favicon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bookmark-favicon img {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.bookmark-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

.bookmark-card:hover .bookmark-actions {
  opacity: 1;
}

.bookmark-actions .is-starred {
  color: #f56c6c;
}

.bookmark-content {
  flex: 1;
  margin-bottom: 12px;
}

.bookmark-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.bookmark-url {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #909399;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-description {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.bookmark-footer {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.bookmark-tags {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.bookmark-tag {
  font-size: 12px;
}

.bookmark-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #c0c4cc;
}

.visit-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

.created-time {
  flex-shrink: 0;
}
</style>
