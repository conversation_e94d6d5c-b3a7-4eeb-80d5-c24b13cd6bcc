<?php

namespace app\validate;

use think\Validate;

/**
 * 导入验证器
 */
class ImportValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'folder_id' => 'integer|egt:1',
        'merge_duplicates' => 'boolean',
        'create_folders' => 'boolean',
        'file_type' => 'in:html,json',
    ];

    /**
     * 验证消息
     */
    protected $message = [
        'folder_id.integer' => '文件夹ID必须是整数',
        'folder_id.egt' => '文件夹ID必须大于0',
        'merge_duplicates.boolean' => '合并重复书签参数必须是布尔值',
        'create_folders.boolean' => '创建文件夹参数必须是布尔值',
        'file_type.in' => '文件类型不支持，仅支持HTML和JSON格式',
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'import' => ['folder_id', 'merge_duplicates', 'create_folders'],
    ];

    /**
     * 自定义验证文件大小
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkFileSize($value, $rule, $data)
    {
        if (!$value) {
            return '请选择要导入的文件';
        }

        // 检查文件大小（最大10MB）
        if ($value->getSize() > 10 * 1024 * 1024) {
            return '文件大小不能超过10MB';
        }

        return true;
    }

    /**
     * 自定义验证文件类型
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkFileType($value, $rule, $data)
    {
        if (!$value) {
            return '请选择要导入的文件';
        }

        $extension = strtolower($value->getOriginalExtension());
        $allowedTypes = ['html', 'htm', 'json'];

        if (!in_array($extension, $allowedTypes)) {
            return '文件格式不支持，仅支持HTML和JSON格式';
        }

        return true;
    }

    /**
     * 自定义验证文件内容
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkFileContent($value, $rule, $data)
    {
        if (!$value) {
            return '请选择要导入的文件';
        }

        $content = file_get_contents($value->getPathname());
        if (empty($content)) {
            return '文件内容为空';
        }

        $extension = strtolower($value->getOriginalExtension());
        
        if (in_array($extension, ['html', 'htm'])) {
            // 检查是否包含书签标记
            if (strpos($content, '<DT><A HREF=') === false && strpos($content, '<dt><a href=') === false) {
                return '不是有效的书签HTML文件';
            }
        } elseif ($extension === 'json') {
            // 检查JSON格式
            $json = json_decode($content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return '不是有效的JSON文件';
            }
        }

        return true;
    }
}
