/**
 * 本地存储工具类
 */

// 存储键名常量
export const STORAGE_KEYS = {
  TOKEN: 'token',
  REFRESH_TOKEN: 'refreshToken',
  USER: 'user',
  THEME: 'theme',
  LANGUAGE: 'language',
  SIDEBAR_COLLAPSED: 'sidebarCollapsed',
  SEARCH_HISTORY: 'searchHistory',
  USER_PREFERENCES: 'userPreferences'
} as const

/**
 * localStorage 封装
 */
export const localStorage = {
  // 设置数据
  set<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value)
      window.localStorage.setItem(key, serializedValue)
    } catch (error) {
      console.error('localStorage.set error:', error)
    }
  },

  // 获取数据
  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = window.localStorage.getItem(key)
      if (item === null) {
        return defaultValue ?? null
      }
      return JSON.parse(item)
    } catch (error) {
      console.error('localStorage.get error:', error)
      return defaultValue ?? null
    }
  },

  // 移除数据
  remove(key: string): void {
    try {
      window.localStorage.removeItem(key)
    } catch (error) {
      console.error('localStorage.remove error:', error)
    }
  },

  // 清空所有数据
  clear(): void {
    try {
      window.localStorage.clear()
    } catch (error) {
      console.error('localStorage.clear error:', error)
    }
  },

  // 检查是否存在
  has(key: string): boolean {
    return window.localStorage.getItem(key) !== null
  },

  // 获取所有键名
  keys(): string[] {
    try {
      return Object.keys(window.localStorage)
    } catch (error) {
      console.error('localStorage.keys error:', error)
      return []
    }
  }
}

/**
 * sessionStorage 封装
 */
export const sessionStorage = {
  // 设置数据
  set<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value)
      window.sessionStorage.setItem(key, serializedValue)
    } catch (error) {
      console.error('sessionStorage.set error:', error)
    }
  },

  // 获取数据
  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = window.sessionStorage.getItem(key)
      if (item === null) {
        return defaultValue ?? null
      }
      return JSON.parse(item)
    } catch (error) {
      console.error('sessionStorage.get error:', error)
      return defaultValue ?? null
    }
  },

  // 移除数据
  remove(key: string): void {
    try {
      window.sessionStorage.removeItem(key)
    } catch (error) {
      console.error('sessionStorage.remove error:', error)
    }
  },

  // 清空所有数据
  clear(): void {
    try {
      window.sessionStorage.clear()
    } catch (error) {
      console.error('sessionStorage.clear error:', error)
    }
  },

  // 检查是否存在
  has(key: string): boolean {
    return window.sessionStorage.getItem(key) !== null
  },

  // 获取所有键名
  keys(): string[] {
    try {
      return Object.keys(window.sessionStorage)
    } catch (error) {
      console.error('sessionStorage.keys error:', error)
      return []
    }
  }
}

/**
 * 带过期时间的存储
 */
export const expiredStorage = {
  // 设置带过期时间的数据
  set<T>(key: string, value: T, expireTime: number): void {
    const data = {
      value,
      expireTime: Date.now() + expireTime
    }
    localStorage.set(key, data)
  },

  // 获取数据（自动检查过期）
  get<T>(key: string, defaultValue?: T): T | null {
    const data = localStorage.get<{ value: T; expireTime: number }>(key)
    
    if (!data) {
      return defaultValue ?? null
    }

    // 检查是否过期
    if (Date.now() > data.expireTime) {
      localStorage.remove(key)
      return defaultValue ?? null
    }

    return data.value
  },

  // 移除数据
  remove(key: string): void {
    localStorage.remove(key)
  }
}

/**
 * 缓存管理器
 */
export class CacheManager {
  private prefix: string
  private maxSize: number
  private storage: typeof localStorage | typeof sessionStorage

  constructor(
    prefix = 'cache_',
    maxSize = 100,
    storage: typeof localStorage | typeof sessionStorage = localStorage
  ) {
    this.prefix = prefix
    this.maxSize = maxSize
    this.storage = storage
  }

  // 生成缓存键
  private getKey(key: string): string {
    return this.prefix + key
  }

  // 设置缓存
  set<T>(key: string, value: T, ttl?: number): void {
    const cacheKey = this.getKey(key)
    const data = {
      value,
      timestamp: Date.now(),
      ttl
    }
    
    this.storage.set(cacheKey, data)
    this.cleanup()
  }

  // 获取缓存
  get<T>(key: string, defaultValue?: T): T | null {
    const cacheKey = this.getKey(key)
    const data = this.storage.get<{
      value: T
      timestamp: number
      ttl?: number
    }>(cacheKey)

    if (!data) {
      return defaultValue ?? null
    }

    // 检查TTL
    if (data.ttl && Date.now() - data.timestamp > data.ttl) {
      this.storage.remove(cacheKey)
      return defaultValue ?? null
    }

    return data.value
  }

  // 移除缓存
  remove(key: string): void {
    const cacheKey = this.getKey(key)
    this.storage.remove(cacheKey)
  }

  // 清空所有缓存
  clear(): void {
    const keys = this.storage.keys()
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        this.storage.remove(key)
      }
    })
  }

  // 清理过期缓存
  private cleanup(): void {
    const keys = this.storage.keys().filter(key => key.startsWith(this.prefix))
    
    if (keys.length <= this.maxSize) {
      return
    }

    // 按时间戳排序，移除最旧的缓存
    const cacheItems = keys
      .map(key => ({
        key,
        data: this.storage.get<{ timestamp: number }>(key)
      }))
      .filter(item => item.data)
      .sort((a, b) => a.data!.timestamp - b.data!.timestamp)

    const removeCount = keys.length - this.maxSize
    for (let i = 0; i < removeCount; i++) {
      this.storage.remove(cacheItems[i].key)
    }
  }
}

// 默认缓存实例
export const cache = new CacheManager()
export const sessionCache = new CacheManager('session_cache_', 50, sessionStorage)
