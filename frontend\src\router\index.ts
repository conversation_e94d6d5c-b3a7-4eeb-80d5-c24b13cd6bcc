import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw, NavigationGuardNext, RouteLocationNormalized } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: { 
      requiresAuth: false,
      title: '登录'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/RegisterView.vue'),
    meta: { 
      requiresAuth: false,
      title: '注册'
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/components/layout/AppLayout.vue'),
    meta: { requiresAuth: true },
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/DashboardView.vue'),
        meta: { 
          title: '仪表板',
          icon: 'House'
        }
      },
      {
        path: 'bookmarks',
        name: 'Bookmarks',
        component: () => import('@/views/bookmark/BookmarkListView.vue'),
        meta: { 
          title: '我的书签',
          icon: 'Collection'
        }
      },
      {
        path: 'folders',
        name: 'Folders',
        component: () => import('@/views/folder/FolderManageView.vue'),
        meta: { 
          title: '文件夹管理',
          icon: 'Folder'
        }
      },
      {
        path: 'tags',
        name: 'Tags',
        component: () => import('@/views/tag/TagManageView.vue'),
        meta: { 
          title: '标签管理',
          icon: 'PriceTag'
        }
      },
      {
        path: 'search',
        name: 'Search',
        component: () => import('@/views/search/SearchView.vue'),
        meta: { 
          title: '搜索',
          icon: 'Search'
        }
      },
      {
        path: 'stats',
        name: 'Stats',
        component: () => import('@/views/stats/StatsView.vue'),
        meta: { 
          title: '统计分析',
          icon: 'DataAnalysis'
        }
      },
      {
        path: 'trash',
        name: 'Trash',
        component: () => import('@/views/trash/TrashView.vue'),
        meta: {
          title: '回收站',
          icon: 'Delete'
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/user/ProfileView.vue'),
        meta: {
          title: '个人信息',
          icon: 'User'
        }
      },
      {
        path: 'bookmarks/starred',
        name: 'StarredBookmarks',
        component: () => import('@/views/bookmark/StarredBookmarksView.vue'),
        meta: {
          title: '收藏书签',
          icon: 'Star'
        }
      },
      {
        path: 'bookmarks/recent',
        name: 'RecentBookmarks',
        component: () => import('@/views/bookmark/RecentBookmarksView.vue'),
        meta: {
          title: '最近添加',
          icon: 'Clock'
        }
      },
      {
        path: 'smart-folders',
        name: 'SmartFolders',
        component: () => import('@/views/folder/SmartFoldersView.vue'),
        meta: {
          title: '智能文件夹',
          icon: 'MagicStick'
        }
      },
      {
        path: 'import-export',
        name: 'ImportExport',
        component: () => import('@/views/tools/ImportExportView.vue'),
        meta: {
          title: '导入导出',
          icon: 'Download'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/NotFoundView.vue'),
    meta: { 
      requiresAuth: false,
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to: RouteLocationNormalized, from: RouteLocationNormalized, savedPosition: any) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 书签管理系统`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('token')
    if (!token) {
      next('/login')
      return
    }
  }

  // 如果已登录用户访问登录页，重定向到仪表板
  if (to.path === '/login' || to.path === '/register') {
    const token = localStorage.getItem('token')
    if (token) {
      next('/dashboard')
      return
    }
  }

  next()
})

export default router
