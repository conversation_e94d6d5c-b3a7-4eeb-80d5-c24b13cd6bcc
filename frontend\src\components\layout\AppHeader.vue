<template>
  <div class="app-header">
    <div class="header-left">
      <el-button
        class="sidebar-toggle"
        :icon="Menu"
        @click="toggleSidebar"
        text
      />
      <h1 class="app-title">📚 书签管理系统</h1>
    </div>

    <div class="header-center">
      <el-input
        v-model="searchQuery"
        placeholder="搜索书签..."
        class="search-input"
        :prefix-icon="Search"
        @keyup.enter="handleSearch"
        clearable
      />
    </div>

    <div class="header-right">
      <!-- 快速添加按钮 -->
      <el-button
        type="primary"
        :icon="Plus"
        @click="showAddBookmark"
        class="add-button"
      >
        添加书签
      </el-button>

      <!-- 通知中心 -->
      <el-badge :value="notificationCount" :hidden="notificationCount === 0">
        <el-button
          :icon="Bell"
          @click="showNotifications"
          text
          class="notification-button"
        />
      </el-badge>

      <!-- 主题切换 -->
      <el-button
        :icon="isDark ? Sunny : Moon"
        @click="toggleTheme"
        text
        class="theme-button"
      />

      <!-- 用户菜单 -->
      <el-dropdown @command="handleCommand" trigger="click">
        <span class="user-info">
          <el-avatar :size="32" :src="userAvatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">{{ username }}</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人信息
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item command="help">
              <el-icon><QuestionFilled /></el-icon>
              帮助中心
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArrowDown,
  Menu,
  Search,
  Plus,
  Bell,
  Sunny,
  Moon,
  User,
  Setting,
  QuestionFilled,
  SwitchButton
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'

interface Emits {
  (e: 'toggle-sidebar'): void
}

const emit = defineEmits<Emits>()

const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

const searchQuery = ref('')
const notificationCount = ref(3)

const username = computed(() => authStore.user?.username || '用户')
const userAvatar = computed(() => authStore.user?.avatar || '')
const isDark = computed(() => appStore.theme === 'dark')

const toggleSidebar = () => {
  emit('toggle-sidebar')
}

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({
      path: '/search',
      query: { q: searchQuery.value.trim() }
    })
  }
}

const showAddBookmark = () => {
  // 显示添加书签对话框
  console.log('显示添加书签对话框')
}

const showNotifications = () => {
  // 显示通知中心
  console.log('显示通知中心')
}

const toggleTheme = () => {
  const newTheme = isDark.value ? 'light' : 'dark'
  appStore.setTheme(newTheme)
}

const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'help':
      router.push('/help')
      break
    case 'logout':
      try {
        await authStore.logout()
        ElMessage.success('已安全退出')
        router.push('/login')
      } catch (error) {
        console.error('退出登录失败:', error)
      }
      break
  }
}
</script>

<style scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.sidebar-toggle {
  font-size: 18px;
  color: #606266;
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
}

.header-center {
  flex: 1;
  max-width: 400px;
  margin: 0 20px;
}

.search-input {
  width: 100%;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.add-button {
  font-size: 14px;
}

.notification-button,
.theme-button {
  font-size: 18px;
  color: #606266;
  width: 40px;
  height: 40px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #606266;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  font-size: 12px;
  color: #c0c4cc;
  transition: transform 0.3s;
}

.user-info:hover .dropdown-icon {
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    padding: 0 12px;
  }

  .header-center {
    display: none;
  }

  .app-title {
    font-size: 16px;
  }

  .username {
    display: none;
  }

  .add-button span {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-left {
    gap: 8px;
  }

  .header-right {
    gap: 8px;
  }

  .add-button {
    padding: 8px;
  }
}
</style>
