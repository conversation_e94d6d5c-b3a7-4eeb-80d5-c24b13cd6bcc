<?php
// +----------------------------------------------------------------------
// | 书签管理系统 API 路由配置 - 简化版本
// +----------------------------------------------------------------------
use think\facade\Route;

// 默认首页（保留ThinkPHP欢迎页）
Route::get('/', function () {
    return 'hello,ThinkPHP8!';
});

// API路由组
Route::group('api/v1', function () {

    // 认证相关路由（无需认证）
    Route::group('auth', function () {
        Route::post('register', 'app\\controller\\api\\v1\\Auth@register');        // 用户注册
        Route::post('login', 'app\\controller\\api\\v1\\Auth@login');              // 用户登录
        Route::post('refresh', 'app\\controller\\api\\v1\\Auth@refresh');          // 刷新令牌
        Route::post('logout', 'app\\controller\\api\\v1\\Auth@logout')->middleware('auth'); // 用户登出
    });

    // 文件夹管理路由（需要认证）
    Route::group('folders', function () {
        Route::get('', 'app\\controller\\api\\v1\\Folder@index');                  // 获取文件夹列表
        Route::post('', 'app\\controller\\api\\v1\\Folder@create');                // 创建文件夹
        Route::get(':id', 'app\\controller\\api\\v1\\Folder@show');                // 获取文件夹详情
        Route::put(':id', 'app\\controller\\api\\v1\\Folder@update');              // 更新文件夹
        Route::delete(':id', 'app\\controller\\api\\v1\\Folder@delete');           // 删除文件夹
        Route::patch(':id/move', 'app\\controller\\api\\v1\\Folder@move');         // 移动文件夹
        Route::patch('sort', 'app\\controller\\api\\v1\\Folder@sort');             // 批量排序
    })->middleware('auth');

    // 书签管理路由（需要认证）
    Route::group('bookmarks', function () {
        Route::get('', 'app\\controller\\api\\v1\\Bookmark@index');                // 获取书签列表
        Route::post('', 'app\\controller\\api\\v1\\Bookmark@create');              // 创建书签
        Route::post('batch', 'app\\controller\\api\\v1\\Bookmark@batch');          // 批量操作
        Route::post('parse', 'app\\controller\\api\\v1\\Bookmark@parse');          // AI解析URL
        Route::post('batch-check', 'app\\controller\\api\\v1\\Bookmark@batchCheck'); // 批量检测死链
        Route::get('dead-link-stats', 'app\\controller\\api\\v1\\Bookmark@deadLinkStats'); // 死链统计
        Route::get(':id', 'app\\controller\\api\\v1\\Bookmark@show');              // 获取书签详情
        Route::put(':id', 'app\\controller\\api\\v1\\Bookmark@update');            // 更新书签
        Route::delete(':id', 'app\\controller\\api\\v1\\Bookmark@delete');         // 删除书签
        Route::post(':id/visit', 'app\\controller\\api\\v1\\Bookmark@visit');      // 访问统计
        Route::post(':id/parse', 'app\\controller\\api\\v1\\Bookmark@parse');      // AI解析书签
        Route::post(':id/check', 'app\\controller\\api\\v1\\Bookmark@check');      // 检查死链
    })->middleware('auth');

    // URL信息获取路由（需要认证）
    Route::get('url-info', 'app\\controller\\api\\v1\\Bookmark@getUrlInfo')->middleware('auth');

    // 系统配置路由
    Route::group('system', function () {
        Route::get('config', 'app\\controller\\api\\v1\\System@config');           // 获取系统配置（公开）
        Route::get('status', 'app\\controller\\api\\v1\\System@status');           // 获取系统状态（公开）
    });

});

// 兼容旧版本路由
Route::get('think', function () {
    return 'hello,ThinkPHP8!';
});

Route::get('hello/:name', 'app\\controller\\Index@hello');
