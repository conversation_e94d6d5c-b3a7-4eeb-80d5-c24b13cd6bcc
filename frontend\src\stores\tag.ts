import { defineStore } from 'pinia'
import type { Tag, CreateTagRequest } from '@/types/api'
import { http } from '@/utils/request'

export const useTagStore = defineStore('tag', () => {
  // 状态
  const tags = ref<Tag[]>([])
  const currentTag = ref<Tag | null>(null)
  const loading = ref(false)
  const total = ref(0)

  // 计算属性
  const popularTags = computed(() => 
    tags.value
      .sort((a, b) => (b.bookmark_count || 0) - (a.bookmark_count || 0))
      .slice(0, 20)
  )

  const tagMap = computed(() => {
    const map = new Map<number, Tag>()
    tags.value.forEach(tag => map.set(tag.id, tag))
    return map
  })

  // 获取标签列表
  const fetchTags = async (params?: any) => {
    try {
      loading.value = true
      const response = await http.get('/tags', { params })
      
      tags.value = response.data.list || response.data
      total.value = response.data.total || response.data.length
      
      return response.data
    } catch (error) {
      console.error('获取标签列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取标签详情
  const fetchTag = async (id: number) => {
    try {
      const response = await http.get(`/tags/${id}`)
      currentTag.value = response.data
      return response.data
    } catch (error) {
      console.error('获取标签详情失败:', error)
      throw error
    }
  }

  // 创建标签
  const createTag = async (data: CreateTagRequest) => {
    try {
      const response = await http.post('/tags', data)
      const newTag = response.data
      
      // 添加到列表
      tags.value.unshift(newTag)
      total.value += 1
      
      return newTag
    } catch (error) {
      console.error('创建标签失败:', error)
      throw error
    }
  }

  // 更新标签
  const updateTag = async (id: number, data: Partial<CreateTagRequest>) => {
    try {
      const response = await http.put(`/tags/${id}`, data)
      const updatedTag = response.data
      
      // 更新列表中的标签
      const index = tags.value.findIndex(tag => tag.id === id)
      if (index !== -1) {
        tags.value[index] = updatedTag
      }
      
      // 更新当前标签
      if (currentTag.value?.id === id) {
        currentTag.value = updatedTag
      }
      
      return updatedTag
    } catch (error) {
      console.error('更新标签失败:', error)
      throw error
    }
  }

  // 删除标签
  const deleteTag = async (id: number) => {
    try {
      await http.delete(`/tags/${id}`)
      
      // 从列表中移除
      tags.value = tags.value.filter(tag => tag.id !== id)
      total.value -= 1
      
      // 清除当前标签
      if (currentTag.value?.id === id) {
        currentTag.value = null
      }
    } catch (error) {
      console.error('删除标签失败:', error)
      throw error
    }
  }

  // 合并标签
  const mergeTags = async (sourceTagIds: number[], targetTagId: number) => {
    try {
      await http.post('/tags/merge', {
        source_tag_ids: sourceTagIds,
        target_tag_id: targetTagId
      })
      
      // 重新获取标签列表
      await fetchTags()
    } catch (error) {
      console.error('合并标签失败:', error)
      throw error
    }
  }

  // 搜索标签
  const searchTags = async (query: string) => {
    try {
      const response = await http.get('/tags/search', {
        params: { q: query }
      })
      return response.data
    } catch (error) {
      console.error('搜索标签失败:', error)
      throw error
    }
  }

  // 获取标签统计
  const getTagStats = async () => {
    try {
      const response = await http.get('/tags/stats')
      return response.data
    } catch (error) {
      console.error('获取标签统计失败:', error)
      throw error
    }
  }

  // 根据ID获取标签
  const getTagById = (id: number): Tag | undefined => {
    return tagMap.value.get(id)
  }

  // 根据名称获取标签
  const getTagByName = (name: string): Tag | undefined => {
    return tags.value.find(tag => tag.name === name)
  }

  // 清空状态
  const clearTags = () => {
    tags.value = []
    currentTag.value = null
    total.value = 0
  }

  return {
    // 状态
    tags: readonly(tags),
    currentTag: readonly(currentTag),
    loading: readonly(loading),
    total: readonly(total),
    
    // 计算属性
    popularTags,
    tagMap,
    
    // 方法
    fetchTags,
    fetchTag,
    createTag,
    updateTag,
    deleteTag,
    mergeTags,
    searchTags,
    getTagStats,
    getTagById,
    getTagByName,
    clearTags
  }
})
