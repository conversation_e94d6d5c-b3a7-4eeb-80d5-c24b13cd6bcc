<?php

namespace app\controller\api\v1;

use app\BaseController;
use app\service\FolderService;
use app\validate\FolderValidate;
use app\exception\BusinessException;
use think\Response;

/**
 * 文件夹管理控制器
 *
 * 提供文件夹的CRUD操作、移动、排序等功能，支持层级结构管理
 */
class Folder extends BaseController
{
    /**
     * 文件夹服务
     * @var FolderService
     */
    protected $folderService;

    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->folderService = new FolderService();
    }

    /**
     * 获取文件夹列表
     *
     * 支持树形结构和列表形式，可选择是否包含统计信息
     *
     * @route GET /api/v1/folders
     * @middleware auth
     * @param int $parent_id 父文件夹ID，可选，null表示获取根目录
     * @param string $type 返回类型：tree|list，默认tree
     * @param bool $with_stats 是否包含统计信息，默认false
     * @return Response JSON响应，包含文件夹列表或树形结构
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/folders?parent_id=1&type=tree&with_stats=true
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取文件夹列表成功",
     *   "data": [
     *     {
     *       "id": 1,
     *       "name": "工作相关",
     *       "parent_id": null,
     *       "sort_order": 1,
     *       "bookmark_count": 15,
     *       "children": [...]
     *     }
     *   ]
     * }
     */
    public function index(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $parentIdParam = $this->request->get('parent_id', null);
            // 处理parent_id参数：null、"null"、空字符串都视为null，其他转为int
            if ($parentIdParam === null || $parentIdParam === '' || $parentIdParam === 'null') {
                $parentId = null;
            } else {
                $parentId = (int)$parentIdParam;
            }
            $type = $this->request->get('type', 'tree'); // tree or list
            $withStats = (bool)$this->request->get('with_stats', false);

            if ($type === 'tree') {
                $data = $this->folderService->getFolderTree($userId, $parentId, $withStats);
            } else {
                $data = $this->folderService->getFolderList($userId, $parentId, $withStats);
            }

            return $this->success($data, '获取文件夹列表成功');
        } catch (\Exception $e) {
            return $this->error('获取文件夹列表失败：' . $e->getMessage());
        }
    }

    /**
     * 创建文件夹
     *
     * 创建新的文件夹，支持设置父文件夹、图标、颜色等属性
     *
     * @route POST /api/v1/folders
     * @middleware auth
     * @param string $name 文件夹名称，必填，1-100个字符
     * @param int $parent_id 父文件夹ID，可选，null表示创建在根目录
     * @param string $icon 图标，可选，如：folder、star、heart等
     * @param string $color 颜色，可选，十六进制颜色值，如：#1890ff
     * @param string $description 描述，可选，最大500个字符
     * @return Response JSON响应，包含创建的文件夹信息
     * @throws BusinessException 参数验证失败或业务逻辑错误
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/folders
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "name": "工作相关",
     *   "parent_id": null,
     *   "icon": "folder",
     *   "color": "#1890ff",
     *   "description": "存放工作相关的书签"
     * }
     * Response: {
     *   "code": 201,
     *   "message": "创建文件夹成功",
     *   "data": {
     *     "id": 1,
     *     "name": "工作相关",
     *     "parent_id": null,
     *     "icon": "folder",
     *     "color": "#1890ff",
     *     "sort_order": 1,
     *     "bookmark_count": 0,
     *     "created_at": "2024-06-30 12:00:00"
     *   }
     * }
     */
    public function create(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $data = $this->request->post();
            $this->validate($data, FolderValidate::class . '.create');

            $folder = $this->folderService->createFolder($userId, $data);
            return $this->created($folder->toApiArray(), '创建文件夹成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('创建文件夹失败：' . $e->getMessage());
        }
    }

    /**
     * 获取文件夹详情
     *
     * 根据文件夹ID获取详细信息，包括统计数据和子文件夹信息
     *
     * @route GET /api/v1/folders/{id}
     * @middleware auth
     * @param int $id 文件夹ID，路径参数
     * @param bool $with_stats 是否包含统计信息，默认true
     * @param bool $with_children 是否包含子文件夹，默认true
     * @return Response JSON响应，包含文件夹详细信息
     * @throws BusinessException 文件夹不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/folders/1?with_stats=true&with_children=true
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取文件夹详情成功",
     *   "data": {
     *     "id": 1,
     *     "name": "工作相关",
     *     "parent_id": null,
     *     "icon": "folder",
     *     "color": "#1890ff",
     *     "description": "存放工作相关的书签",
     *     "sort_order": 1,
     *     "level": 1,
     *     "path": "/1/",
     *     "full_path": "工作相关",
     *     "created_at": "2024-06-30 12:00:00",
     *     "updated_at": "2024-06-30 12:00:00",
     *     "stats": {
     *       "bookmark_count": 15,
     *       "subfolder_count": 3,
     *       "total_bookmark_count": 25
     *     },
     *     "children": [
     *       {
     *         "id": 2,
     *         "name": "前端开发",
     *         "bookmark_count": 8
     *       }
     *     ],
     *     "breadcrumbs": [
     *       {
     *         "id": 1,
     *         "name": "工作相关"
     *       }
     *     ]
     *   }
     * }
     */
    public function show(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $folderId = (int)$this->request->param('id');
            $withStatsParam = $this->request->get('with_stats', 'true');
            $withChildrenParam = $this->request->get('with_children', 'true');
            $withStats = $withStatsParam !== 'false' && $withStatsParam !== '0';
            $withChildren = $withChildrenParam !== 'false' && $withChildrenParam !== '0';

            $folder = $this->folderService->getFolderDetail($userId, $folderId, $withStats, $withChildren);
            return $this->success($folder, '获取文件夹详情成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('获取文件夹详情失败：' . $e->getMessage());
        }
    }

    /**
     * 更新文件夹
     *
     * 更新指定文件夹的信息，支持部分字段更新
     *
     * @route PUT /api/v1/folders/{id}
     * @middleware auth
     * @param int $id 文件夹ID，路径参数
     * @param string $name 文件夹名称，可选，1-100个字符
     * @param string $icon 图标，可选
     * @param string $color 颜色，可选，十六进制颜色值
     * @param string $description 描述，可选，最大500个字符
     * @return Response JSON响应，包含更新后的文件夹信息
     * @throws BusinessException 参数验证失败、文件夹不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * PUT /api/v1/folders/1
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "name": "工作相关 - 更新",
     *   "color": "#52c41a",
     *   "description": "更新后的描述"
     * }
     * Response: {
     *   "code": 200,
     *   "message": "更新文件夹成功",
     *   "data": {
     *     "id": 1,
     *     "name": "工作相关 - 更新",
     *     "color": "#52c41a",
     *     "description": "更新后的描述",
     *     "updated_at": "2024-06-30 12:30:00"
     *   }
     * }
     */
    public function update(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $folderId = (int)$this->request->param('id');
            $data = $this->request->put();
            $this->validate($data, FolderValidate::class . '.update');

            $folder = $this->folderService->updateFolder($userId, $folderId, $data);
            return $this->success($folder->toApiArray(), '更新文件夹成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('更新文件夹失败：' . $e->getMessage());
        }
    }

    /**
     * 删除文件夹
     *
     * 删除指定文件夹，可选择是否强制删除（包含子文件夹和书签）
     *
     * @route DELETE /api/v1/folders/{id}
     * @middleware auth
     * @param int $id 文件夹ID，路径参数
     * @param bool $force 是否强制删除，默认false，强制删除会删除所有子文件夹和书签
     * @return Response JSON响应，确认删除成功
     * @throws BusinessException 文件夹不存在、无权限访问或包含子项目且未强制删除
     * @throws \Exception 系统异常
     * @example
     * DELETE /api/v1/folders/1?force=false
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "删除文件夹成功",
     *   "data": null
     * }
     */
    public function delete(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $folderId = (int)$this->request->param('id');
            $force = (bool)$this->request->get('force', false);

            $this->folderService->deleteFolder($userId, $folderId, $force);
            return $this->success(null, '删除文件夹成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('删除文件夹失败：' . $e->getMessage());
        }
    }

    /**
     * 移动文件夹
     *
     * 将文件夹移动到指定的父文件夹下，支持移动到根目录
     *
     * @route PATCH /api/v1/folders/{id}/move
     * @middleware auth
     * @param int $id 要移动的文件夹ID，路径参数
     * @param int $parent_id 目标父文件夹ID，null表示移动到根目录
     * @param int $sort_order 在目标位置的排序顺序，可选
     * @return Response JSON响应，包含移动后的文件夹信息
     * @throws BusinessException 文件夹不存在、无权限访问或不能移动到自身或子文件夹
     * @throws \Exception 系统异常
     * @example
     * PATCH /api/v1/folders/1/move
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "parent_id": 2,
     *   "sort_order": 1
     * }
     * Response: {
     *   "code": 200,
     *   "message": "移动文件夹成功",
     *   "data": {
     *     "id": 1,
     *     "name": "工作相关",
     *     "parent_id": 2,
     *     "sort_order": 1,
     *     "updated_at": "2024-06-30 12:30:00"
     *   }
     * }
     */
    public function move(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $folderId = (int)$this->request->param('id');
            $data = $this->request->patch();
            $targetParentIdParam = $data['parent_id'] ?? null;
            // 处理parent_id参数：null、"null"、空字符串都视为null，其他转为int
            if ($targetParentIdParam === null || $targetParentIdParam === '' || $targetParentIdParam === 'null') {
                $targetParentId = null;
            } else {
                $targetParentId = (int)$targetParentIdParam;
            }

            $folder = $this->folderService->moveFolder($userId, $folderId, $targetParentId);
            return $this->success($folder->toApiArray(), '移动文件夹成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('移动文件夹失败：' . $e->getMessage());
        }
    }

    /**
     * 批量排序文件夹
     *
     * 批量更新文件夹的排序顺序，支持同级文件夹重新排序
     *
     * @route PATCH /api/v1/folders/sort
     * @middleware auth
     * @param array $folders 文件夹排序列表，包含id和sort_order字段
     * @return Response JSON响应，包含更新统计信息
     * @throws BusinessException 参数验证失败或无权限访问
     * @throws \Exception 系统异常
     * @example
     * PATCH /api/v1/folders/sort
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "folders": [
     *     {"id": 1, "sort_order": 1},
     *     {"id": 2, "sort_order": 2},
     *     {"id": 3, "sort_order": 3}
     *   ]
     * }
     * Response: {
     *   "code": 200,
     *   "message": "排序成功",
     *   "data": {
     *     "updated_count": 3
     *   }
     * }
     */
    public function sort(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $data = $this->request->patch();
            $sortData = $data['folders'] ?? [];

            $this->folderService->sortFolders($userId, $sortData);
            return $this->success(null, '排序成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('排序失败：' . $e->getMessage());
        }
    }
}
