import { defineStore } from 'pinia'
import type { Folder, CreateFolderRequest } from '@/types/api'
import { http } from '@/utils/request'

export const useFolderStore = defineStore('folder', () => {
  // 状态
  const folders = ref<Folder[]>([])
  const folderTree = ref<Folder[]>([])
  const currentFolder = ref<Folder | null>(null)
  const loading = ref(false)

  // 计算属性
  const rootFolders = computed(() =>
    (folders.value || []).filter(folder => !folder.parent_id)
  )

  const getFolderById = computed(() => (id: number) =>
    (folders.value || []).find(folder => folder.id === id)
  )

  const getChildFolders = computed(() => (parentId: number) =>
    (folders.value || []).filter(folder => folder.parent_id === parentId)
  )

  // 构建文件夹树
  const buildFolderTree = (folders: Folder[], parentId: number | null = null): Folder[] => {
    return folders
      .filter(folder => folder.parent_id === parentId)
      .map(folder => ({
        ...folder,
        children: buildFolderTree(folders, folder.id)
      }))
  }

  // 获取文件夹列表
  const fetchFolders = async () => {
    try {
      loading.value = true
      const response = await http.get('/folders')

      // 适配后端返回的数据结构
      const folderData = response.data.data || response.data || []
      folders.value = folderData
      folderTree.value = buildFolderTree(folderData)

      return folderData
    } catch (error) {
      console.error('获取文件夹列表失败:', error)
      // 确保在错误时也有默认值
      folders.value = []
      folderTree.value = []
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取文件夹详情
  const fetchFolder = async (id: number) => {
    try {
      const response = await http.get(`/folders/${id}`)
      currentFolder.value = response.data
      return response.data
    } catch (error) {
      console.error('获取文件夹详情失败:', error)
      throw error
    }
  }

  // 创建文件夹
  const createFolder = async (data: CreateFolderRequest) => {
    try {
      const response = await http.post('/folders', data)
      const newFolder = response.data
      
      // 添加到列表
      if (folders.value) {
        folders.value.push(newFolder)
      }

      // 重新构建树结构
      folderTree.value = buildFolderTree(folders.value || [])
      
      return newFolder
    } catch (error) {
      console.error('创建文件夹失败:', error)
      throw error
    }
  }

  // 更新文件夹
  const updateFolder = async (id: number, data: Partial<CreateFolderRequest>) => {
    try {
      const response = await http.put(`/folders/${id}`, data)
      const updatedFolder = response.data
      
      // 更新列表中的文件夹
      if (folders.value) {
        const index = folders.value.findIndex(folder => folder.id === id)
        if (index !== -1) {
          folders.value[index] = updatedFolder
        }
      }

      // 更新当前文件夹
      if (currentFolder.value?.id === id) {
        currentFolder.value = updatedFolder
      }

      // 重新构建树结构
      folderTree.value = buildFolderTree(folders.value || [])
      
      return updatedFolder
    } catch (error) {
      console.error('更新文件夹失败:', error)
      throw error
    }
  }

  // 删除文件夹
  const deleteFolder = async (id: number) => {
    try {
      await http.delete(`/folders/${id}`)
      
      // 从列表中移除
      if (folders.value) {
        folders.value = folders.value.filter(folder => folder.id !== id)
      }

      // 清除当前文件夹
      if (currentFolder.value?.id === id) {
        currentFolder.value = null
      }

      // 重新构建树结构
      folderTree.value = buildFolderTree(folders.value || [])
    } catch (error) {
      console.error('删除文件夹失败:', error)
      throw error
    }
  }

  // 移动文件夹
  const moveFolder = async (id: number, parentId: number | null, position?: number) => {
    try {
      await http.patch(`/folders/${id}/move`, {
        parent_id: parentId,
        position
      })
      
      // 重新获取文件夹列表
      await fetchFolders()
    } catch (error) {
      console.error('移动文件夹失败:', error)
      throw error
    }
  }

  // 文件夹排序
  const sortFolders = async (folderOrders: Array<{ id: number; position: number }>) => {
    try {
      await http.patch('/folders/sort', { folder_orders: folderOrders })
      
      // 重新获取文件夹列表
      await fetchFolders()
    } catch (error) {
      console.error('文件夹排序失败:', error)
      throw error
    }
  }

  // 获取文件夹路径
  const getFolderPath = (folderId: number): Folder[] => {
    const path: Folder[] = []
    let currentId: number | null = folderId
    
    while (currentId) {
      const folder = (folders.value || []).find(f => f.id === currentId)
      if (folder) {
        path.unshift(folder)
        currentId = folder.parent_id || null
      } else {
        break
      }
    }
    
    return path
  }

  // 检查是否为子文件夹
  const isChildFolder = (parentId: number, childId: number): boolean => {
    let currentId: number | null = childId
    
    while (currentId) {
      if (currentId === parentId) {
        return true
      }
      
      const folder = (folders.value || []).find(f => f.id === currentId)
      currentId = folder?.parent_id || null
    }
    
    return false
  }

  // 清空状态
  const clearFolders = () => {
    folders.value = []
    folderTree.value = []
    currentFolder.value = null
  }

  return {
    // 状态
    folders: readonly(folders),
    folderTree: readonly(folderTree),
    currentFolder: readonly(currentFolder),
    loading: readonly(loading),
    
    // 计算属性
    rootFolders,
    getFolderById,
    getChildFolders,
    
    // 方法
    fetchFolders,
    fetchFolder,
    createFolder,
    updateFolder,
    deleteFolder,
    moveFolder,
    sortFolders,
    getFolderPath,
    isChildFolder,
    clearFolders
  }
})
