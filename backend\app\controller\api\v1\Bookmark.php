<?php

namespace app\controller\api\v1;

use app\BaseController;
use app\service\BookmarkService;
use app\validate\BookmarkValidate;
use app\exception\BusinessException;
use think\Response;

/**
 * 书签管理控制器
 *
 * 提供书签的CRUD操作、批量操作、访问统计、AI解析、死链检测等功能
 */
class Bookmark extends BaseController
{
    /**
     * 书签服务
     * @var BookmarkService
     */
    protected $bookmarkService;

    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->bookmarkService = new BookmarkService();
    }

    /**
     * 获取书签列表
     *
     * 支持分页、筛选、排序等功能，获取当前用户的书签列表
     *
     * @route GET /api/v1/bookmarks
     * @middleware auth
     * @param int $page 页码，默认1
     * @param int $per_page 每页数量，默认20，最大100
     * @param int $folder_id 文件夹ID筛选，可选
     * @param int $tag_id 标签ID筛选，可选
     * @param int $is_star 是否星标筛选：0-否，1-是，可选
     * @param string $keyword 关键词搜索，可选
     * @param string $sort 排序方式：created_at|updated_at|visit_count|title，默认created_at
     * @param string $order 排序顺序：asc|desc，默认desc
     * @return Response JSON响应，包含书签列表和分页信息
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/bookmarks?page=1&per_page=20&folder_id=1&is_star=1&sort=visit_count&order=desc
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取书签列表成功",
     *   "data": {
     *     "list": [...],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 100,
     *       "last_page": 5
     *     }
     *   }
     * }
     */
    public function index(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $params = $this->request->get();
            $result = $this->bookmarkService->getBookmarkList($userId, $params);
            return $this->paginate($result['items'], $result['meta'], '获取书签列表成功');
        } catch (\Exception $e) {
            return $this->error('获取书签列表失败：' . $e->getMessage());
        }
    }

    /**
     * 创建书签
     *
     * 创建新的书签，支持设置标题、URL、描述、文件夹、标签等信息
     *
     * @route POST /api/v1/bookmarks
     * @middleware auth
     * @param string $title 书签标题，必填，1-255个字符
     * @param string $url 书签URL，必填，有效的URL格式
     * @param string $description 书签描述，可选，最大1000个字符
     * @param int $folder_id 所属文件夹ID，可选
     * @param array $tags 标签列表，可选，字符串数组
     * @param int $is_star 是否星标：0-否，1-是，默认0
     * @return Response JSON响应，包含创建的书签信息
     * @throws BusinessException 参数验证失败或业务逻辑错误
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/bookmarks
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "title": "GitHub",
     *   "url": "https://github.com",
     *   "description": "全球最大的代码托管平台",
     *   "folder_id": 1,
     *   "tags": ["技术", "开发"],
     *   "is_star": 1
     * }
     * Response: {
     *   "code": 201,
     *   "message": "创建书签成功",
     *   "data": {
     *     "id": 1,
     *     "title": "GitHub",
     *     "url": "https://github.com",
     *     "description": "全球最大的代码托管平台",
     *     "folder_id": 1,
     *     "is_star": 1,
     *     "tags": [...],
     *     "created_at": "2024-06-30 12:00:00"
     *   }
     * }
     */
    public function create(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $data = $this->request->post();
            $this->validate($data, BookmarkValidate::class . '.create');

            $bookmark = $this->bookmarkService->createBookmark($userId, $data);
            return $this->created($bookmark->toApiArray(), '创建书签成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('创建书签失败：' . $e->getMessage());
        }
    }

    /**
     * 获取书签详情
     *
     * 根据书签ID获取指定书签的详细信息，包含关联的标签信息
     *
     * @route GET /api/v1/bookmarks/{id}
     * @middleware auth
     * @param int $id 书签ID，路径参数
     * @return Response JSON响应，包含书签详细信息
     * @throws BusinessException 书签不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/bookmarks/1
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取书签详情成功",
     *   "data": {
     *     "id": 1,
     *     "title": "GitHub",
     *     "url": "https://github.com",
     *     "description": "全球最大的代码托管平台",
     *     "folder_id": 1,
     *     "is_star": 1,
     *     "visit_count": 10,
     *     "tags": [...],
     *     "created_at": "2024-06-30 12:00:00",
     *     "updated_at": "2024-06-30 12:00:00"
     *   }
     * }
     */
    public function show(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $bookmarkId = (int)$this->request->param('id');
            
            $bookmark = \app\model\Bookmark::where('id', $bookmarkId)
                ->where('user_id', $userId)
                ->with('tags')
                ->find();
                
            if (!$bookmark) {
                throw BusinessException::bookmarkNotFound();
            }

            return $this->success($bookmark->toApiArray(), '获取书签详情成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('获取书签详情失败：' . $e->getMessage());
        }
    }

    /**
     * 更新书签
     *
     * 更新指定书签的信息，支持部分字段更新
     *
     * @route PUT /api/v1/bookmarks/{id}
     * @middleware auth
     * @param int $id 书签ID，路径参数
     * @param string $title 书签标题，可选，1-255个字符
     * @param string $url 书签URL，可选，有效的URL格式
     * @param string $description 书签描述，可选，最大1000个字符
     * @param int $folder_id 所属文件夹ID，可选
     * @param array $tags 标签列表，可选，字符串数组
     * @param int $is_star 是否星标：0-否，1-是，可选
     * @return Response JSON响应，包含更新后的书签信息
     * @throws BusinessException 参数验证失败、书签不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * PUT /api/v1/bookmarks/1
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "title": "GitHub - 新标题",
     *   "description": "更新后的描述",
     *   "is_star": 0
     * }
     * Response: {
     *   "code": 200,
     *   "message": "更新书签成功",
     *   "data": {
     *     "id": 1,
     *     "title": "GitHub - 新标题",
     *     "description": "更新后的描述",
     *     "is_star": 0,
     *     "updated_at": "2024-06-30 12:30:00"
     *   }
     * }
     */
    public function update(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $bookmarkId = (int)$this->request->param('id');
            $data = $this->request->put();
            $this->validate($data, BookmarkValidate::class . '.update');

            $bookmark = $this->bookmarkService->updateBookmark($userId, $bookmarkId, $data);
            return $this->success($bookmark->toApiArray(), '更新书签成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('更新书签失败：' . $e->getMessage());
        }
    }

    /**
     * 删除书签
     *
     * 删除指定的书签，书签将被移动到回收站
     *
     * @route DELETE /api/v1/bookmarks/{id}
     * @middleware auth
     * @param int $id 书签ID，路径参数
     * @return Response JSON响应，确认删除成功
     * @throws BusinessException 书签不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * DELETE /api/v1/bookmarks/1
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "删除书签成功",
     *   "data": null
     * }
     */
    public function delete(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $bookmarkId = (int)$this->request->param('id');

            $this->bookmarkService->deleteBookmark($userId, $bookmarkId);
            return $this->success(null, '删除书签成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('删除书签失败：' . $e->getMessage());
        }
    }

    /**
     * 记录书签访问
     *
     * 记录用户访问书签的行为，更新访问次数和最后访问时间
     *
     * @route POST /api/v1/bookmarks/{id}/visit
     * @middleware auth
     * @param int $id 书签ID，路径参数
     * @return Response JSON响应，包含访问统计信息
     * @throws BusinessException 书签不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/bookmarks/1/visit
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "访问记录成功",
     *   "data": {
     *     "visit_count": 11,
     *     "last_visit_at": "2024-06-30 12:00:00"
     *   }
     * }
     */
    public function visit(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $bookmarkId = (int)$this->request->param('id');

            $this->bookmarkService->visitBookmark($userId, $bookmarkId);
            return $this->success(null, '访问记录成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('访问记录失败：' . $e->getMessage());
        }
    }

    /**
     * 批量操作书签
     *
     * 对多个书签执行批量操作，如删除、移动、添加标签等
     *
     * @route POST /api/v1/bookmarks/batch
     * @middleware auth
     * @param array $bookmark_ids 书签ID列表，必填
     * @param string $action 操作类型：delete|move|star|unstar|add_tags|remove_tags，必填
     * @param int $folder_id 目标文件夹ID，move操作时必填
     * @param array $tags 标签列表，add_tags/remove_tags操作时必填
     * @return Response JSON响应，包含批量操作结果统计
     * @throws BusinessException 参数验证失败或操作权限不足
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/bookmarks/batch
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "bookmark_ids": [1, 2, 3],
     *   "action": "move",
     *   "folder_id": 2
     * }
     * Response: {
     *   "code": 200,
     *   "message": "批量操作成功",
     *   "data": {
     *     "success_count": 2,
     *     "failed_count": 1,
     *     "failed_ids": [3]
     *   }
     * }
     */
    public function batch(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $data = $this->request->post();

            // 验证必需参数
            if (empty($data['bookmark_ids']) || !is_array($data['bookmark_ids'])) {
                return $this->error('请提供书签ID列表', 400);
            }

            if (empty($data['action'])) {
                return $this->error('请指定操作类型', 400);
            }

            $bookmarkIds = array_map('intval', $data['bookmark_ids']);
            $action = $data['action'];

            // 准备操作参数
            $params = [];
            switch ($action) {
                case 'move':
                    if (!isset($data['folder_id'])) {
                        return $this->error('移动操作需要指定目标文件夹ID', 400);
                    }
                    $params['folder_id'] = (int)$data['folder_id'];
                    break;

                case 'add_tags':
                case 'remove_tags':
                    if (empty($data['tags']) || !is_array($data['tags'])) {
                        return $this->error('标签操作需要指定标签列表', 400);
                    }
                    $params['tags'] = $data['tags'];
                    break;
            }

            // 执行批量操作
            $result = $this->bookmarkService->batchOperation($userId, $bookmarkIds, $action, $params);

            $message = $result['failed_count'] > 0
                ? "批量操作完成，成功 {$result['success_count']} 个，失败 {$result['failed_count']} 个"
                : "批量操作成功，共处理 {$result['success_count']} 个书签";

            return $this->success($result, $message);

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('批量操作失败：' . $e->getMessage());
        }
    }

    /**
     * AI解析书签
     *
     * 使用Moonshot AI技术解析书签网页内容，自动提取标题、摘要和建议标签
     *
     * @route POST /api/v1/bookmarks/{id}/parse
     * @middleware auth
     * @param int $id 书签ID，路径参数
     * @param string $url 可选，要解析的URL（如果不提供则解析现有书签的URL）
     * @return Response JSON响应，包含解析结果
     * @throws BusinessException 书签不存在、解析失败或无权限访问
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/bookmarks/1/parse
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     *
     * 或者解析指定URL：
     * POST /api/v1/bookmarks/parse
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "url": "https://baijiahao.baidu.com/s?id=1799673049077965780&wfr=spider&for=pc"
     * }
     *
     * Response: {
     *   "code": 200,
     *   "message": "AI解析成功",
     *   "data": {
     *     "title": "网页标题",
     *     "summary": "网页内容摘要...",
     *     "tags": ["标签1", "标签2", "标签3"]
     *   }
     * }
     */
    public function parse(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $bookmarkId = (int)$this->request->param('id', 0);
            $postData = $this->request->post();

            // 如果提供了URL参数，直接解析URL
            if (!empty($postData['url'])) {
                $result = $this->bookmarkService->parseUrlWithAI($postData['url']);
                return $this->success($result, 'AI解析成功');
            }

            // 如果没有书签ID，返回错误
            if (!$bookmarkId) {
                return $this->error('请提供书签ID或URL参数', 400);
            }

            // 解析现有书签
            $result = $this->bookmarkService->parseBookmarkWithAI($userId, $bookmarkId);
            return $this->success($result, 'AI解析成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('AI解析失败：' . $e->getMessage());
        }
    }

    /**
     * 检查书签死链
     *
     * 检查书签URL是否有效，更新死链状态和响应时间
     *
     * @route POST /api/v1/bookmarks/{id}/check
     * @middleware auth
     * @param int $id 书签ID，路径参数
     * @return Response JSON响应，包含检查结果
     * @throws BusinessException 书签不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/bookmarks/1/check
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "死链检测完成",
     *   "data": {
     *     "bookmark_id": 1,
     *     "url": "https://example.com",
     *     "is_dead": 0,
     *     "status_code": 200,
     *     "response_time": 1250.5,
     *     "last_check_at": "2024-07-21 12:00:00",
     *     "error_message": null
     *   }
     * }
     */
    public function check(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $bookmarkId = (int)$this->request->param('id', 0);

            if (!$bookmarkId) {
                return $this->error('请提供有效的书签ID', 400);
            }

            // 执行死链检测
            $result = $this->bookmarkService->checkBookmarkDeadLink($userId, $bookmarkId);

            $message = $result['is_dead'] ? '检测到死链' : '链接正常';
            return $this->success($result, $message);

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('死链检测失败：' . $e->getMessage());
        }
    }

    /**
     * 批量检测死链
     *
     * 批量检测用户的书签死链状态
     *
     * @route POST /api/v1/bookmarks/batch-check
     * @middleware auth
     * @param int $limit 检测数量限制，默认100
     * @param bool $only_dead_links 是否只检测已标记为死链的书签，默认false
     * @return Response JSON响应，包含批量检测结果
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/bookmarks/batch-check
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "limit": 50,
     *   "only_dead_links": false
     * }
     * Response: {
     *   "code": 200,
     *   "message": "批量检测完成",
     *   "data": {
     *     "total_checked": 50,
     *     "dead_links": 5,
     *     "alive_links": 45,
     *     "errors": 0,
     *     "details": [...]
     *   }
     * }
     */
    public function batchCheck(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            $postData = $this->request->post();

            $options = [
                'limit' => (int)($postData['limit'] ?? 100),
                'only_dead_links' => (bool)($postData['only_dead_links'] ?? false)
            ];

            // 执行批量死链检测
            $result = $this->bookmarkService->batchCheckDeadLinks($userId, $options);

            return $this->success($result, '批量检测完成');

        } catch (\Exception $e) {
            return $this->error('批量死链检测失败：' . $e->getMessage());
        }
    }

    /**
     * 获取死链统计
     *
     * 获取用户的死链统计信息
     *
     * @route GET /api/v1/bookmarks/dead-link-stats
     * @middleware auth
     * @return Response JSON响应，包含统计信息
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/bookmarks/dead-link-stats
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取统计信息成功",
     *   "data": {
     *     "total_bookmarks": 100,
     *     "dead_links": 5,
     *     "alive_links": 95,
     *     "checked_bookmarks": 80,
     *     "unchecked_bookmarks": 20,
     *     "dead_link_rate": 5.0
     *   }
     * }
     */
    public function deadLinkStats(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;

            // 获取死链统计信息
            $stats = $this->bookmarkService->getDeadLinkStats($userId);

            return $this->success($stats, '获取统计信息成功');

        } catch (\Exception $e) {
            return $this->error('获取统计信息失败：' . $e->getMessage());
        }
    }

    /**
     * 获取URL信息
     *
     * 根据提供的URL获取网页标题、描述、图标等信息，用于自动填充书签表单
     *
     * @route GET /api/v1/url-info
     * @middleware auth
     * @param string $url 要获取信息的URL
     * @return Response JSON响应，包含URL信息
     * @throws \Exception 系统异常
     * @example
     * 请求示例：
     * GET /api/v1/url-info?url=https://example.com
     *
     * 响应示例：
     * {
     *   "code": 0,
     *   "message": "获取URL信息成功",
     *   "data": {
     *     "title": "Example Domain",
     *     "description": "This domain is for use in illustrative examples",
     *     "favicon": "https://example.com/favicon.ico",
     *     "url": "https://example.com"
     *   }
     * }
     */
    public function getUrlInfo(): Response
    {
        try {
            $url = $this->request->get('url', '');

            if (empty($url)) {
                return $this->error('URL参数不能为空', 400);
            }

            // 验证URL格式
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                return $this->error('URL格式不正确', 400);
            }

            // 获取URL信息
            $urlInfo = $this->bookmarkService->getUrlInfo($url);

            return $this->success($urlInfo, '获取URL信息成功');

        } catch (\Exception $e) {
            return $this->error('获取URL信息失败：' . $e->getMessage());
        }
    }
}
