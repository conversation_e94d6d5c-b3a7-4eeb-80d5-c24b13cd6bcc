<?php

namespace app\controller\api\v1;

use app\BaseController;
use app\service\JobService;
use app\exception\BusinessException;
use think\Response;

/**
 * 任务状态控制器
 *
 * 提供异步任务状态查询功能，用于导入导出等长时间运行的任务
 */
class Job extends BaseController
{
    /**
     * 任务服务
     * @var JobService
     */
    protected $jobService;

    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->jobService = new JobService();
    }

    /**
     * 获取任务状态
     *
     * 查询指定任务的执行状态和进度信息
     *
     * @route GET /api/v1/jobs/{id}
     * @middleware auth
     * @param string $id 任务ID，路径参数
     * @return Response JSON响应，包含任务状态信息
     * @throws BusinessException 任务不存在或无权限访问
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/jobs/import_20240630_123456
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取任务状态成功",
     *   "data": {
     *     "job_id": "import_20240630_123456",
     *     "type": "import",
     *     "status": "completed",
     *     "progress": 100,
     *     "total_items": 150,
     *     "processed_items": 150,
     *     "success_items": 145,
     *     "failed_items": 5,
     *     "start_time": "2024-06-30 12:00:00",
     *     "end_time": "2024-06-30 12:05:30",
     *     "duration": 330,
     *     "result": {
     *       "imported_count": 145,
     *       "skipped_count": 5,
     *       "errors": [...]
     *     }
     *   }
     * }
     */
    public function status(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $jobId = $this->request->param('id');
            if (empty($jobId)) {
                return $this->error('任务ID不能为空', 40001);
            }

            // 获取任务状态
            $jobStatus = $this->jobService->getJobStatus($userId, $jobId);

            return $this->success($jobStatus, '获取任务状态成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('获取任务状态失败：' . $e->getMessage());
        }
    }
}
