<?php

namespace app\model;

use think\Model;

/**
 * 用户偏好设置模型
 */
class UserPreference extends Model
{
    // 表名
    protected $name = 'user_preferences';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'id' => 'integer',
        'user_id' => 'integer',
        'items_per_page' => 'integer',
        'auto_backup' => 'boolean',
        'email_notifications' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // 允许批量赋值的字段
    protected $field = [
        'user_id',
        'theme',
        'language',
        'items_per_page',
        'default_view',
        'auto_backup',
        'email_notifications'
    ];

    /**
     * 主题选项
     */
    const THEME_LIGHT = 'light';
    const THEME_DARK = 'dark';

    /**
     * 语言选项
     */
    const LANGUAGE_ZH_CN = 'zh-CN';
    const LANGUAGE_EN_US = 'en-US';

    /**
     * 视图选项
     */
    const VIEW_LIST = 'list';
    const VIEW_GRID = 'grid';
    const VIEW_CARD = 'card';

    /**
     * 关联用户
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 根据用户ID查找偏好设置
     * @param int $userId 用户ID
     * @return UserPreference|null
     */
    public static function findByUserId(int $userId): ?UserPreference
    {
        return self::where('user_id', $userId)->find();
    }

    /**
     * 获取用户偏好设置，如果不存在则创建默认设置
     * @param int $userId 用户ID
     * @return UserPreference
     */
    public static function getOrCreateByUserId(int $userId): UserPreference
    {
        $preference = self::findByUserId($userId);
        
        if (!$preference) {
            $preference = new self();
            $preference->user_id = $userId;
            $preference->theme = self::THEME_LIGHT;
            $preference->language = self::LANGUAGE_ZH_CN;
            $preference->items_per_page = 20;
            $preference->default_view = self::VIEW_LIST;
            $preference->auto_backup = true;
            $preference->email_notifications = true;
            $preference->save();
        }
        
        return $preference;
    }

    /**
     * 更新用户偏好设置
     * @param int $userId 用户ID
     * @param array $data 偏好设置数据
     * @return bool
     */
    public static function updateByUserId(int $userId, array $data): bool
    {
        $preference = self::getOrCreateByUserId($userId);
        
        // 只更新允许的字段
        $allowedFields = ['theme', 'language', 'items_per_page', 'default_view', 'auto_backup', 'email_notifications'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $preference->$field = $data[$field];
            }
        }
        
        return $preference->save();
    }

    /**
     * 获取默认偏好设置
     * @return array
     */
    public static function getDefaultPreferences(): array
    {
        return [
            'theme' => self::THEME_LIGHT,
            'language' => self::LANGUAGE_ZH_CN,
            'items_per_page' => 20,
            'default_view' => self::VIEW_LIST,
            'auto_backup' => true,
            'email_notifications' => true,
        ];
    }

    /**
     * 转换为API输出格式
     * @return array
     */
    public function toApiArray(): array
    {
        return [
            'theme' => $this->theme,
            'language' => $this->language,
            'items_per_page' => $this->items_per_page,
            'default_view' => $this->default_view,
            'auto_backup' => $this->auto_backup,
            'email_notifications' => $this->email_notifications,
        ];
    }

    /**
     * 验证主题值
     * @param string $theme
     * @return bool
     */
    public static function isValidTheme(string $theme): bool
    {
        return in_array($theme, [self::THEME_LIGHT, self::THEME_DARK]);
    }

    /**
     * 验证语言值
     * @param string $language
     * @return bool
     */
    public static function isValidLanguage(string $language): bool
    {
        return in_array($language, [self::LANGUAGE_ZH_CN, self::LANGUAGE_EN_US]);
    }

    /**
     * 验证视图值
     * @param string $view
     * @return bool
     */
    public static function isValidView(string $view): bool
    {
        return in_array($view, [self::VIEW_LIST, self::VIEW_GRID, self::VIEW_CARD]);
    }

    /**
     * 验证每页显示数量
     * @param int $itemsPerPage
     * @return bool
     */
    public static function isValidItemsPerPage(int $itemsPerPage): bool
    {
        return $itemsPerPage >= 10 && $itemsPerPage <= 100;
    }
}
