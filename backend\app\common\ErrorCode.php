<?php

namespace app\common;

/**
 * 错误码常量类
 */
class ErrorCode
{
    // 成功
    const SUCCESS = 0;

    // 通用错误码 (40000-40999)
    const PARAM_ERROR = 40001;          // 请求参数错误
    const UNAUTHORIZED = 40101;         // 未认证
    const TOKEN_EXPIRED = 40102;        // Token已过期
    const TOKEN_INVALID = 40103;        // Token无效
    const FORBIDDEN = 40301;            // 无权限访问
    const NOT_FOUND = 40401;            // 资源不存在
    const BUSINESS_ERROR = 42201;       // 业务逻辑错误
    const TOO_MANY_REQUESTS = 42901;    // 请求过于频繁
    const SERVER_ERROR = 50001;         // 服务器内部错误

    // 用户相关错误码 (10000-10999)
    const USER_EMAIL_EXISTS = 10001;    // 邮箱已被注册
    const USER_USERNAME_EXISTS = 10002; // 用户名已被使用
    const USER_LOGIN_FAILED = 10003;    // 账号或密码错误
    const USER_PASSWORD_WRONG = 10004;  // 原密码错误
    const USER_DISABLED = 10005;        // 账号已被禁用
    const USER_NOT_FOUND = 10006;       // 用户不存在

    // 文件夹相关错误码 (20000-20999)
    const FOLDER_NAME_EXISTS = 20001;   // 文件夹名称重复
    const FOLDER_MOVE_INVALID = 20002;  // 不能将文件夹移动到自身或子文件夹
    const FOLDER_LEVEL_LIMIT = 20003;   // 文件夹层级超过限制
    const FOLDER_NOT_FOUND = 20004;     // 文件夹不存在
    const FOLDER_NOT_EMPTY = 20005;     // 文件夹不为空

    // 书签相关错误码 (30000-30999)
    const BOOKMARK_URL_INVALID = 30001; // URL格式不正确
    const BOOKMARK_EXISTS = 30002;      // 书签已存在
    const BOOKMARK_BATCH_LIMIT = 30003; // 批量操作数量超过限制
    const BOOKMARK_NOT_FOUND = 30004;   // 书签不存在

    // 标签相关错误码 (40000-40999)
    const TAG_NAME_EXISTS = 40001;      // 标签名称已存在
    const TAG_NOT_FOUND = 40002;        // 标签不存在
    const TAG_IN_USE = 40003;           // 标签正在使用中

    // 导入导出相关错误码 (50000-50999)
    const IMPORT_FILE_INVALID = 50001;  // 文件格式不支持
    const IMPORT_FILE_TOO_LARGE = 50002; // 文件过大
    const IMPORT_PARSE_FAILED = 50003;  // 文件解析失败
    const EXPORT_FAILED = 50004;        // 导出失败

    // AI解析相关错误码 (60000-60999)
    const AI_SERVICE_UNAVAILABLE = 60001; // AI解析服务暂时不可用
    const AI_QUOTA_EXCEEDED = 60002;    // AI解析额度不足
    const AI_PARSE_FAILED = 60003;      // AI解析失败

    // 系统相关错误码 (70000-70999)
    const SYSTEM_MAINTENANCE = 70001;   // 系统维护中
    const SYSTEM_CONFIG_ERROR = 70002;  // 系统配置错误

    /**
     * 错误码对应的默认消息
     */
    const MESSAGES = [
        self::SUCCESS => '操作成功',
        
        // 通用错误
        self::PARAM_ERROR => '请求参数错误',
        self::UNAUTHORIZED => '未认证或认证失效',
        self::TOKEN_EXPIRED => 'Token已过期',
        self::TOKEN_INVALID => 'Token无效',
        self::FORBIDDEN => '无权限访问',
        self::NOT_FOUND => '资源不存在',
        self::BUSINESS_ERROR => '业务逻辑错误',
        self::TOO_MANY_REQUESTS => '请求过于频繁',
        self::SERVER_ERROR => '服务器内部错误',
        
        // 用户相关
        self::USER_EMAIL_EXISTS => '邮箱已被注册',
        self::USER_USERNAME_EXISTS => '用户名已被使用',
        self::USER_LOGIN_FAILED => '账号或密码错误',
        self::USER_PASSWORD_WRONG => '原密码错误',
        self::USER_DISABLED => '账号已被禁用',
        self::USER_NOT_FOUND => '用户不存在',
        
        // 文件夹相关
        self::FOLDER_NAME_EXISTS => '文件夹名称重复',
        self::FOLDER_MOVE_INVALID => '不能将文件夹移动到自身或子文件夹',
        self::FOLDER_LEVEL_LIMIT => '文件夹层级超过限制',
        self::FOLDER_NOT_FOUND => '文件夹不存在',
        self::FOLDER_NOT_EMPTY => '文件夹不为空',
        
        // 书签相关
        self::BOOKMARK_URL_INVALID => 'URL格式不正确',
        self::BOOKMARK_EXISTS => '书签已存在',
        self::BOOKMARK_BATCH_LIMIT => '批量操作数量超过限制',
        self::BOOKMARK_NOT_FOUND => '书签不存在',
        
        // 标签相关
        self::TAG_NAME_EXISTS => '标签名称已存在',
        self::TAG_NOT_FOUND => '标签不存在',
        self::TAG_IN_USE => '标签正在使用中',
        
        // 导入导出相关
        self::IMPORT_FILE_INVALID => '文件格式不支持',
        self::IMPORT_FILE_TOO_LARGE => '文件过大',
        self::IMPORT_PARSE_FAILED => '文件解析失败',
        self::EXPORT_FAILED => '导出失败',
        
        // AI解析相关
        self::AI_SERVICE_UNAVAILABLE => 'AI解析服务暂时不可用',
        self::AI_QUOTA_EXCEEDED => 'AI解析额度不足',
        self::AI_PARSE_FAILED => 'AI解析失败',
        
        // 系统相关
        self::SYSTEM_MAINTENANCE => '系统维护中',
        self::SYSTEM_CONFIG_ERROR => '系统配置错误',
    ];

    /**
     * 获取错误码对应的消息
     * @param int $code 错误码
     * @return string
     */
    public static function getMessage(int $code): string
    {
        return self::MESSAGES[$code] ?? '未知错误';
    }
}
