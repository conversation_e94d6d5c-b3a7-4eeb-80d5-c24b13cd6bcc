<?php

namespace app\controller\api\v1;

use app\BaseController;
use think\Response;

/**
 * 系统管理控制器
 *
 * 提供系统配置信息获取和API状态检查功能
 */
class System extends BaseController
{
    /**
     * 获取系统配置
     *
     * 获取系统的公开配置信息，包括应用信息、功能特性、限制参数等
     *
     * @route GET /api/v1/system/config
     * @middleware 无需认证（公开接口）
     * @return Response JSON响应，包含系统配置信息
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/system/config
     * Response: {
     *   "code": 200,
     *   "message": "获取系统配置成功",
     *   "data": {
     *     "app_name": "Web端书签管理系统",
     *     "version": "1.0.0",
     *     "api_version": "v1",
     *     "framework": "ThinkPHP 8.1.2",
     *     "features": {
     *       "user_auth": true,
     *       "folder_management": true,
     *       "ai_parsing": false
     *     },
     *     "limits": {
     *       "max_bookmarks_per_user": 10000,
     *       "jwt_ttl": 7200
     *     },
     *     "urls": {
     *       "api_base": "https://vscode.qidian.cc/api/v1",
     *       "frontend": "https://vscode.qidian.cc"
     *     }
     *   }
     * }
     */
    public function config(): Response
    {
        $config = [
            'app_name' => 'Web端书签管理系统',
            'version' => '1.0.0',
            'api_version' => 'v1',
            'build_time' => '2024-06-30',
            'framework' => 'ThinkPHP 8.1.2',
            'php_version' => PHP_VERSION,
            'features' => [
                'user_auth' => true,
                'folder_management' => true,
                'tag_management' => true,
                'bookmark_management' => true,
                'search' => true,
                'import_export' => true,
                'ai_parsing' => false,
                'dead_link_check' => false,
                'statistics' => true,
                'trash' => true,
            ],
            'limits' => [
                'max_folder_level' => 10,
                'max_bookmarks_per_user' => 10000,
                'max_tags_per_bookmark' => 20,
                'max_file_size' => '10MB',
                'max_devices_per_user' => 5,
                'jwt_ttl' => 2592000,
                'refresh_ttl' => 604800,
            ],
            'urls' => [
                'api_base' => 'https://vscode.qidian.cc:8000/api/v1',
                'frontend' => 'https://vscode.qidian.cc:3000',
                'docs' => 'https://vscode.qidian.cc:8000/docs',
            ]
        ];

        return $this->success($config, '获取系统配置成功');
    }

    /**
     * 获取API状态
     *
     * 获取API服务的运行状态，包括系统时间、内存使用、数据库和缓存状态
     *
     * @route GET /api/v1/system/status
     * @middleware 无需认证（公开接口）
     * @return Response JSON响应，包含API状态信息
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/system/status
     * Response: {
     *   "code": 200,
     *   "message": "API运行正常",
     *   "data": {
     *     "status": "ok",
     *     "timestamp": 1719734400,
     *     "datetime": "2024-06-30 12:00:00",
     *     "timezone": "Asia/Shanghai",
     *     "memory_usage": 8388608,
     *     "memory_peak": 16777216,
     *     "database": {
     *       "status": "connected",
     *       "message": "数据库连接正常"
     *     },
     *     "cache": {
     *       "status": "working",
     *       "message": "缓存工作正常"
     *     }
     *   }
     * }
     */
    public function status(): Response
    {
        $status = [
            'status' => 'ok',
            'timestamp' => time(),
            'datetime' => date('Y-m-d H:i:s'),
            'timezone' => date_default_timezone_get(),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'database' => $this->checkDatabase(),
            'cache' => $this->checkCache(),
        ];

        return $this->success($status, 'API运行正常');
    }

    /**
     * 检查数据库连接
     *
     * 测试数据库连接状态，返回连接结果
     *
     * @return array 包含状态和消息的数组
     * @internal 内部方法，用于状态检查
     */
    private function checkDatabase(): array
    {
        try {
            \think\facade\Db::query('SELECT 1');
            return [
                'status' => 'connected',
                'message' => '数据库连接正常'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => '数据库连接失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 检查缓存状态
     *
     * 测试缓存系统的读写功能，返回检查结果
     *
     * @return array 包含状态和消息的数组
     * @internal 内部方法，用于状态检查
     */
    private function checkCache(): array
    {
        try {
            \think\facade\Cache::set('health_check', time(), 60);
            $value = \think\facade\Cache::get('health_check');

            return [
                'status' => $value ? 'working' : 'error',
                'message' => $value ? '缓存工作正常' : '缓存读写失败'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => '缓存检查失败：' . $e->getMessage()
            ];
        }
    }
}
