{"name": "bookmark-manager-frontend", "version": "1.0.0", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@vueuse/core": "^10.3.0", "@vueuse/head": "^1.3.1", "axios": "^1.5.0", "dayjs": "^1.11.9", "element-plus": "^2.3.9", "jwt-decode": "^3.1.2", "lodash-es": "^4.17.21", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@types/jwt-decode": "^3.1.0", "@types/lodash-es": "^4.17.8", "@types/node": "^20.5.0", "@vitejs/plugin-vue": "^4.3.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.15", "eslint": "^8.46.0", "eslint-plugin-vue": "^9.16.1", "postcss": "^8.4.28", "prettier": "^3.0.0", "tailwindcss": "^3.3.3", "typescript": "~5.1.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.9", "vue-tsc": "^1.8.8"}}