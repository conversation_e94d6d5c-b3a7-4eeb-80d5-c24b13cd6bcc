<?php

namespace app\controller\api\v1;

use app\BaseController;
use app\service\TrashService;
use app\exception\BusinessException;
use think\Response;

/**
 * 回收站控制器
 *
 * 提供已删除项目的管理功能，包括查看、恢复和永久删除
 */
class Trash extends BaseController
{
    /**
     * 回收站服务
     * @var TrashService
     */
    protected $trashService;

    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->trashService = new TrashService();
    }

    /**
     * 获取回收站列表
     *
     * 获取当前用户回收站中的已删除项目列表，支持分页和筛选
     *
     * @route GET /api/v1/trash
     * @middleware auth
     * @param string $type 项目类型：bookmark|folder|all，默认all
     * @param int $page 页码，默认1
     * @param int $limit 每页数量，默认20，最大100
     * @param string $search 搜索关键词，可选
     * @param string $order_by 排序字段：deleted_at|name|type，默认deleted_at
     * @param string $order_dir 排序方向：asc|desc，默认desc
     * @return Response JSON响应，包含回收站项目列表
     * @throws \Exception 系统异常
     * @example
     * GET /api/v1/trash?type=bookmark&page=1&limit=20&search=技术
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "获取回收站列表成功",
     *   "data": {
     *     "list": [
     *       {
     *         "id": 1,
     *         "type": "bookmark",
     *         "title": "技术文章",
     *         "url": "https://example.com",
     *         "folder_name": "技术资料",
     *         "deleted_at": "2024-06-30 12:00:00",
     *         "auto_delete_at": "2024-07-30 12:00:00"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 15,
     *       "last_page": 1
     *     }
     *   }
     * }
     */
    public function index(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $params = $this->request->get();
            $result = $this->trashService->getTrashList($userId, $params);

            return $this->paginate($result['items'], $result['meta'], '获取回收站列表成功');
        } catch (\Exception $e) {
            return $this->error('获取回收站列表失败：' . $e->getMessage());
        }
    }

    /**
     * 恢复项目
     *
     * 从回收站恢复已删除的项目到原位置
     *
     * @route POST /api/v1/trash/{id}/restore
     * @middleware auth
     * @param int $id 项目ID，路径参数
     * @param string $type 项目类型：bookmark|folder，必填
     * @return Response JSON响应，确认恢复成功
     * @throws BusinessException 项目不存在、无权限访问或恢复失败
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/trash/1/restore
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "type": "bookmark"
     * }
     * Response: {
     *   "code": 200,
     *   "message": "恢复项目成功",
     *   "data": {
     *     "id": 1,
     *     "type": "bookmark",
     *     "title": "技术文章",
     *     "restored_to": "技术资料文件夹",
     *     "restored_at": "2024-06-30 12:30:00"
     *   }
     * }
     */
    public function restore(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $itemId = (int)$this->request->param('id');
            $data = $this->request->post();

            if (empty($data['type'])) {
                return $this->error('项目类型不能为空', 40001);
            }

            // 恢复项目
            $result = $this->trashService->restoreItem($userId, $itemId, $data['type']);

            return $this->success($result, '恢复项目成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('恢复项目失败：' . $e->getMessage());
        }
    }

    /**
     * 永久删除
     *
     * 永久删除回收站中的项目，此操作不可恢复
     *
     * @route DELETE /api/v1/trash/{id}
     * @middleware auth
     * @param int $id 项目ID，路径参数
     * @param string $type 项目类型：bookmark|folder，查询参数
     * @return Response JSON响应，确认永久删除成功
     * @throws BusinessException 项目不存在、无权限访问或删除失败
     * @throws \Exception 系统异常
     * @example
     * DELETE /api/v1/trash/1?type=bookmark
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * Response: {
     *   "code": 200,
     *   "message": "永久删除成功",
     *   "data": null
     * }
     */
    public function destroy(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $itemId = (int)$this->request->param('id');
            $type = $this->request->get('type');

            if (empty($type)) {
                return $this->error('项目类型不能为空', 40001);
            }

            // 永久删除项目
            $this->trashService->destroyItem($userId, $itemId, $type);

            return $this->success(null, '永久删除成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('永久删除失败：' . $e->getMessage());
        }
    }

    /**
     * 清空回收站
     *
     * 永久删除回收站中的所有项目，此操作不可恢复
     *
     * @route DELETE /api/v1/trash/clear
     * @middleware auth
     * @param string $type 项目类型：bookmark|folder|all，默认all
     * @param bool $confirm 确认清空，必须为true
     * @return Response JSON响应，确认清空成功
     * @throws BusinessException 确认参数错误或清空失败
     * @throws \Exception 系统异常
     * @example
     * DELETE /api/v1/trash/clear
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "type": "all",
     *   "confirm": true
     * }
     * Response: {
     *   "code": 200,
     *   "message": "清空回收站成功",
     *   "data": {
     *     "deleted_bookmarks": 15,
     *     "deleted_folders": 3,
     *     "total_deleted": 18
     *   }
     * }
     */
    public function clear(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            $data = $this->request->delete();
            $type = $data['type'] ?? 'all';
            $confirm = $data['confirm'] ?? false;

            if (!$confirm) {
                return $this->error('请确认清空回收站操作', 40001);
            }

            // 清空回收站
            $result = $this->trashService->clearTrash($userId, $type);

            return $this->success($result, '清空回收站成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode());
        } catch (\Exception $e) {
            return $this->error('清空回收站失败：' . $e->getMessage());
        }
    }
}
