<template>
  <div class="starred-bookmarks">
    <div class="page-header">
      <h1>收藏书签</h1>
      <div class="header-actions">
        <el-button @click="refreshBookmarks" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
    
    <div class="bookmarks-content">
      <BookmarkList 
        :starred="true"
        @add="handleAdd"
        @edit="handleEdit"
        @move="handleMove"
        @delete="handleDelete"
      />
    </div>
    
    <!-- 添加书签对话框 -->
    <BookmarkForm
      v-model="showAddDialog"
      @success="handleBookmarkSuccess"
    />
    
    <!-- 编辑书签对话框 -->
    <BookmarkForm
      v-model="showEditDialog"
      :bookmark="currentBookmark"
      @success="handleBookmarkSuccess"
    />
    
    <!-- 移动书签对话框 -->
    <FolderSelectDialog
      v-model="showMoveDialog"
      :bookmark="currentBookmark"
      @success="handleMoveSuccess"
    />
    
    <!-- 删除确认对话框 -->
    <AppConfirm
      v-model="showDeleteDialog"
      type="warning"
      title="删除书签"
      :message="`确定要删除书签「${currentBookmark?.title}」吗？`"
      @confirm="handleDeleteConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { Refresh } from '@element-plus/icons-vue'
import type { Bookmark } from '@/types/api'
import { useBookmarkStore } from '@/stores/bookmark'
import BookmarkList from '@/components/business/BookmarkList.vue'
import BookmarkForm from '@/components/business/BookmarkForm.vue'
import FolderSelectDialog from '@/components/business/FolderSelectDialog.vue'
import AppConfirm from '@/components/common/AppConfirm.vue'

const bookmarkStore = useBookmarkStore()

const loading = ref(false)
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const showMoveDialog = ref(false)
const showDeleteDialog = ref(false)
const currentBookmark = ref<Bookmark | null>(null)

const refreshBookmarks = async () => {
  loading.value = true
  try {
    await bookmarkStore.fetchBookmarks({ is_star: true })
  } catch (error) {
    console.error('刷新收藏书签失败:', error)
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  showAddDialog.value = true
}

const handleEdit = (bookmark: Bookmark) => {
  currentBookmark.value = bookmark
  showEditDialog.value = true
}

const handleMove = (bookmark: Bookmark) => {
  currentBookmark.value = bookmark
  showMoveDialog.value = true
}

const handleDelete = (bookmark: Bookmark) => {
  currentBookmark.value = bookmark
  showDeleteDialog.value = true
}

const handleBookmarkSuccess = () => {
  refreshBookmarks()
}

const handleMoveSuccess = () => {
  refreshBookmarks()
}

const handleDeleteConfirm = async () => {
  if (!currentBookmark.value) return
  
  try {
    await bookmarkStore.deleteBookmark(currentBookmark.value.id)
    ElMessage.success('书签删除成功')
    refreshBookmarks()
  } catch (error) {
    console.error('删除书签失败:', error)
    ElMessage.error('删除失败')
  }
}

// 设置页面标题
useHead({
  title: '收藏书签 - 书签管理系统'
})

onMounted(() => {
  refreshBookmarks()
})
</script>

<style scoped>
.starred-bookmarks {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.bookmarks-content {
  flex: 1;
  overflow: hidden;
}
</style>
