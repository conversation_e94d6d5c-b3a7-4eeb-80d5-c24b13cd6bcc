<template>
  <div class="not-found">
    <div class="error-content">
      <div class="error-icon">
        <el-icon size="120" color="#c0c4cc">
          <Warning />
        </el-icon>
      </div>
      <h1 class="error-title">404</h1>
      <p class="error-message">抱歉，您访问的页面不存在</p>
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          返回首页
        </el-button>
        <el-button @click="goBack">
          返回上页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Warning } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/dashboard')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

.error-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
}

.error-icon {
  margin-bottom: 20px;
}

.error-title {
  font-size: 72px;
  font-weight: 600;
  color: #c0c4cc;
  margin: 0 0 16px 0;
}

.error-message {
  font-size: 18px;
  color: #909399;
  margin: 0 0 32px 0;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style>
