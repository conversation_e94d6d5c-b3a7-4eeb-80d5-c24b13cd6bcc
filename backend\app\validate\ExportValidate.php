<?php

namespace app\validate;

use think\Validate;

/**
 * 导出验证器
 */
class ExportValidate extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'format' => 'in:html,json,csv',
        'folder_ids' => 'array',
        'folder_ids.*' => 'integer|egt:1',
        'include_folders' => 'boolean',
        'include_tags' => 'boolean',
        'encoding' => 'in:utf-8,gbk',
    ];

    /**
     * 验证消息
     */
    protected $message = [
        'format.in' => '导出格式不支持，仅支持html、json、csv格式',
        'folder_ids.array' => '文件夹ID列表必须是数组格式',
        'folder_ids.*.integer' => '文件夹ID必须是整数',
        'folder_ids.*.egt' => '文件夹ID必须大于0',
        'include_folders.boolean' => '包含文件夹参数必须是布尔值',
        'include_tags.boolean' => '包含标签参数必须是布尔值',
        'encoding.in' => '文件编码不支持，仅支持utf-8、gbk编码',
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'export' => ['format', 'folder_ids', 'folder_ids.*', 'include_folders', 'include_tags', 'encoding'],
    ];

    /**
     * 自定义验证文件夹数量
     * @param mixed $value
     * @param mixed $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkFolderCount($value, $rule, $data)
    {
        if (!is_array($value)) {
            return '文件夹ID列表必须是数组格式';
        }

        if (count($value) > 50) {
            return '一次最多只能导出50个文件夹';
        }

        return true;
    }
}
