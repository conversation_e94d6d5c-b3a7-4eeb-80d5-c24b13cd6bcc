-- ============================================
-- Web端书签管理系统 - 数据库脚本
-- 版本: 1.0
-- 数据库: MySQL 8.0+
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_unicode_ci
-- ============================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `bookmark_system` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE `bookmark_system`;

-- ============================================
-- 1. 用户表 (users)
-- ============================================
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `email` VARCHAR(100) NOT NULL COMMENT '邮箱地址',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(255) NOT NULL COMMENT '密码（bcrypt加密）',
    `avatar` VARCHAR(255) NULL DEFAULT NULL COMMENT '头像URL',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    `last_login_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(45) NULL DEFAULT NULL COMMENT '最后登录IP',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_email` (`email`),
    UNIQUE KEY `uk_username` (`username`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ============================================
-- 2. 用户认证令牌表 (user_tokens)
-- ============================================
DROP TABLE IF EXISTS `user_tokens`;
CREATE TABLE `user_tokens` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '令牌ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `token_type` VARCHAR(20) NOT NULL DEFAULT 'refresh' COMMENT '令牌类型：refresh',
    `token` VARCHAR(500) NOT NULL COMMENT '令牌值',
    `expires_at` TIMESTAMP NOT NULL COMMENT '过期时间',
    `device_info` VARCHAR(255) NULL DEFAULT NULL COMMENT '设备信息',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_token` (`token`(191)),
    KEY `idx_expires_at` (`expires_at`),
    CONSTRAINT `fk_user_tokens_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户认证令牌表';

-- ============================================
-- 3. 文件夹表 (folders)
-- ============================================
DROP TABLE IF EXISTS `folders`;
CREATE TABLE `folders` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '文件夹ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `parent_id` BIGINT UNSIGNED NULL DEFAULT NULL COMMENT '父文件夹ID',
    `name` VARCHAR(100) NOT NULL COMMENT '文件夹名称',
    `icon` VARCHAR(50) NULL DEFAULT NULL COMMENT '图标（emoji或预设图标代码）',
    `color` VARCHAR(7) NULL DEFAULT NULL COMMENT '颜色代码（#开头的16进制）',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    `path` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '文件夹路径（如：/1/2/3/）',
    `level` TINYINT NOT NULL DEFAULT 1 COMMENT '层级深度',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_path` (`path`(191)),
    KEY `idx_sort_order` (`sort_order`),
    CONSTRAINT `fk_folders_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_folders_parent` FOREIGN KEY (`parent_id`) REFERENCES `folders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件夹表';

-- ============================================
-- 4. 书签表 (bookmarks)
-- ============================================
DROP TABLE IF EXISTS `bookmarks`;
CREATE TABLE `bookmarks` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '书签ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `folder_id` BIGINT UNSIGNED NULL DEFAULT NULL COMMENT '所属文件夹ID',
    `title` VARCHAR(255) NOT NULL COMMENT '标题',
    `url` VARCHAR(2000) NOT NULL COMMENT 'URL地址',
    `description` TEXT NULL DEFAULT NULL COMMENT '描述',
    `favicon` VARCHAR(500) NULL DEFAULT NULL COMMENT '网站图标URL',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    `is_star` TINYINT NOT NULL DEFAULT 0 COMMENT '是否星标：0-否，1-是',
    `visit_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '访问次数',
    `last_visit_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后访问时间',
    `is_dead` TINYINT NOT NULL DEFAULT 0 COMMENT '是否死链：0-正常，1-死链',
    `last_check_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后检测时间',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_folder_id` (`folder_id`),
    KEY `idx_url` (`url`(191)),
    KEY `idx_is_star` (`is_star`),
    KEY `idx_visit_count` (`visit_count`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_user_folder` (`user_id`, `folder_id`),
    CONSTRAINT `fk_bookmarks_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_bookmarks_folders` FOREIGN KEY (`folder_id`) REFERENCES `folders` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='书签表';

-- ============================================
-- 5. 标签表 (tags)
-- ============================================
DROP TABLE IF EXISTS `tags`;
CREATE TABLE `tags` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `name` VARCHAR(50) NOT NULL COMMENT '标签名称',
    `color` VARCHAR(7) NULL DEFAULT NULL COMMENT '标签颜色（#开头的16进制）',
    `usage_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用次数',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_tag` (`user_id`, `name`),
    KEY `idx_usage_count` (`usage_count`),
    CONSTRAINT `fk_tags_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';

-- ============================================
-- 6. 书签标签关联表 (bookmark_tags)
-- ============================================
DROP TABLE IF EXISTS `bookmark_tags`;
CREATE TABLE `bookmark_tags` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `bookmark_id` BIGINT UNSIGNED NOT NULL COMMENT '书签ID',
    `tag_id` BIGINT UNSIGNED NOT NULL COMMENT '标签ID',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_bookmark_tag` (`bookmark_id`, `tag_id`),
    KEY `idx_tag_id` (`tag_id`),
    CONSTRAINT `fk_bookmark_tags_bookmarks` FOREIGN KEY (`bookmark_id`) REFERENCES `bookmarks` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_bookmark_tags_tags` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='书签标签关联表';

-- ============================================
-- 7. AI解析结果表 (ai_parse_results)
-- ============================================
DROP TABLE IF EXISTS `ai_parse_results`;
CREATE TABLE `ai_parse_results` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '解析结果ID',
    `bookmark_id` BIGINT UNSIGNED NOT NULL COMMENT '书签ID',
    `summary` TEXT NULL DEFAULT NULL COMMENT 'AI生成的摘要',
    `keywords` JSON NULL DEFAULT NULL COMMENT 'AI提取的关键词列表',
    `content_type` VARCHAR(50) NULL DEFAULT NULL COMMENT '内容类型：article/video/code/news等',
    `parse_status` TINYINT NOT NULL DEFAULT 0 COMMENT '解析状态：0-待解析，1-解析中，2-成功，3-失败',
    `error_message` TEXT NULL DEFAULT NULL COMMENT '解析失败原因',
    `parsed_at` TIMESTAMP NULL DEFAULT NULL COMMENT '解析完成时间',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_bookmark_id` (`bookmark_id`),
    KEY `idx_parse_status` (`parse_status`),
    KEY `idx_content_type` (`content_type`),
    CONSTRAINT `fk_ai_parse_bookmarks` FOREIGN KEY (`bookmark_id`) REFERENCES `bookmarks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI解析结果表';

-- ============================================
-- 8. 智能文件夹表 (smart_folders)
-- ============================================
DROP TABLE IF EXISTS `smart_folders`;
CREATE TABLE `smart_folders` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '智能文件夹ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `name` VARCHAR(100) NOT NULL COMMENT '文件夹名称',
    `icon` VARCHAR(50) NULL DEFAULT NULL COMMENT '图标',
    `conditions` JSON NOT NULL COMMENT '搜索条件（最多10个）',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_sort_order` (`sort_order`),
    CONSTRAINT `fk_smart_folders_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='智能文件夹表';

-- ============================================
-- 9. 回收站表 (trash)
-- ============================================
DROP TABLE IF EXISTS `trash`;
CREATE TABLE `trash` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '回收站ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `item_type` VARCHAR(20) NOT NULL COMMENT '项目类型：bookmark/folder',
    `item_id` BIGINT UNSIGNED NOT NULL COMMENT '原始项目ID',
    `item_data` JSON NOT NULL COMMENT '项目完整数据（JSON格式）',
    `deleted_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '删除时间',
    `expire_at` TIMESTAMP NOT NULL DEFAULT (CURRENT_TIMESTAMP + INTERVAL 365 DAY) COMMENT '过期时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_item_type_id` (`item_type`, `item_id`),
    KEY `idx_expire_at` (`expire_at`),
    CONSTRAINT `fk_trash_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回收站表';

-- ============================================
-- 10. 操作日志表 (operation_logs)
-- ============================================
DROP TABLE IF EXISTS `operation_logs`;
CREATE TABLE `operation_logs` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    `operation` VARCHAR(50) NOT NULL COMMENT '操作类型',
    `module` VARCHAR(30) NOT NULL COMMENT '模块名称',
    `content` JSON NULL DEFAULT NULL COMMENT '操作详情',
    `ip` VARCHAR(45) NOT NULL COMMENT 'IP地址',
    `user_agent` VARCHAR(255) NULL DEFAULT NULL COMMENT '用户代理',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_operation` (`operation`),
    KEY `idx_created_at` (`created_at`),
    CONSTRAINT `fk_logs_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- ============================================
-- 11. 系统配置表 (system_configs)
-- ============================================
DROP TABLE IF EXISTS `system_configs`;
CREATE TABLE `system_configs` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `value` TEXT NULL DEFAULT NULL COMMENT '配置值',
    `type` VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '值类型：string/number/boolean/json',
    `description` VARCHAR(255) NULL DEFAULT NULL COMMENT '配置说明',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- ============================================
-- 12. 队列任务表 (queue_jobs) - ThinkPHP Queue
-- ============================================
DROP TABLE IF EXISTS `queue_jobs`;
CREATE TABLE `queue_jobs` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `queue` VARCHAR(255) NOT NULL,
    `payload` LONGTEXT NOT NULL,
    `attempts` TINYINT UNSIGNED NOT NULL,
    `reserved_at` INT UNSIGNED DEFAULT NULL,
    `available_at` INT UNSIGNED NOT NULL,
    `created_at` INT UNSIGNED NOT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_queue_reserved_at` (`queue`, `reserved_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='队列任务表';

-- ============================================
-- 初始化数据
-- ============================================

-- 插入系统配置
INSERT INTO `system_configs` (`key`, `value`, `type`, `description`) VALUES
('site.name', '书签管理系统', 'string', '网站名称'),
('site.logo', '/assets/logo.png', 'string', '网站Logo'),
('ai.provider', 'qwen', 'string', 'AI服务提供商'),
('ai.model', 'qwen-turbo', 'string', 'AI模型'),
('ai.api_key', '', 'string', 'AI API密钥'),
('mail.driver', 'smtp', 'string', '邮件驱动'),
('mail.host', 'smtp.example.com', 'string', 'SMTP服务器'),
('mail.port', '465', 'number', 'SMTP端口'),
('mail.username', '', 'string', 'SMTP用户名'),
('mail.password', '', 'string', 'SMTP密码'),
('mail.encryption', 'ssl', 'string', '加密方式'),
('backup.retain_days', '30', 'number', '备份保留天数'),
('user.avatar_max_size', '2097152', 'number', '头像最大字节数(2MB)'),
('import.max_file_size', '10485760', 'number', '导入文件最大字节数(10MB)'),
('batch.max_items', '300', 'number', '批量操作最大数量');

-- 插入默认管理员账号（密码：admin123）
-- 注意：实际部署时请修改默认密码
INSERT INTO `users` (`email`, `username`, `password`, `status`) VALUES
('<EMAIL>', 'admin', '$2y$12$YourBcryptHashHere', 1);

-- ============================================
-- 触发器
-- ============================================

-- 更新标签使用次数的触发器
DELIMITER $$

CREATE TRIGGER `update_tag_usage_count_insert` 
AFTER INSERT ON `bookmark_tags` 
FOR EACH ROW
BEGIN
    UPDATE `tags` SET `usage_count` = `usage_count` + 1 WHERE `id` = NEW.`tag_id`;
END$$

CREATE TRIGGER `update_tag_usage_count_delete` 
AFTER DELETE ON `bookmark_tags` 
FOR EACH ROW
BEGIN
    UPDATE `tags` SET `usage_count` = `usage_count` - 1 WHERE `id` = OLD.`tag_id`;
END$$

DELIMITER ;

-- ============================================
-- 存储过程
-- ============================================

-- 更新文件夹路径的存储过程
DELIMITER $$

CREATE PROCEDURE `update_folder_path`(IN folder_id BIGINT)
BEGIN
    DECLARE parent_path VARCHAR(500);
    DECLARE parent_level INT;
    
    SELECT `path`, `level` INTO parent_path, parent_level 
    FROM `folders` 
    WHERE `id` = (SELECT `parent_id` FROM `folders` WHERE `id` = folder_id);
    
    IF parent_path IS NULL THEN
        UPDATE `folders` 
        SET `path` = CONCAT('/', folder_id, '/'), `level` = 1 
        WHERE `id` = folder_id;
    ELSE
        UPDATE `folders` 
        SET `path` = CONCAT(parent_path, folder_id, '/'), `level` = parent_level + 1 
        WHERE `id` = folder_id;
    END IF;
END$$

-- 清理过期回收站数据的存储过程
CREATE PROCEDURE `clean_expired_trash`()
BEGIN
    DELETE FROM `trash` WHERE `expire_at` < NOW();
END$$

-- 清理过期令牌的存储过程
CREATE PROCEDURE `clean_expired_tokens`()
BEGIN
    DELETE FROM `user_tokens` WHERE `expires_at` < NOW();
END$$

DELIMITER ;

-- ============================================
-- 定时任务（需要在应用层或使用MySQL事件调度器实现）
-- ============================================

-- 启用事件调度器
SET GLOBAL event_scheduler = ON;

-- 每天凌晨3点清理过期数据
DELIMITER $$

CREATE EVENT IF NOT EXISTS `clean_expired_data_daily`
ON SCHEDULE EVERY 1 DAY
STARTS CONCAT(DATE(NOW() + INTERVAL 1 DAY), ' 03:00:00')
DO
BEGIN
    CALL clean_expired_trash();
    CALL clean_expired_tokens();
    
    -- 清理90天前的操作日志
    DELETE FROM `operation_logs` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 90 DAY);
END$$

DELIMITER ;

-- ============================================
-- 性能优化建议
-- ============================================

-- 1. 分区建议（当数据量达到千万级时考虑）
-- ALTER TABLE `bookmarks` PARTITION BY RANGE (user_id) (
--     PARTITION p0 VALUES LESS THAN (10000),
--     PARTITION p1 VALUES LESS THAN (20000),
--     PARTITION p2 VALUES LESS THAN (30000),
--     PARTITION p3 VALUES LESS THAN (MAXVALUE)
-- );

-- 2. 查询优化建议
-- ANALYZE TABLE `bookmarks`, `folders`, `tags`, `bookmark_tags`;

-- ============================================
-- 权限设置（根据实际情况调整）
-- ============================================

-- 创建应用程序专用用户
-- CREATE USER 'bookmark_app'@'localhost' IDENTIFIED BY 'StrongPassword123!';
-- GRANT SELECT, INSERT, UPDATE, DELETE, EXECUTE ON `bookmark_system`.* TO 'bookmark_app'@'localhost';
-- FLUSH PRIVILEGES;

-- ============================================
-- 备份恢复测试
-- ============================================

-- 备份命令示例
-- mysqldump -u root -p bookmark_system > bookmark_system_backup_$(date +%Y%m%d_%H%M%S).sql

-- 恢复命令示例
-- mysql -u root -p bookmark_system < bookmark_system_backup_20240120_120000.sql 