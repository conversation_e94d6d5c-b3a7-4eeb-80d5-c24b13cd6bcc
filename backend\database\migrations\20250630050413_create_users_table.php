<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateUsersTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $table = $this->table('users', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => '用户表'
        ]);

        $table->addColumn('id', 'biginteger', [
            'identity' => true,
            'signed' => false,
            'comment' => '用户ID'
        ])
        ->addColumn('email', 'string', [
            'limit' => 100,
            'null' => false,
            'comment' => '邮箱地址'
        ])
        ->addColumn('username', 'string', [
            'limit' => 50,
            'null' => false,
            'comment' => '用户名'
        ])
        ->addColumn('password', 'string', [
            'limit' => 255,
            'null' => false,
            'comment' => '密码（bcrypt加密）'
        ])
        ->addColumn('avatar', 'string', [
            'limit' => 255,
            'null' => true,
            'default' => null,
            'comment' => '头像URL'
        ])
        ->addColumn('status', 'integer', [
            'limit' => 1,
            'null' => false,
            'default' => 1,
            'comment' => '状态：0-禁用，1-正常'
        ])
        ->addColumn('last_login_at', 'timestamp', [
            'null' => true,
            'default' => null,
            'comment' => '最后登录时间'
        ])
        ->addColumn('last_login_ip', 'string', [
            'limit' => 45,
            'null' => true,
            'default' => null,
            'comment' => '最后登录IP'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => false,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addColumn('updated_at', 'timestamp', [
            'null' => false,
            'default' => 'CURRENT_TIMESTAMP',
            'update' => 'CURRENT_TIMESTAMP',
            'comment' => '更新时间'
        ])
        ->addIndex(['email'], ['unique' => true, 'name' => 'uk_email'])
        ->addIndex(['username'], ['unique' => true, 'name' => 'uk_username'])
        ->addIndex(['status'], ['name' => 'idx_status'])
        ->addIndex(['created_at'], ['name' => 'idx_created_at'])
        ->create();
    }
}
