<template>
  <div class="app-pagination" v-if="total > 0">
    <div class="pagination-info">
      <span class="info-text">
        共 {{ total }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
      </span>
    </div>
    
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="pageSizes"
      :total="total"
      :layout="layout"
      :background="background"
      :small="small"
      :disabled="disabled"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  total: number
  currentPage?: number
  pageSize?: number
  pageSizes?: number[]
  layout?: string
  background?: boolean
  small?: boolean
  disabled?: boolean
  showInfo?: boolean
}

interface Emits {
  (e: 'update:currentPage', value: number): void
  (e: 'update:pageSize', value: number): void
  (e: 'change', page: number, size: number): void
  (e: 'sizeChange', size: number): void
  (e: 'currentChange', page: number): void
}

const props = withDefaults(defineProps<Props>(), {
  currentPage: 1,
  pageSize: 20,
  pageSizes: () => [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper',
  background: true,
  small: false,
  disabled: false,
  showInfo: true
})

const emit = defineEmits<Emits>()

const currentPage = computed({
  get: () => props.currentPage,
  set: (value) => emit('update:currentPage', value)
})

const pageSize = computed({
  get: () => props.pageSize,
  set: (value) => emit('update:pageSize', value)
})

const totalPages = computed(() => Math.ceil(props.total / props.pageSize))

const handleSizeChange = (size: number) => {
  emit('sizeChange', size)
  emit('change', currentPage.value, size)
}

const handleCurrentChange = (page: number) => {
  emit('currentChange', page)
  emit('change', page, pageSize.value)
}
</script>

<style scoped>
.app-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 16px 0;
}

.pagination-info {
  flex-shrink: 0;
}

.info-text {
  font-size: 14px;
  color: #606266;
}

.el-pagination {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .app-pagination {
    flex-direction: column;
    gap: 12px;
  }
  
  .el-pagination {
    justify-content: center;
  }
}
</style>
