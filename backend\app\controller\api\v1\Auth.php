<?php

namespace app\controller\api\v1;

use app\BaseController;
use app\service\AuthService;
use app\validate\AuthValidate;
use app\exception\BusinessException;
use think\Response;

/**
 * 认证控制器
 */
class Auth extends BaseController
{
    /**
     * 认证服务
     * @var AuthService
     */
    protected $authService;

    /**
     * 初始化
     */
    protected function initialize()
    {
        parent::initialize();
        $this->authService = new AuthService();
    }

    /**
     * 用户注册
     *
     * 创建新用户账户，支持用户名和邮箱注册
     *
     * @route POST /api/v1/auth/register
     * @middleware 无需认证
     * @param string $username 用户名，3-20个字符，支持字母、数字、下划线
     * @param string $email 邮箱地址，必须是有效的邮箱格式
     * @param string $password 密码，6-20个字符
     * @param string $password_confirm 确认密码，必须与密码一致
     * @param string $nickname 昵称，可选，1-50个字符
     * @return Response JSON响应，包含用户信息（不含敏感数据）
     * @throws BusinessException 参数验证失败或用户名/邮箱已存在
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/auth/register
     * {
     *   "username": "testuser",
     *   "email": "<EMAIL>",
     *   "password": "123456",
     *   "password_confirm": "123456",
     *   "nickname": "测试用户"
     * }
     * Response: {
     *   "code": 201,
     *   "message": "注册成功",
     *   "data": {
     *     "id": 1,
     *     "username": "testuser",
     *     "email": "<EMAIL>",
     *     "nickname": "测试用户",
     *     "status": 1,
     *     "created_at": "2024-06-30 12:00:00"
     *   }
     * }
     */
    public function register(): Response
    {
        try {
            // 参数验证
            $data = $this->request->post();
            $this->validate($data, AuthValidate::class . '.register');
           
            // 执行注册
            $user = $this->authService->register($data);

            // 返回用户信息（不包含敏感信息）
            return $this->created($user->toApiArray(), '注册成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode(), $e->getErrors());
        } catch (\Exception $e) {
            return $this->error('注册失败：' . $e->getMessage());
        }
    }

    /**
     * 用户登录
     *
     * 用户通过用户名/邮箱和密码进行登录认证，成功后返回访问令牌
     *
     * @route POST /api/v1/auth/login
     * @middleware 无需认证
     * @param string $account 登录账号，支持用户名或邮箱
     * @param string $login_password 登录密码
     * @return Response JSON响应，包含用户信息和访问令牌
     * @throws BusinessException 参数验证失败或账号密码错误
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/auth/login
     * {
     *   "account": "testuser",
     *   "login_password": "123456"
     * }
     * Response: {
     *   "code": 200,
     *   "message": "登录成功",
     *   "data": {
     *     "user": {
     *       "id": 1,
     *       "username": "testuser",
     *       "email": "<EMAIL>",
     *       "nickname": "测试用户"
     *     },
     *     "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
     *     "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
     *     "expires_in": 7200
     *   }
     * }
     */
    public function login(): Response
    {
        try {
            // 获取请求数据
            $data = $this->request->post();

            // 支持两种参数格式
            if (isset($data['email']) && isset($data['password'])) {
                // 简化格式：email + password
                $this->validate($data, AuthValidate::class . '.login_simple');
                $account = $data['email'];
                $password = $data['password'];
            } else {
                // 原始格式：account + login_password
                $this->validate($data, AuthValidate::class . '.login');
                $account = $data['account'];
                $password = $data['login_password'];
            }

            // 获取设备信息
            $deviceInfo = $this->getDeviceInfo();

            // 执行登录
            $result = $this->authService->login($account, $password, $deviceInfo);

            return $this->success($result, '登录成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode(), $e->getErrors());
        } catch (\Exception $e) {
            return $this->error('登录失败：' . $e->getMessage());
        }
    }

    /**
     * 刷新访问令牌
     *
     * 使用刷新令牌获取新的访问令牌，延长用户登录状态
     *
     * @route POST /api/v1/auth/refresh
     * @middleware 无需认证
     * @param string $refresh_token 刷新令牌
     * @return Response JSON响应，包含新的访问令牌和刷新令牌
     * @throws BusinessException 刷新令牌无效或已过期
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/auth/refresh
     * {
     *   "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
     * }
     * Response: {
     *   "code": 200,
     *   "message": "令牌刷新成功",
     *   "data": {
     *     "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
     *     "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
     *     "expires_in": 7200
     *   }
     * }
     */
    public function refresh(): Response
    {
        try {
            // 参数验证
            $data = $this->request->post();
            $this->validate($data, AuthValidate::class . '.refresh');

            // 刷新令牌
            $tokens = $this->authService->refreshToken($data['refresh_token']);

            return $this->success($tokens, '令牌刷新成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode(), $e->getErrors());
        } catch (\Exception $e) {
            return $this->error('令牌刷新失败：' . $e->getMessage());
        }
    }

    /**
     * 用户登出
     *
     * 用户主动登出，使当前或所有设备的令牌失效
     *
     * @route POST /api/v1/auth/logout
     * @middleware auth
     * @param string $refresh_token 刷新令牌，可选
     * @param bool $all_devices 是否登出所有设备，默认false
     * @return Response JSON响应，确认登出成功
     * @throws BusinessException 用户未登录或令牌无效
     * @throws \Exception 系统异常
     * @example
     * POST /api/v1/auth/logout
     * Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
     * {
     *   "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
     *   "all_devices": false
     * }
     * Response: {
     *   "code": 200,
     *   "message": "登出成功",
     *   "data": null
     * }
     */
    public function logout(): Response
    {
        try {
            $userId = $this->request->userId ?? 0;
            if (!$userId) {
                return $this->error('用户未登录', 40101);
            }

            // 获取请求参数
            $data = $this->request->post();
            $refreshToken = $data['refresh_token'] ?? null;
            $allDevices = (bool)($data['all_devices'] ?? false);

            // 执行登出
            $this->authService->logout($userId, $refreshToken, $allDevices);

            return $this->success(null, '登出成功');

        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getErrorCode(), $e->getErrors());
        } catch (\Exception $e) {
            return $this->error('登出失败：' . $e->getMessage());
        }
    }

    /**
     * 获取设备信息
     *
     * 解析用户代理字符串，提取操作系统、浏览器等设备信息
     *
     * @return string JSON格式的设备信息字符串
     * @internal 内部方法，用于登录时记录设备信息
     */
    private function getDeviceInfo(): string
    {
        $userAgent = $this->request->header('User-Agent', '');
        $ip = $this->request->ip();
        
        // 简单解析设备信息
        $deviceInfo = [];
        
        // 检测操作系统
        if (strpos($userAgent, 'Windows') !== false) {
            $deviceInfo['os'] = 'Windows';
        } elseif (strpos($userAgent, 'Mac') !== false) {
            $deviceInfo['os'] = 'macOS';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            $deviceInfo['os'] = 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            $deviceInfo['os'] = 'Android';
        } elseif (strpos($userAgent, 'iOS') !== false) {
            $deviceInfo['os'] = 'iOS';
        } else {
            $deviceInfo['os'] = 'Unknown';
        }

        // 检测浏览器
        if (strpos($userAgent, 'Chrome') !== false) {
            $deviceInfo['browser'] = 'Chrome';
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            $deviceInfo['browser'] = 'Firefox';
        } elseif (strpos($userAgent, 'Safari') !== false) {
            $deviceInfo['browser'] = 'Safari';
        } elseif (strpos($userAgent, 'Edge') !== false) {
            $deviceInfo['browser'] = 'Edge';
        } else {
            $deviceInfo['browser'] = 'Unknown';
        }

        $deviceInfo['ip'] = $ip;
        $deviceInfo['time'] = date('Y-m-d H:i:s');

        return json_encode($deviceInfo, JSON_UNESCAPED_UNICODE);
    }
}
