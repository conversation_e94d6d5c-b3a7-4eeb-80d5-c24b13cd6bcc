<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateBookmarksTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $table = $this->table('bookmarks', [
            'id' => false,
            'primary_key' => ['id'],
            'engine' => 'InnoDB',
            'collation' => 'utf8mb4_unicode_ci',
            'comment' => '书签表'
        ]);

        $table->addColumn('id', 'biginteger', [
            'identity' => true,
            'signed' => false,
            'comment' => '书签ID'
        ])
        ->addColumn('user_id', 'biginteger', [
            'signed' => false,
            'null' => false,
            'comment' => '用户ID'
        ])
        ->addColumn('folder_id', 'biginteger', [
            'signed' => false,
            'null' => true,
            'default' => null,
            'comment' => '所属文件夹ID'
        ])
        ->addColumn('title', 'string', [
            'limit' => 255,
            'null' => false,
            'comment' => '标题'
        ])
        ->addColumn('url', 'string', [
            'limit' => 2000,
            'null' => false,
            'comment' => 'URL地址'
        ])
        ->addColumn('description', 'text', [
            'null' => true,
            'default' => null,
            'comment' => '描述'
        ])
        ->addColumn('favicon', 'string', [
            'limit' => 500,
            'null' => true,
            'default' => null,
            'comment' => '网站图标URL'
        ])
        ->addColumn('sort_order', 'integer', [
            'null' => false,
            'default' => 0,
            'comment' => '排序顺序'
        ])
        ->addColumn('is_star', 'integer', [
            'limit' => 1,
            'null' => false,
            'default' => 0,
            'comment' => '是否星标：0-否，1-是'
        ])
        ->addColumn('visit_count', 'integer', [
            'signed' => false,
            'null' => false,
            'default' => 0,
            'comment' => '访问次数'
        ])
        ->addColumn('last_visit_at', 'timestamp', [
            'null' => true,
            'default' => null,
            'comment' => '最后访问时间'
        ])
        ->addColumn('is_dead', 'integer', [
            'limit' => 1,
            'null' => false,
            'default' => 0,
            'comment' => '是否死链：0-正常，1-死链'
        ])
        ->addColumn('last_check_at', 'timestamp', [
            'null' => true,
            'default' => null,
            'comment' => '最后检测时间'
        ])
        ->addColumn('status_code', 'integer', [
            'null' => true,
            'default' => null,
            'comment' => 'HTTP状态码'
        ])
        ->addColumn('response_time', 'decimal', [
            'precision' => 8,
            'scale' => 2,
            'null' => true,
            'default' => null,
            'comment' => '响应时间（毫秒）'
        ])
        ->addColumn('error_message', 'text', [
            'null' => true,
            'default' => null,
            'comment' => '错误信息'
        ])
        ->addColumn('created_at', 'timestamp', [
            'null' => false,
            'default' => 'CURRENT_TIMESTAMP',
            'comment' => '创建时间'
        ])
        ->addColumn('updated_at', 'timestamp', [
            'null' => false,
            'default' => 'CURRENT_TIMESTAMP',
            'update' => 'CURRENT_TIMESTAMP',
            'comment' => '更新时间'
        ])
        ->addIndex(['user_id'], ['name' => 'idx_user_id'])
        ->addIndex(['folder_id'], ['name' => 'idx_folder_id'])
        ->addIndex(['url'], ['name' => 'idx_url'])
        ->addIndex(['is_star'], ['name' => 'idx_is_star'])
        ->addIndex(['visit_count'], ['name' => 'idx_visit_count'])
        ->addIndex(['created_at'], ['name' => 'idx_created_at'])
        ->addIndex(['sort_order'], ['name' => 'idx_sort_order'])
        ->addIndex(['user_id', 'folder_id'], ['name' => 'idx_user_folder'])
        ->addForeignKey('user_id', 'users', 'id', [
            'delete' => 'CASCADE',
            'constraint' => 'fk_bookmarks_users'
        ])
        ->addForeignKey('folder_id', 'folders', 'id', [
            'delete' => 'SET_NULL',
            'constraint' => 'fk_bookmarks_folders'
        ])
        ->create();
    }
}
