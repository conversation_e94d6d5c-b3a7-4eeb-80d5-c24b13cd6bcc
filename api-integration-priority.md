# 🔌 API接口对接优先级规划

## 📊 接口总览

基于后端已完成的**57个API接口**，按业务重要性和开发依赖关系制定对接优先级。

### 📈 接口分布统计

| 模块 | 接口数量 | 优先级 | 预计对接时间 |
|------|----------|--------|--------------|
| 🔐 用户认证 | 4个 | P0 | Phase 3 |
| 👤 用户管理 | 7个 | P1 | Phase 3-4 |
| 📚 书签管理 | 12个 | P0 | Phase 5 |
| 🗂️ 文件夹管理 | 7个 | P0 | Phase 5 |
| 🏷️ 标签管理 | 6个 | P1 | Phase 5 |
| 🔍 搜索功能 | 2个 | P1 | Phase 6 |
| 🤖 智能文件夹 | 5个 | P2 | Phase 6 |
| 📥 导入导出 | 3个 | P3 | Phase 6 |
| 📊 统计分析 | 4个 | P2 | Phase 6 |
| 🗑️ 回收站 | 5个 | P2 | Phase 6 |
| ⚙️ 系统管理 | 2个 | P3 | Phase 2 |

## 🎯 P0级接口 - 核心功能 (必须优先完成)

### 🔐 用户认证模块 (4个接口)

#### 1. POST /auth/register - 用户注册
```typescript
interface RegisterRequest {
  username: string
  email: string
  password: string
  password_confirmation: string
}
```
**前端对接**: Phase 3.3 注册页面
**依赖**: 无
**验收**: 用户可以成功注册并跳转到登录页

#### 2. POST /auth/login - 用户登录
```typescript
interface LoginRequest {
  account: string        // 邮箱或用户名
  login_password: string
  device_name?: string
}
```
**前端对接**: Phase 3.2 登录页面
**依赖**: 注册接口
**验收**: 用户可以登录并获得Token

#### 3. POST /auth/refresh - 刷新令牌
```typescript
interface RefreshResponse {
  access_token: string
  refresh_token: string
  expires_in: number
}
```
**前端对接**: Phase 3.5 Token管理
**依赖**: 登录接口
**验收**: Token自动刷新，用户无感知

#### 4. POST /auth/logout - 用户登出
**前端对接**: Phase 3.1 认证状态管理
**依赖**: 登录接口
**验收**: 用户可以安全登出，清除本地状态

### 📚 书签管理模块 (12个接口)

#### 5. GET /bookmarks - 获取书签列表
```typescript
interface BookmarkListParams {
  page?: number
  per_page?: number
  folder_id?: number
  tag_ids?: number[]
  is_star?: boolean
  sort?: 'created_at' | 'updated_at' | 'title'
  order?: 'asc' | 'desc'
}
```
**前端对接**: Phase 5.1.1 书签列表组件
**依赖**: 用户认证
**验收**: 显示分页的书签列表，支持筛选和排序

#### 6. POST /bookmarks - 创建书签
```typescript
interface CreateBookmarkRequest {
  title: string
  url: string
  description?: string
  folder_id?: number
  tags?: string[]
  is_star?: boolean
}
```
**前端对接**: Phase 5.1.2 书签表单组件
**依赖**: 文件夹列表、标签列表
**验收**: 用户可以创建书签并选择文件夹和标签

#### 7. GET /bookmarks/{id} - 获取书签详情
**前端对接**: Phase 5.1.4 书签详情页
**依赖**: 书签列表
**验收**: 显示书签完整信息，包括访问统计

#### 8. PUT /bookmarks/{id} - 更新书签
**前端对接**: Phase 5.1.2 书签表单组件
**依赖**: 书签详情
**验收**: 用户可以编辑书签信息

#### 9. DELETE /bookmarks/{id} - 删除书签
**前端对接**: Phase 5.1.3 书签操作菜单
**依赖**: 书签列表
**验收**: 书签被移动到回收站（软删除）

#### 10. POST /bookmarks/batch - 批量操作书签
```typescript
interface BatchBookmarkRequest {
  bookmark_ids: number[]
  action: 'delete' | 'move' | 'star' | 'unstar' | 'add_tags' | 'remove_tags'
  folder_id?: number
  tags?: string[]
}
```
**前端对接**: Phase 5.1.3 书签操作菜单
**依赖**: 书签列表
**验收**: 支持批量删除、移动、加星、添加标签等操作

#### 11. POST /bookmarks/{id}/visit - 访问书签
**前端对接**: Phase 5.1.1 书签列表组件
**依赖**: 书签列表
**验收**: 点击书签时记录访问次数

#### 12. POST /bookmarks/{id}/parse - AI解析书签
**前端对接**: Phase 5.1.2 书签表单组件
**依赖**: 创建书签
**验收**: 自动解析网页标题、描述、关键词

#### 13. POST /bookmarks/{id}/check - 检查书签状态
**前端对接**: Phase 5.1.3 书签操作菜单
**依赖**: 书签列表
**验收**: 检查链接是否有效，标记死链

#### 14. POST /bookmarks/parse - 批量解析书签
**前端对接**: Phase 5.1.3 书签操作菜单
**依赖**: 批量操作
**验收**: 批量解析多个书签的元信息

#### 15. POST /bookmarks/batch-check - 批量检查书签
**前端对接**: Phase 5.1.3 书签操作菜单
**依赖**: 批量操作
**验收**: 批量检查多个书签的有效性

#### 16. GET /bookmarks/dead-link-stats - 死链统计
**前端对接**: Phase 6.2 统计分析模块
**依赖**: 书签检查
**验收**: 显示死链数量和分布统计

### 🗂️ 文件夹管理模块 (7个接口)

#### 17. GET /folders - 获取文件夹列表
```typescript
interface FolderTreeResponse {
  id: number
  name: string
  parent_id: number | null
  children: FolderTreeResponse[]
  bookmark_count: number
}
```
**前端对接**: Phase 5.2.1 文件夹树组件
**依赖**: 用户认证
**验收**: 显示树形结构的文件夹列表

#### 18. POST /folders - 创建文件夹
```typescript
interface CreateFolderRequest {
  name: string
  parent_id?: number
  description?: string
}
```
**前端对接**: Phase 5.2.2 文件夹管理对话框
**依赖**: 文件夹列表
**验收**: 用户可以创建子文件夹

#### 19. GET /folders/{id} - 获取文件夹详情
**前端对接**: Phase 5.2.1 文件夹树组件
**依赖**: 文件夹列表
**验收**: 显示文件夹信息和包含的书签数量

#### 20. PUT /folders/{id} - 更新文件夹
**前端对接**: Phase 5.2.2 文件夹管理对话框
**依赖**: 文件夹详情
**验收**: 用户可以重命名文件夹

#### 21. DELETE /folders/{id} - 删除文件夹
**前端对接**: Phase 5.2.2 文件夹管理对话框
**依赖**: 文件夹列表
**验收**: 删除空文件夹或将书签移动到父文件夹

#### 22. PATCH /folders/{id}/move - 移动文件夹
```typescript
interface MoveFolderRequest {
  parent_id: number | null
  position?: number
}
```
**前端对接**: Phase 5.2.1 文件夹树组件
**依赖**: 文件夹列表
**验收**: 支持拖拽移动文件夹

#### 23. PATCH /folders/sort - 文件夹排序
```typescript
interface SortFoldersRequest {
  folder_orders: Array<{
    id: number
    position: number
  }>
}
```
**前端对接**: Phase 5.2.1 文件夹树组件
**依赖**: 文件夹列表
**验收**: 支持拖拽排序文件夹

## 🥈 P1级接口 - 重要功能 (第二优先级)

### 🏷️ 标签管理模块 (6个接口)

#### 24. GET /tags - 获取标签列表
**前端对接**: Phase 5.3.1 标签管理页面
**优先级**: P1
**预计时间**: Phase 5

#### 25. POST /tags - 创建标签
**前端对接**: Phase 5.3.1 标签管理页面
**优先级**: P1
**预计时间**: Phase 5

#### 26. PUT /tags/{id} - 更新标签
**前端对接**: Phase 5.3.1 标签管理页面
**优先级**: P1
**预计时间**: Phase 5

#### 27. DELETE /tags/{id} - 删除标签
**前端对接**: Phase 5.3.1 标签管理页面
**优先级**: P1
**预计时间**: Phase 5

#### 28. GET /tags/cloud - 标签云
**前端对接**: Phase 5.3.2 标签云组件
**优先级**: P1
**预计时间**: Phase 5

#### 29. POST /tags/merge - 合并标签
**前端对接**: Phase 5.3.1 标签管理页面
**优先级**: P1
**预计时间**: Phase 5

### 👤 用户管理模块 (7个接口)

#### 30-36. 用户信息、设备管理、偏好设置等
**前端对接**: Phase 3.6 用户信息管理
**优先级**: P1
**预计时间**: Phase 3-4

### 🔍 搜索功能模块 (2个接口)

#### 37. GET /search - 全文搜索
#### 38. POST /search/filter - 高级筛选
**前端对接**: Phase 6.1 搜索功能模块
**优先级**: P1
**预计时间**: Phase 6

## 🥉 P2级接口 - 增强功能 (第三优先级)

### 📊 统计分析、🤖 智能文件夹、🗑️ 回收站
**预计时间**: Phase 6
**总计**: 14个接口

## 🔧 P3级接口 - 辅助功能 (最后完成)

### 📥 导入导出、⚙️ 系统管理
**预计时间**: Phase 6-7
**总计**: 5个接口

## 📅 对接时间表

| 阶段 | 接口数量 | 预计时间 | 主要功能 |
|------|----------|----------|----------|
| Phase 2 | 2个 | 0.5小时 | 系统配置 |
| Phase 3 | 11个 | 2小时 | 用户认证和管理 |
| Phase 5 | 25个 | 4小时 | 核心业务功能 |
| Phase 6 | 19个 | 3小时 | 高级功能 |

**总计**: 57个接口，预计9.5小时完成所有API对接工作。
