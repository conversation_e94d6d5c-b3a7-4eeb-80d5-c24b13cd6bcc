<?php

namespace app\service;

use app\model\Folder;
use app\model\Bookmark;
use app\exception\BusinessException;
use think\facade\Db;

/**
 * 文件夹服务类
 */
class FolderService
{
    /**
     * 创建文件夹
     * @param int $userId 用户ID
     * @param array $data 文件夹数据
     * @return Folder
     * @throws BusinessException
     */
    public function createFolder(int $userId, array $data): Folder
    {
        // 类型转换：确保 parent_id 是正确的类型
        $parentId = isset($data['parent_id']) && $data['parent_id'] !== null ? (int)$data['parent_id'] : null;

        // 检查父文件夹是否存在且属于当前用户
        if ($parentId !== null) {
            $parentFolder = Folder::where('id', $parentId)
                ->where('user_id', $userId)
                ->find();

            if (!$parentFolder) {
                throw BusinessException::folderNotFound();
            }

            // 检查层级限制
            if ($parentFolder->level >= Folder::MAX_LEVEL) {
                throw new BusinessException('文件夹层级不能超过' . Folder::MAX_LEVEL . '级');
            }
        }

        // 检查同级文件夹名称是否重复
        if (!Folder::isNameUniqueInLevel($data['name'], $userId, $parentId)) {
            throw BusinessException::folderNameExists();
        }

        // 创建文件夹
        $folder = new Folder();
        $folder->user_id = $userId;
        $folder->parent_id = $parentId;
        $folder->name = $data['name'];
        $folder->icon = $data['icon'] ?? null;
        $folder->color = $data['color'] ?? null;
        $folder->sort_order = Folder::getNextSortOrder($userId, $data['parent_id'] ?? null);

        if (!$folder->save()) {
            throw new BusinessException('创建文件夹失败');
        }

        // 更新路径信息
        $folder->updatePath();

        return $folder;
    }

    /**
     * 更新文件夹
     * @param int $userId 用户ID
     * @param int $folderId 文件夹ID
     * @param array $data 更新数据
     * @return Folder
     * @throws BusinessException
     */
    public function updateFolder(int $userId, int $folderId, array $data): Folder
    {
        $folder = Folder::where('id', $folderId)
            ->where('user_id', $userId)
            ->find();

        if (!$folder) {
            throw BusinessException::folderNotFound();
        }

        // 检查名称是否重复（排除自己）
        if (isset($data['name']) && $data['name'] !== $folder->name) {
            if (!Folder::isNameUniqueInLevel($data['name'], $userId, $folder->parent_id, $folderId)) {
                throw BusinessException::folderNameExists();
            }
        }

        // 更新字段
        $allowedFields = ['name', 'icon', 'color'];
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $folder->$field = $data[$field];
            }
        }

        if (!$folder->save()) {
            throw new BusinessException('更新文件夹失败');
        }

        return $folder;
    }

    /**
     * 删除文件夹
     * @param int $userId 用户ID
     * @param int $folderId 文件夹ID
     * @param bool $force 是否强制删除（包含子文件夹和书签）
     * @return bool
     * @throws BusinessException
     */
    public function deleteFolder(int $userId, int $folderId, bool $force = false): bool
    {
        $folder = Folder::where('id', $folderId)
            ->where('user_id', $userId)
            ->find();

        if (!$folder) {
            throw BusinessException::folderNotFound();
        }

        // 检查是否有子文件夹
        $hasSubfolders = $folder->children()->count() > 0;
        $hasBookmarks = $folder->bookmarks()->count() > 0;

        if (!$force && ($hasSubfolders || $hasBookmarks)) {
            throw new BusinessException('文件夹不为空，无法删除');
        }

        Db::startTrans();
        try {
            if ($force) {
                // 递归删除所有子文件夹和书签
                $this->deleteSubfoldersRecursive($folder);
                
                // 删除文件夹下的所有书签
                Bookmark::where('folder_id', $folderId)->delete();
            }

            // 删除文件夹
            $folder->delete();

            Db::commit();
            return true;

        } catch (\Exception $e) {
            Db::rollback();
            throw new BusinessException('删除文件夹失败：' . $e->getMessage());
        }
    }

    /**
     * 移动文件夹
     * @param int $userId 用户ID
     * @param int $folderId 文件夹ID
     * @param int|null $targetParentId 目标父文件夹ID
     * @return Folder
     * @throws BusinessException
     */
    public function moveFolder(int $userId, int $folderId, ?int $targetParentId): Folder
    {
        $folder = Folder::where('id', $folderId)
            ->where('user_id', $userId)
            ->find();

        if (!$folder) {
            throw BusinessException::folderNotFound();
        }

        // 检查目标父文件夹
        if ($targetParentId) {
            $targetParent = Folder::where('id', $targetParentId)
                ->where('user_id', $userId)
                ->find();
            
            if (!$targetParent) {
                throw BusinessException::folderNotFound();
            }
        }

        // 检查是否可以移动
        if (!$folder->canMoveTo($targetParentId)) {
            throw BusinessException::folderMoveInvalid();
        }

        // 检查目标位置名称是否重复
        if (!Folder::isNameUniqueInLevel($folder->name, $userId, $targetParentId, $folderId)) {
            throw BusinessException::folderNameExists();
        }

        Db::startTrans();
        try {
            // 更新父文件夹
            $folder->parent_id = $targetParentId;
            $folder->sort_order = Folder::getNextSortOrder($userId, $targetParentId);
            $folder->save();

            // 更新路径信息
            $folder->updatePath();
            $folder->updateChildrenPaths();

            Db::commit();
            return $folder;

        } catch (\Exception $e) {
            Db::rollback();
            throw new BusinessException('移动文件夹失败：' . $e->getMessage());
        }
    }

    /**
     * 批量排序文件夹
     * @param int $userId 用户ID
     * @param array $sortData 排序数据 [['id' => 1, 'sort_order' => 1], ...]
     * @return bool
     * @throws BusinessException
     */
    public function sortFolders(int $userId, array $sortData): bool
    {
        if (empty($sortData)) {
            return true;
        }

        Db::startTrans();
        try {
            foreach ($sortData as $item) {
                if (!isset($item['id']) || !isset($item['sort_order'])) {
                    continue;
                }

                Folder::where('id', $item['id'])
                    ->where('user_id', $userId)
                    ->update(['sort_order' => $item['sort_order']]);
            }

            Db::commit();
            return true;

        } catch (\Exception $e) {
            Db::rollback();
            throw new BusinessException('排序失败：' . $e->getMessage());
        }
    }

    /**
     * 获取文件夹树
     * @param int $userId 用户ID
     * @param int|null $parentId 父文件夹ID
     * @param bool $withStats 是否包含统计信息
     * @return array
     */
    public function getFolderTree(int $userId, ?int $parentId = null, bool $withStats = false): array
    {
        return Folder::getFolderTree($userId, $parentId);
    }

    /**
     * 获取文件夹详情
     * @param int $userId 用户ID
     * @param int $folderId 文件夹ID
     * @param bool $withStats 是否包含统计信息
     * @param bool $withChildren 是否包含子文件夹
     * @return array
     * @throws BusinessException
     */
    public function getFolderDetail(int $userId, int $folderId, bool $withStats = true, bool $withChildren = true): array
    {
        // 查找文件夹
        $folder = Folder::where('id', $folderId)
            ->where('user_id', $userId)
            ->find();

        if (!$folder) {
            throw BusinessException::folderNotFound();
        }

        // 转换为API格式（不包含统计信息，后面单独处理）
        $data = $folder->toApiArray(false);

        // 添加子文件夹信息
        if ($withChildren) {
            $children = Folder::where('user_id', $userId)
                ->where('parent_id', $folderId)
                ->order('sort_order', 'asc')
                ->select();

            $data['children'] = $children->map(function ($child) use ($withStats) {
                return $child->toApiArray($withStats);
            })->toArray();
        }

        // 添加面包屑导航
        $data['breadcrumbs'] = $folder->getBreadcrumbs();

        // 添加统计信息
        if ($withStats) {
            $data['stats'] = [
                'bookmark_count' => $folder->getBookmarkCount(),
                'subfolder_count' => $folder->getSubfolderCount(),
                'total_bookmark_count' => $folder->getTotalBookmarkCount(),
            ];
        }

        return $data;
    }

    /**
     * 获取文件夹列表（平铺）
     * @param int $userId 用户ID
     * @param int|null $parentId 父文件夹ID
     * @param bool $withStats 是否包含统计信息
     * @return array
     */
    public function getFolderList(int $userId, ?int $parentId = null, bool $withStats = false): array
    {
        $query = Folder::where('user_id', $userId)
            ->where('parent_id', $parentId)
            ->order('sort_order', 'asc');

        $folders = $query->select();

        return $folders->map(function ($folder) use ($withStats) {
            return $folder->toApiArray($withStats);
        })->toArray();
    }

    /**
     * 获取文件夹面包屑导航
     * @param int $userId 用户ID
     * @param int $folderId 文件夹ID
     * @return array
     * @throws BusinessException
     */
    public function getFolderBreadcrumbs(int $userId, int $folderId): array
    {
        $folder = Folder::where('id', $folderId)
            ->where('user_id', $userId)
            ->find();

        if (!$folder) {
            throw BusinessException::folderNotFound();
        }

        return $folder->getBreadcrumbs();
    }

    /**
     * 递归删除子文件夹
     * @param Folder $folder
     */
    private function deleteSubfoldersRecursive(Folder $folder): void
    {
        $children = $folder->children;
        
        foreach ($children as $child) {
            // 递归删除子文件夹的子文件夹
            $this->deleteSubfoldersRecursive($child);
            
            // 删除子文件夹下的书签
            Bookmark::where('folder_id', $child->id)->delete();
            
            // 删除子文件夹
            $child->delete();
        }
    }
}
